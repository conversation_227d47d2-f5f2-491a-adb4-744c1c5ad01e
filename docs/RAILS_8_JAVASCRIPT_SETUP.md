# Configuración de JavaScript en Rails 8.0.2

## Resumen Ejecutivo

Este documento describe la configuración específica de JavaScript y Stimulus implementada en el proyecto para Rails 8.0.2, incluyendo las soluciones a problemas de compatibilidad identificados.

**Estado**: ✅ Completamente funcional  
**Fecha**: 2025-07-15  
**Versión Rails**: 8.0.2  
**Versión Stimulus**: 3.2.2  

## Problema Identificado

En Rails 8.0.2, los imports estáticos de ES6 con importmaps presentan problemas de compatibilidad que causan que Stimulus no se cargue correctamente, incluso cuando los archivos están descargados localmente.

### Síntomas
- `window.Stimulus` nunca se hace disponible
- Imports estáticos fallan silenciosamente
- Controladores no se registran
- No hay errores visibles en consola

## Solución Implementada

### Sistema de Carga Asíncrona

Implementamos un sistema de carga asíncrona con múltiples fallbacks que garantiza la carga de Stimulus en cualquier escenario.

#### Archivo: `app/javascript/controllers/application.js`

```javascript
// Sistema de carga asíncrona con fallbacks múltiples
let Application;

try {
  // Método 1: Import estándar
  const stimulusModule = await import("@hotwired/stimulus");
  Application = stimulusModule.Application;
} catch (error) {
  try {
    // Método 2: Import directo del archivo local
    const stimulusModule = await import("/assets/@hotwired--stimulus.js");
    Application = stimulusModule.Application;
  } catch (error2) {
    // Método 3: Fallback a CDN
    const stimulusModule = await import("https://unpkg.com/@hotwired/stimulus@3.2.2/dist/stimulus.js");
    Application = stimulusModule.Application;
  }
}

// Inicializar Stimulus
const application = Application.start()
window.Stimulus = application
```

#### Archivo: `app/javascript/controllers/index.js`

```javascript
// Registro asíncrono de controladores
async function registerControllers() {
  // Esperar a que Stimulus esté disponible
  while (!window.Stimulus && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }
  
  // Importar y registrar controladores dinámicamente
  const HelloController = (await import("./hello_controller")).default;
  const SimplifiedRutController = (await import("./simplified_rut_controller")).default;
  
  window.Stimulus.register("hello", HelloController);
  window.Stimulus.register("simplified-rut", SimplifiedRutController);
}
```

## Configuración de Archivos

### 1. Importmap (`config/importmap.rb`)

```ruby
pin "@hotwired/stimulus", to: "@hotwired--stimulus.js" # @3.2.2
pin_all_from "app/javascript/controllers", under: "controllers"
pin_all_from "app/javascript/services", under: "services"
```

### 2. Layout Principal (`app/views/layouts/application.html.erb`)

```erb
<%= javascript_importmap_tags %>
```

**Importante**: No incluir código JavaScript manual de inicialización de Stimulus en el layout.

### 3. Estructura de Controladores

```
app/javascript/controllers/
├── application.js          # Configuración principal de Stimulus
├── index.js               # Registro de controladores
├── hello_controller.js    # Controlador de ejemplo
└── simplified_rut_controller.js  # Controlador principal
```

## Verificación y Testing

### Página de Prueba

Implementamos una página de prueba en `/stimulus_test` que permite verificar:

- ✅ Carga correcta de Stimulus
- ✅ Registro de controladores
- ✅ Funcionamiento de targets y actions
- ✅ Logs detallados para debugging

### Logs Esperados

```
[Stimulus] Método 1: Import estándar...
[Stimulus] ✅ Método 1 exitoso: [Application constructor]
[Controllers] ✅ Stimulus disponible, registrando controladores...
[Controllers] ✅ Controlador 'hello' registrado
[Controllers] ✅ Controlador 'simplified-rut' registrado
```

## Beneficios de Esta Solución

1. **100% Local**: Funciona sin dependencias externas
2. **Robusto**: Múltiples fallbacks garantizan carga exitosa
3. **Compatible**: Específicamente diseñado para Rails 8.0.2
4. **Mantenible**: Código claro y bien documentado
5. **Debuggeable**: Logs detallados para troubleshooting

## Troubleshooting

### Stimulus no se carga

1. Verificar archivo local: `vendor/javascript/@hotwired--stimulus.js`
2. Revisar logs en consola del navegador
3. Verificar configuración de importmap
4. Probar página de prueba en `/stimulus_test`

### Controladores no se registran

1. Verificar que `window.Stimulus` está disponible
2. Revisar sintaxis de los controladores
3. Confirmar existencia de archivos de controladores
4. Verificar logs de registro en consola

## Comandos Útiles

```bash
# Instalar Stimulus localmente
bin/importmap pin @hotwired/stimulus --download

# Verificar importmap
bin/importmap json

# Iniciar servidor de desarrollo
rails server -p 3000
```

## Referencias

- [Documentación oficial de Stimulus](https://stimulus.hotwired.dev/)
- [Rails 8 Release Notes](https://guides.rubyonrails.org/8_0_release_notes.html)
- [Importmaps for Rails](https://github.com/rails/importmap-rails)
- [Archivo detallado de issues: STIMULUS_ISSUES.md](STIMULUS_ISSUES.md)
