# Arquitectura de Consulta Fiscal

Este documento describe la arquitectura implementada para la consulta y gestión de datos fiscales en la aplicación.

## Visión General

Hemos implementado una **arquitectura híbrida** que combina lo mejor del backend y frontend para la gestión de datos fiscales. Esta arquitectura proporciona:

- **Seguridad**: Los tokens de API permanecen en el backend
- **Experiencia de usuario**: Validación en tiempo real en el frontend
- **Mantenibilidad**: Separación clara de responsabilidades
- **Reusabilidad**: Componentes independientes que pueden ser utilizados en diferentes contextos

## Componentes Principales

### Backend

1. **FiscalDataService (Ruby)**
   - Encapsula toda la lógica de comunicación con la API fiscal externa
   - Maneja tokens de autenticación, construcción de URLs y procesamiento de respuestas
   - Proporciona una interfaz limpia para el controlador

2. **SearchController#fiscal_data (Ruby)**
   - Actúa como proxy seguro entre el frontend y la API externa
   - Implementa validación de RUT para seguridad
   - Proporciona un endpoint RESTful para el frontend
   - Maneja errores y logging de forma centralizada

3. **UruguayanRutValidator (Ruby)**
   - Implementa la validación completa del RUT uruguayo en el backend
   - Garantiza la integridad de los datos independientemente del frontend
   - Puede ser utilizado como validador de ActiveModel o de forma independiente

### Frontend

1. **FiscalApiService (JavaScript)**
   - Cliente para el endpoint Rails, no para la API externa directamente
   - Encapsula la lógica de consulta HTTP y manejo de errores
   - Proporciona una interfaz Promise-based para los controladores Stimulus

2. **RutController (Stimulus)**
   - Gestiona la interacción con el formulario fiscal
   - Utiliza FiscalApiService para obtener datos
   - Maneja la actualización del DOM con los datos fiscales

3. **RutValidationController (Stimulus)**
   - Proporciona validación en tiempo real del RUT
   - Mejora la experiencia de usuario sin necesidad de llamadas al servidor
   - Complementa (no reemplaza) la validación del backend

## Flujo de Datos

1. El usuario ingresa un RUT en el formulario
2. El RutValidationController valida el formato en tiempo real (UX)
3. Al consultar, el RutController llama a FiscalApiService
4. FiscalApiService envía la solicitud al endpoint Rails
5. SearchController valida el RUT y llama a FiscalDataService
6. FiscalDataService consulta la API externa usando credenciales seguras
7. La respuesta fluye de vuelta a través de la cadena
8. RutController actualiza el formulario con los datos obtenidos

## Beneficios de esta Arquitectura

- **Separación de Responsabilidades**: Cada componente tiene una única responsabilidad
- **Seguridad**: Credenciales y tokens siempre en el backend
- **Experiencia de Usuario**: Validación inmediata en el frontend + seguridad en backend
- **Reusabilidad**: Servicios independientes utilizables en diferentes contextos
- **Mantenibilidad**: Cambios en la API externa solo afectan a FiscalDataService

## Consideraciones para el Futuro

- Implementar caching de consultas frecuentes en FiscalDataService
- Considerar procesamiento asíncrono para consultas extensas
- Extender la arquitectura para otros tipos de datos externos

## Comandos de Ejemplo

### Consulta de API Externa Directa
```bash
curl -X GET https://www.gestioncfe.minervaconsultora.com/api/consultar-rut/217090160018 -H "Authorization: <token>"
```

### Consulta a Nuestro Endpoint Rails
```bash
curl -X GET http://localhost:3000/owner/search/fiscal_data?rut=217090160018 -H "Accept: application/json" -H "X-Requested-With: XMLHttpRequest"
```

### Consulta desde JavaScript
```javascript
const fiscalService = new FiscalApiService();
fiscalService.fetchFiscalData('217090160018')
  .then(data => {
    console.log('Datos obtenidos:', data);
    // Hacer algo con los datos...
  })
  .catch(error => console.error('Error:', error));
```
