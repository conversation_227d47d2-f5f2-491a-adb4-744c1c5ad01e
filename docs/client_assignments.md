# Asignación Múltiple de Colaboradores a Clientes

## Descripción

Esta funcionalidad permite asignar múltiples colaboradores del equipo a un cliente, lo que facilita la gestión colaborativa de clientes por parte de varios colaboradores de la organización.

## Modelos

### ClientAssignment

Este modelo representa la relación N:N entre clientes y colaboradores del equipo.

```ruby
class ClientAssignment < ApplicationRecord
  # Relaciones
  belongs_to :client, class_name: "User"
  belongs_to :collaborator, class_name: "User"

  # Validaciones
  validates :client_id, presence: true
  validates :collaborator_id, presence: true
  validates :client_id, uniqueness: { scope: :collaborator_id, message: "ya está asignado a este colaborador" }

  # Validar que el cliente tenga rol de cliente
  validate :validate_client_role

  # Validar que el colaborador tenga rol de colaborador
  validate :validate_collaborator_role

  private

  # Validar que el usuario asignado como cliente tenga el rol de cliente
  def validate_client_role
    if client && !client.client?
      errors.add(:client, "debe tener el rol de cliente")
    end
  end

  # Validar que el usuario asignado como colaborador tenga el rol de colaborador
  def validate_collaborator_role
    if collaborator && !collaborator.collaborator?
      errors.add(:collaborator, "debe tener el rol de colaborador")
    end
  end
end
```

### User (Modificaciones)

Se agregaron las siguientes relaciones y métodos al modelo User:

```ruby
# Para clientes: obtener todas las asignaciones y los colaboradores asignados
has_many :client_assignments, foreign_key: "client_id", dependent: :destroy
has_many :assigned_collaborators, through: :client_assignments, source: :collaborator

# Para colaboradores: obtener todas las asignaciones y los clientes asignados
has_many :collaborator_assignments, class_name: "ClientAssignment", foreign_key: "collaborator_id", dependent: :destroy
has_many :assigned_clients_multiple, through: :collaborator_assignments, source: :client

# Verifica si un colaborador tiene asignado a un cliente específico
def has_client_assigned?(client)
  return false unless collaborator? && client&.client?

  # Verificar asignación legacy (assigned_to_id)
  return true if client.assigned_to_id == id

  # Verificar asignación múltiple (ClientAssignment)
  collaborator_assignments.exists?(client_id: client.id)
end

# Verifica si un cliente está asignado a un colaborador específico
def assigned_to_collaborator?(collaborator)
  return false unless client? && collaborator&.collaborator?

  # Verificar asignación legacy (assigned_to_id)
  return true if assigned_to_id == collaborator.id

  # Verificar asignación múltiple (ClientAssignment)
  client_assignments.exists?(collaborator_id: collaborator.id)
end
```

## Controladores

### ClientsController (Modificaciones)

Se modificó el método `update` para manejar las asignaciones múltiples, asegurando que solo se puedan asignar miembros de la organización actual y preservando las asignaciones de miembros de otras organizaciones:

```ruby
def update
  # Verificar que el usuario tenga permisos para editar este cliente
  unless can_edit_client?(@client)
    redirect_to clients_path, alert: "No tienes permisos para editar este cliente."
    return
  end

  # Obtener la organización del cliente usando el método helper
  @client_organization = find_or_create_client_organization(@client)

  # Obtener los miembros seleccionados para asignación múltiple
  member_ids = params[:user][:member_ids].reject(&:blank?) if params[:user][:member_ids]

  ActiveRecord::Base.transaction do
    # Actualizar datos básicos del cliente
    client_updated = @client.update(user_params)
    organization_updated = @client_organization.update(organization_params)

    # Procesar asignaciones múltiples si se proporcionaron member_ids
    if member_ids.present?
      # Obtener la organización activa
      organization = current_organization

      # Verificar que exista una organización activa
      if organization
        # Obtener los miembros válidos (que pertenecen a la organización actual)
        valid_member_ids = organization.members.where(id: member_ids).pluck(:id)

        # Si hay miembros válidos seleccionados
        if valid_member_ids.any?
          # Eliminar asignaciones existentes para este cliente, pero solo de miembros de la organización actual
          # Esto preserva asignaciones de miembros de otras organizaciones si existen
          current_org_member_ids = organization.members.pluck(:id)
          @client.client_assignments.where(member_id: current_org_member_ids).destroy_all

          # Crear nuevas asignaciones para cada miembro seleccionado
          valid_member_ids.each do |member_id|
            @client.client_assignments.create(member_id: member_id)
          end
        end
      end
    else
      # Si no se proporcionaron member_ids, eliminar todas las asignaciones de miembros de la organización actual
      if current_organization
        current_org_member_ids = current_organization.members.pluck(:id)
        @client.client_assignments.where(member_id: current_org_member_ids).destroy_all
      end
    end

    if client_updated && organization_updated
      redirect_to client_path(@client), notice: "Cliente actualizado correctamente."
    else
      render :edit, status: :unprocessable_entity
    end
  end
end
```

## Vistas

### Edición de Cliente

Se modificó la vista de edición de cliente para permitir seleccionar múltiples miembros:

```erb
<div class="sm:col-span-6 mt-6">
  <fieldset>
    <legend class="text-base font-medium text-gray-700">Asignación Múltiple de Miembros</legend>
    <p class="text-sm text-gray-500 mb-4">Selecciona los miembros que pueden gestionar este cliente</p>

    <!-- Botones de selección rápida -->
    <div class="flex space-x-2 mb-4">
      <button type="button" id="select-all-members" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-1 px-2 rounded">
        Seleccionar todos
      </button>
      <button type="button" id="deselect-all-members" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-1 px-2 rounded">
        Deseleccionar todos
      </button>
    </div>

    <div class="mt-4 border-t border-gray-200 pt-4">
      <div class="grid grid-cols-1 gap-y-2 sm:grid-cols-2 lg:grid-cols-3">
        <% current_organization.members.each do |member| %>
          <div class="relative flex items-start">
            <div class="flex items-center h-5">
              <%= check_box_tag 'user[member_ids][]', member.id,
                              @client.assigned_members.include?(member) || @client.assigned_to_id == member.id,
                              id: "member_#{member.id}",
                              class: "member-checkbox focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
            </div>
            <div class="ml-3 text-sm">
              <label for="member_<%= member.id %>" class="font-medium text-gray-700"><%= member.email %></label>
              <p class="text-gray-500"><%= member.name.present? ? member.name : "Sin nombre" %></p>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </fieldset>
</div>
```

### Detalles del Cliente

Se modificó la vista de detalles del cliente para mostrar los miembros asignados, incluyendo información sobre las organizaciones a las que pertenece cada miembro:

```erb
<div class="mb-4">
  <div class="flex justify-between items-center">
    <h3 class="text-sm font-medium text-gray-500">Colaboradores Asignados</h3>
    <% if @client.assigned_collaborators.any? || @client.assigned_to.present? %>
      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
        <%= @client.assigned_members.count + (@client.assigned_to.present? && !@client.assigned_members.include?(@client.assigned_to) ? 1 : 0) %> miembro(s)
      </span>
    <% end %>
  </div>

  <% if @client.assigned_members.any? || @client.assigned_to.present? %>
    <div class="mt-2 border border-gray-200 rounded-md overflow-hidden">
      <ul class="divide-y divide-gray-200">
        <% if @client.assigned_to.present? && !@client.assigned_members.include?(@client.assigned_to) %>
          <li class="px-4 py-2 flex items-center justify-between bg-gray-50">
            <div>
              <span class="text-sm font-medium text-gray-900"><%= @client.assigned_to.email %></span>
              <% if @client.assigned_to.name.present? %>
                <p class="text-xs text-gray-500"><%= @client.assigned_to.name %> <%= @client.assigned_to.last_name %></p>
              <% end %>
              <%
                # Obtener las organizaciones del miembro
                member_orgs = @client.assigned_to.organizations
                if member_orgs.any?
              %>
                <p class="text-xs text-gray-400 mt-1">
                  <% if member_orgs.include?(current_organization) %>
                    <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <%= current_organization.name %>
                    </span>
                  <% end %>
                  <% member_orgs.reject { |org| org == current_organization }.each do |org| %>
                    <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <%= org.name %>
                    </span>
                  <% end %>
                </p>
              <% end %>
            </div>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Principal</span>
          </li>
        <% end %>

        <% @client.assigned_members.each do |member| %>
          <li class="px-4 py-2 flex items-center justify-between">
            <div>
              <span class="text-sm font-medium text-gray-900"><%= member.email %></span>
              <% if member.name.present? %>
                <p class="text-xs text-gray-500"><%= member.name %> <%= member.last_name %></p>
              <% end %>
              <%
                # Obtener las organizaciones del miembro
                member_orgs = member.organizations
                if member_orgs.any?
              %>
                <p class="text-xs text-gray-400 mt-1">
                  <% if member_orgs.include?(current_organization) %>
                    <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <%= current_organization.name %>
                    </span>
                  <% end %>
                  <% member_orgs.reject { |org| org == current_organization }.each do |org| %>
                    <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <%= org.name %>
                    </span>
                  <% end %>
                </p>
              <% end %>
            </div>
            <div class="flex items-center">
              <% if member.id == @client.assigned_to_id %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Principal</span>
              <% end %>
              <% unless member_orgs.include?(current_organization) %>
                <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">Externa</span>
              <% end %>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
  <% else %>
    <p class="mt-1 text-sm text-gray-900 italic">No hay miembros asignados</p>
  <% end %>
</div>
```

## Migración

```ruby
class CreateClientAssignments < ActiveRecord::Migration[8.0]
  def change
    create_table :client_assignments do |t|
      t.references :client, null: false, foreign_key: { to_table: :users }
      t.references :member, null: false, foreign_key: { to_table: :users }

      t.timestamps
    end

    # Agregar índice único para evitar asignaciones duplicadas
    add_index :client_assignments, [:client_id, :member_id], unique: true
  end
end
```

## Datos de Prueba

Se crearon datos de prueba para verificar la funcionalidad:

```ruby
# Crear un administrador
admin = User.create!(
  email: '<EMAIL>',
  name: 'Administrador',
  last_name: 'Test',
  role: :admin,
  password: 'password123'
)

# Crear miembros
members = []
3.times do |i|
  member = User.create!(
    email: "miembro_admin_test#{i+1}@ejemplo.com",
    name: "Miembro Admin #{i+1}",
    last_name: "Test",
    role: :member,
    admin_id: admin.id,
    password: 'password123'
  )
  members << member
end

# Crear clientes
clients = []
3.times do |i|
  client = User.create!(
    email: "cliente_admin_test#{i+1}@ejemplo.com",
    name: "Cliente Admin #{i+1}",
    last_name: "Test",
    role: :client,
    assigned_to_id: members[0].id, # Asignación legacy al primer miembro
    password: 'password123'
  )
  clients << client
end

# Asignar múltiples miembros a los clientes
# Cliente 1: asignado al miembro 1 (ya asignado por assigned_to_id) y miembro 2
ClientAssignment.create(client: clients[0], member: members[1])

# Cliente 2: asignado a todos los miembros
members.each do |member|
  ClientAssignment.create(client: clients[1], member: member)
end

# Cliente 3: asignado a miembro 2 y miembro 3
ClientAssignment.create(client: clients[2], member: members[1])
ClientAssignment.create(client: clients[2], member: members[2])
```

## Uso

1. Inicia sesión como administrador o miembro con permisos para editar clientes.
2. Ve a la página de edición de un cliente.
3. En la sección "Asignación Múltiple de Miembros", selecciona los miembros que deseas asignar al cliente.
4. Guarda los cambios.
5. En la página de detalles del cliente, podrás ver todos los miembros asignados.

## Compatibilidad con el Sistema Legacy

Esta funcionalidad mantiene la compatibilidad con el sistema legacy que usa `assigned_to_id` para asignar un único miembro a un cliente. Los métodos helper como `can_view_client?` y `can_edit_client?` han sido actualizados para considerar tanto la asignación legacy como la nueva asignación múltiple.

## Restricciones de Organización

La funcionalidad ha sido implementada con las siguientes restricciones relacionadas con las organizaciones:

1. **Visibilidad de miembros**: En la vista de edición de clientes, solo se muestran los miembros que pertenecen a la organización actual.

2. **Preservación de asignaciones externas**: Al actualizar las asignaciones de miembros, solo se modifican las asignaciones de miembros de la organización actual. Las asignaciones de miembros de otras organizaciones se preservan.

3. **Identificación de organizaciones**: En la vista de detalles del cliente, se muestra a qué organizaciones pertenece cada miembro asignado, destacando la organización actual y mostrando una etiqueta "Externa" para los miembros que no pertenecen a la organización actual.

4. **Seguridad**: El controlador verifica que los miembros seleccionados pertenezcan a la organización actual antes de crear las asignaciones, evitando posibles problemas de seguridad.
