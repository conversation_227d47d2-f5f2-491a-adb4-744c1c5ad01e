# Documentación de DigiCont

Bienvenido a la documentación de DigiCont, una aplicación B2B de gestión contable que permite a organizaciones administrar sus equipos, clientes y datos de facturación.

## Guías Disponibles

### Guías Generales

- [Guía Completa de Funcionalidades](app_functionality_guide.md) - Visión general de todas las funcionalidades de la aplicación

### Guías Específicas

- [Privacidad entre Organizaciones](privacidad_entre_organizaciones.md) - Explica cómo se implementa el aislamiento de datos entre organizaciones
- [Gestión de Usuarios y Miembros](gestion_usuarios_miembros.md) - Detalla los flujos de trabajo para crear y gestionar usuarios
- [Facturación en Organizaciones](facturacion_organizaciones.md) - Describe cómo se manejan los datos de facturación
- [Selector de Organizaciones](organization_selector.md) - Explica el funcionamiento del selector de organizaciones

### Componentes de UI

- [Componentes](components/README.md) - Documentación sobre los componentes de UI utilizados en la aplicación

### Desarrollo y Configuración Técnica

- [Configuración de JavaScript en Rails 8](RAILS_8_JAVASCRIPT_SETUP.md) - **NUEVO**: Guía técnica para desarrolladores sobre la configuración de Stimulus en Rails 8.0.2
- [Issues de Stimulus - Resolución Completa](STIMULUS_ISSUES.md) - Documentación detallada del problema y solución implementada
- [Arquitectura de Datos Fiscales](FISCAL_DATA_ARCHITECTURE.md) - Documentación sobre el manejo de datos fiscales

### Migraciones y Cambios

- [Migración Completada](migracion_completada.md) - Informe sobre la migración de helpers de botones a clases Tailwind

## Cómo Usar Esta Documentación

1. Comienza con la [Guía Completa de Funcionalidades](app_functionality_guide.md) para obtener una visión general de la aplicación
2. Consulta las guías específicas para obtener información detallada sobre aspectos concretos
3. Revisa la documentación de componentes para entender cómo utilizar correctamente los elementos de UI

## Para Desarrolladores

Si estás desarrollando nuevas funcionalidades o realizando mantenimiento en la aplicación, te recomendamos:

1. **Configuración técnica**: Revisar la [Configuración de JavaScript en Rails 8](RAILS_8_JAVASCRIPT_SETUP.md) para entender la implementación de Stimulus
2. **Arquitectura de datos**: Familiarizarte con la [Arquitectura de Datos Fiscales](FISCAL_DATA_ARCHITECTURE.md)
3. **Privacidad**: Entender el principio de [Privacidad entre Organizaciones](privacidad_entre_organizaciones.md)
4. **Gestión de usuarios**: Comprender la [Gestión de Usuarios y Miembros](gestion_usuarios_miembros.md)
5. **UI/UX**: Seguir las convenciones documentadas en [Componentes](components/README.md)

## Para Soporte Técnico

Si estás proporcionando soporte a usuarios de la aplicación:

1. Comprende cómo funciona el [Selector de Organizaciones](organization_selector.md)
2. Familiarízate con los flujos de trabajo de [Gestión de Usuarios y Miembros](gestion_usuarios_miembros.md)
3. Conoce cómo se manejan los [Datos de Facturación](facturacion_organizaciones.md)

## Contribuir a la Documentación

Si encuentras información desactualizada o quieres añadir nueva documentación:

1. Actualiza los archivos Markdown existentes o crea nuevos según sea necesario
2. Asegúrate de que los nuevos documentos estén referenciados en este README
3. Mantén un estilo consistente con el resto de la documentación
