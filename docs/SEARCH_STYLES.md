# Estilos de Búsqueda

Este documento describe los estilos y patrones para los campos de búsqueda en la aplicación.

## Clases Principales

### `.search-input`
Clase base para todos los campos de búsqueda. Incluye:
- Bordes redondeados
- Transiciones suaves
- Estilos de enfoque consistentes
- Espaciado interno adecuado

### Variantes de Tamaño
- `.search-input-sm`: Versión más pequeña para espacios reducidos
- `.search-input-lg`: Versión más grande para mayor visibilidad

## Uso Recomendado

### Búsqueda Básica
```html
<div class="relative">
  <input type="search" 
         class="search-input" 
         placeholder="Buscar...">
</div>
```

### B<PERSON>queda con Í<PERSON><PERSON>
```html
<div class="relative">
  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
    <i class="ri-search-line text-gray-400"></i>
  </div>
  <input type="search" 
         class="search-input pl-10" 
         placeholder="Buscar...">
</div>
```

### Búsqueda con Botón
```html
<div class="relative flex items-stretch w-full">
  <input type="search" 
         class="search-input rounded-r-none pr-10" 
         placeholder="Buscar...">
  <button type="button" 
          class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 transition duration-150 ease-in-out"
          title="Buscar">
    <i class="ri-search-line"></i>
  </button>
</div>
```

## Accesibilidad
- Siempre usar `type="search"` para mejor semántica
- Incluir `placeholder` descriptivos
- Asegurar suficiente contraste de color
- Implementar etiquetas para lectores de pantalla cuando sea necesario

## JavaScript
Los campos de búsqueda pueden usar el controlador Stimulus `search_controller.js` para funcionalidad de búsqueda en tiempo real.
