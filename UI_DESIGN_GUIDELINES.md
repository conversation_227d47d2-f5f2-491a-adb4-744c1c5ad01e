# Guía de Estilos y Patrones de Diseño UI

Este documento contiene los estándares de diseño y patrones de interfaz de usuario para la aplicación. 

## PRINCIPIOS FUNDAMENTALES

**ANTES DE CREAR O MODIFICAR CUALQUIER ELEMENTO VISUAL:**

1. **OBLIGATORIO: Consultar primero esta guía** para verificar si ya existe un patrón establecido para el componente que necesitas.
   
2. **OBLIGATORIO: Revisar `application.tailwind.css`** para verificar si el elemento visual ya está definido centralmente. TODOS los estilos de la aplicación DEBEN estar definidos en este archivo ÚNICAMENTE. No crear archivos CSS separados.
   
3. **NO DUPLICAR ESTILOS**: Si el elemento existe, DEBES reutilizar las clases existentes.
   
4. **MANTENER CENTRALIZACIÓN**: Cualquier nuevo elemento visual o clase CSS DEBE ser definido en `application.tailwind.css` bajo el layer apropiado. NO crear archivos CSS adicionales bajo ninguna circunstancia.

Recuerda: La duplicación de código visual lleva a inconsistencias. Siempre busca primero, crea solo si es necesario.

## Preferencias Generales del Usuario

1. **Diseño Limpio y Funcional**
   - Mantener una interfaz limpia con el mínimo texto necesario
   - Evitar elementos decorativos innecesarios
   - Priorizar la funcionalidad sobre la estética
   - Usar espaciado consistente entre elementos
   - Mantener una jerarquía visual clara

2. **Formularios**
   - Ocupar todo el ancho disponible del contenedor
   - Agrupar campos relacionados lógicamente
   - Usar validación en tiempo real
   - Mantener etiquetas cerca de sus campos correspondientes
   - Espaciado consistente entre campos (ver sección de Espaciado)

3. **Navegación**
   - Usar pestañas para organizar información relacionada
   - Mantener la navegación consistente en toda la aplicación
   - Proporcionar retroalimentación clara al usuario
   - Resaltar visualmente la pestaña activa

## Estructura de Páginas

### Páginas de Perfil

Las páginas de perfil siguen una estructura consistente con pestañas para organizar la información. Cada sección importante debe estar contenida en una tarjeta con encabezado y acciones claramente definidas.

**Estructura Básica:**
```html
<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- Título de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Título de la Página</h1>
      <p class="mt-1 text-sm text-gray-500">Descripción breve de la sección.</p>
    </div>

    <!-- Pestañas de navegación -->
    <%= render 'shared/tabs' do |tabs| %>
      <% tabs.with_tab name: 'Sección 1', 
                      path: some_path(tab: 'section1'), 
                      active: @tab == 'section1',
                      icon: 'ri-icon-1-line' %>
      
      <% tabs.with_tab name: 'Sección 2', 
                      path: some_path(tab: 'section2'), 
                      active: @tab == 'section2',
                      icon: 'ri-icon-2-line' %>
    <% end %>

    <!-- Contenido de la pestaña actual -->
    <% if @tab == 'section1' %>
      <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
        <!-- Encabezado de la tarjeta -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg font-medium text-gray-900">Título de la Sección</h3>
        </div>
        
        <!-- Contenido -->
        <div class="p-6">
          <!-- Contenido del formulario o información -->
        </div>
      </div>
    <% end %>
  </div>
</div>
```

### Encabezado de Página

```html
<div class="min-h-full bg-gray-50">
  <div class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <!-- Header dentro del contenedor -->
        <div class="px-6 py-5 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-lg font-medium text-gray-900">Título de la Página</h1>
              <p class="mt-1 text-sm text-gray-500">Descripción breve de la sección.</p>
            </div>
          </div>
        </div>
        <!-- Contenido aquí -->
      </div>
    </div>
  </div>
</div>
```

### Tarjetas de Contenido

Las tarjetas se utilizan para agrupar contenido relacionado. Todas las tarjetas deben seguir este patrón:

**Estructura de Tarjeta:**
```html
<div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
  <!-- Encabezado con título y acciones -->
  <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900">Título de la Tarjeta</h3>
      <!-- Acciones opcionales -->
      <div class="flex items-center space-x-2">
        <!-- Botones o acciones -->
      </div>
    </div>
  </div>
  
  <!-- Contenido principal -->
  <div class="p-6">
    <!-- Contenido de la tarjeta -->
  </div>
  
  <!-- Pie de tarjeta (opcional) -->
  <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
    <!-- Acciones o información adicional -->
  </div>
</div>
```

### Pestañas de Navegación

```html
<div class="border-b border-gray-200">
  <nav class="flex justify-center -mb-px" aria-label="Navegación">
    <div class="flex max-w-2xl w-full justify-between">
      <%= link_to path, 
          class: "#{active_condition ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm flex items-center justify-center space-x-2" do %>
        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <span class="whitespace-nowrap">Texto de la Pestaña</span>
      <% end %>
    </div>
  </nav>
</div>
```

## Componentes de Interfaz

### Campo de Búsqueda

Los campos de búsqueda deben seguir un estándar consistente en toda la aplicación para mantener la experiencia de usuario predecible.

#### Estructura HTML

```html
<div class="relative">
  <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
  <input 
    type="search" 
    class="search-input" 
    placeholder="Buscar..."
    data-controller="search"
    data-action="input->search#filter"
  >
  <!-- Botón de limpiar (opcional) -->
  <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-500">
    <i class="ri-close-line"></i>
  </button>
</div>
```

#### Clases CSS

- `.search-input`: Clase principal para el campo de búsqueda (definida en `application.tailwind.css`)
- `.dark .search-input`: Estilos específicos para el modo oscuro (definida en `application.tailwind.css`)

#### Características

- **Altura fija**: 2.75rem (44px)
- **Padding**: Ajustado para el ícono de búsqueda a la izquierda
- **Estados**:
  - **Hover**: Cambio sutil en el color del borde
  - **Focus**: Anillo de enfoque sutil y cambio de color del borde
  - **Disabled**: Opacidad reducida y cursor no permitido
- **Modo oscuro**: Ajustes automáticos para mejor contraste

#### Implementación en Vistas

Para implementar un campo de búsqueda que filtre elementos en tiempo real:

```erb
<div class="mb-6">
  <div class="relative">
    <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
    <input 
      type="search" 
      class="search-input" 
      placeholder="Buscar colaboradores..."
      data-controller="search"
      data-action="input->search#filter"
      data-search-target="input"
    >
  </div>
</div>

<div data-search-target="results">
  <!-- Contenido filtrable -->
  <div data-search-target="item" data-search-text="texto para filtrar">
    <!-- Contenido del elemento -->
  </div>
</div>
```

#### JavaScript (Stimulus)

El controlador Stimulus para la funcionalidad de búsqueda debe implementar:

```javascript
// app/javascript/controllers/search_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "results", "item"]

  connect() {
    // Inicialización si es necesario
  }

  filter() {
    const searchTerm = this.inputTarget.value.toLowerCase()
    
    this.itemTargets.forEach(item => {
      const text = item.dataset.searchText.toLowerCase()
      const isVisible = text.includes(searchTerm)
      item.classList.toggle('hidden', !isVisible)
    })
  }

  clear() {
    this.inputTarget.value = ''
    this.filter()
  }
}
```

## Uso de Colores y Estilos

### Variables CSS
La aplicación utiliza variables CSS definidas en `app/assets/stylesheets/application.tailwind.css`. **Siempre usa estas variables** en lugar de valores de color directos para mantener la consistencia y facilitar futuras actualizaciones de diseño.

### Variables Disponibles:

#### Colores Principales
```css
--color-primary         /* Color primario principal */
--color-primary-light   /* Versión más clara del color primario */
--color-primary-dark    /* Versión más oscura del color primario */
--color-primary-50      /* Fondo muy claro para elementos primarios */
--color-primary-100     /* Fondo claro para hover/estados */
--color-primary-200     /* Bordes y fondos sutiles */
--color-primary-300     /* Elementos interactivos secundarios */
```

#### Colores de Estado
```css
--color-success         /* Operaciones exitosas */
--color-warning        /* Advertencias */
--color-error          /* Errores y acciones destructivas */
--color-info           /* Información */
```

#### Colores de Fondo y Superficie
```css
--color-background     /* Fondo principal de la aplicación */
--color-surface        /* Superficie de tarjetas y contenedores */
--color-navbar         /* Barra de navegación */
--color-navbar-text    /* Texto en la barra de navegación */
--color-navbar-border  /* Borde de la barra de navegación */
```

### Cómo Usar las Variables

En archivos CSS/SCSS:
```css
.boton-primario {
  background-color: var(--color-primary);
  color: white;
}

.boton-primario:hover {
  background-color: var(--color-primary-dark);
}
```

En HTML/ERB con clases de utilidad de Tailwind:
```html
<button class="bg-[var(--color-primary)] hover:bg-[var(--color-primary-dark)] text-white">
  Botón de Acción
</button>
```

### Logo y Marca

El logo de DigiCont se implementa como texto con estilos CSS para mantener la consistencia y la escalabilidad. El diseño incluye:

1. **Texto Principal**: "Digi" en gris oscuro
2. **Acento de Color**: "Cont" en color primario definido por `--color-primary`
3. **Elemento Gráfico**: Un círculo con punto central que acompaña al texto

**Ejemplo de Implementación:**
```html
<div class="flex items-baseline">
  <h1 class="text-2xl font-bold tracking-wider text-gray-900">
    Digi<span class="text-[var(--color-primary)]">Cont</span>
  </h1>
  <div class="ml-2 h-5 w-5 rounded-full bg-[var(--color-primary-100)] flex items-center justify-center self-center">
    <div class="h-3 w-3 rounded-full bg-[var(--color-primary)]"></div>
  </div>
</div>
```

**Directrices de Uso:**
- Mantener la proporción entre el tamaño del texto y el elemento gráfico
- Asegurar suficiente contraste con el fondo
- Usar las variables de color definidas para mantener la consistencia
- El logo siempre debe incluir tanto el elemento gráfico como el texto

## Tipografía
- **Familia Principal**: Inter (cargada a través de `inter-font`)
- **Tamaños de texto**:
  - Títulos: `text-2xl font-bold`
  - Subtítulos: `text-xl font-semibold`
  - Texto normal: `text-base`
  - Texto pequeño: `text-sm`

## Componentes

### Notificaciones

El sistema de notificaciones muestra mensajes temporales al usuario. Se cierran automáticamente después de 5 segundos o manualmente con el botón de cierre.

#### Tipos de notificaciones
- **Éxito** (verde): Para operaciones exitosas
- **Error** (rojo): Para mensajes de error
- **Advertencia** (amarillo): Para advertencias
- **Informativa** (azul): Para información general

#### Uso básico

```erb
<%# En tu layout o vista %>
<%= render 'shared/notifications' %>

<%# Para mostrar una notificación desde Rails %
redirect_to some_path, notice: "Operación exitosa"
redirect_to some_path, alert: "Ocurrió un error"

<%# Para mostrar una notificación desde JavaScript %
showNotification("Mensaje de éxito", "success");
showNotification("Mensaje de error", "error");
showNotification("Mensaje de advertencia", "warning");
showNotification("Mensaje informativo", "info");
```

#### Estructura HTML

```html
<div class="notifications" id="notifications-container">
  <div class="notification notification--success" data-notification>
    <div class="notification__icon">
      <svg>...</svg>
    </div>
    <div class="notification__content">
      <div class="notification__message">Mensaje de éxito</div>
    </div>
    <button type="button" class="notification__close" data-notification-close>
      <svg>...</svg>
    </button>
  </div>
</div>
```

## Componentes de Formulario

### Grupos de Botones

Los botones de acción deben agruparse al final del formulario con un espaciado adecuado.

#### Grupo de Botones Estándar

```html
<div class="pt-6 border-t border-gray-200">
  <div class="flex justify-end space-x-4">
    <!-- Botón Secundario (Cancelar) -->
    <a href="/path/to/cancel" 
       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      Cancelar
    </a>
    
    <!-- Botón Primario (Acción principal) -->
    <button type="submit" 
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      Guardar cambios
    </button>
  </div>
</div>
```

#### Directrices de Uso
- **Espaciado**: Usar `space-x-4` entre botones
- **Orden**: Botón de acción secundaria (Cancelar) a la izquierda, acción primaria a la derecha
- **Alineación**: Contenedor con `justify-end` para alinear a la derecha
- **Separación visual**: Borde superior (`border-t border-gray-200`) y padding superior (`pt-6`) para separar del contenido
- **Iconos**: Usar iconos de Heroicons para acciones principales (ej: check para guardar)

#### Botón Primario
```html
<button class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
  <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
  </svg>
  Texto del botón
</button>
```

#### Botón Secundario
```html
<button class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
  Texto del botón
</button>
```

#### Botón de Enlace
```html
<%= link_to "Volver", path, class: "inline-flex items-center text-indigo-600 hover:text-indigo-800 text-sm font-medium" %>
```

## Formularios en Tarjetas

Los formularios dentro de tarjetas deben seguir estas pautas:

1. **Estructura Básica:**
   ```html
   <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
     <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
       <h3 class="text-lg font-medium text-gray-900">Título del Formulario</h3>
     </div>
     
     <div class="p-6">
       <%= form_with model: @model, url: some_path, class: 'w-full' do |form| %>
         <div class="max-w-3xl mx-auto w-full">
           <!-- Campos del formulario -->
           
           <!-- Acciones del formulario -->
           <div class="mt-8 pt-6 border-t border-gray-200">
             <div class="flex flex-col sm:flex-row justify-between gap-4">
               <div class="flex flex-wrap gap-3">
                 <!-- Botones de acción secundarios -->
               </div>
               <div class="flex-shrink-0">
                 <!-- Botón de acción primario -->
               </div>
             </div>
           </div>
         </div>
       <% end %>
     </div>
   </div>
   ```

2. **Grupos de Campos:**
   - Usar `grid grid-cols-1 md:grid-cols-2 gap-6` para campos en línea
   - Agregar `mb-4` a los contenedores de campos individuales
   - Incluir mensajes de ayuda debajo de los campos cuando sea necesario

3. **Acciones del Formulario:**
   - Botón primario alineado a la derecha
   - Botones secundarios (como Cancelar) agrupados a la izquierda
   - Usar iconos consistentes con la acción

## Flujo de Trabajo para Elementos Visuales

### Principios Generales

1. **Centralización**: Todos los elementos visuales comunes DEBEN estar definidos en `application.tailwind.css`.

2. **Proceso para cada nuevo elemento visual**:
   - **Paso 1**: Revisar esta guía de UI para verificar si existe documentación sobre el elemento.
   - **Paso 2**: Revisar `application.tailwind.css` para verificar si ya existe una clase definida.
   - **Paso 3**: Usar la clase existente si está disponible.
   - **Paso 4**: Si no existe, crear una definición en `application.tailwind.css` y documentar aquí.

3. **Dónde definir nuevos elementos**:
   ```css
   /* En application.tailwind.css */
   @layer components {
     .mi-nuevo-elemento {
       @apply [clases-tailwind] !important;
       /* propiedades adicionales si son necesarias */
     }
   }
   ```

### Catálogo de Elementos Visuales Estandarizados

#### Campos de Formulario

**IMPORTANTE:** Para asegurar la consistencia visual en toda la aplicación, SIEMPRE use las siguientes clases estandarizadas para todos los campos de formulario:

- **Campos de texto**: `form-field-input` (para input type="text", "email", "password", etc.)
- **Áreas de texto**: `form-field-input` (para textarea)
- **Selectores**: `form-field-input` (para select)
- **Etiquetas**: `form-field-label` (para labels)
- **Mensajes de error**: `form-field-input-error` (para campos con errores)

**Ejemplos de uso correcto:**

```erb
<%= label_tag "field_name", "Etiqueta", class: "form-field-label" %>
<%= text_field_tag "field_name", value, class: "form-field-input" %>

<%= f.label :field_name, class: "form-field-label" %>
<%= f.text_field :field_name, class: "form-field-input" %>
```

**⚠️ NO utilice clases utilitarias directamente** para estilos básicos de campos (como bordes, padding, etc.). Estos estilos ya están definidos en la clase `form-field-input` y deben ser consistentes en toda la aplicación.

**Incorrecto:**
```erb
<!-- NO HACER ESTO -->
<%= text_field_tag "field_name", value, class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm" %>
```

**Correcto:**
```erb
<!-- HACER ESTO -->
<%= text_field_tag "field_name", value, class: "form-field-input" %>
```

Esta estandarización asegura que todos los formularios mantengan una apariencia consistente y que los cambios visuales se puedan realizar centralmente modificando la definición de estas clases en `application.tailwind.css`.


### Estructura Básica de Formularios

1. **Contenedor Principal**
   - Usar `form_with` para formularios Rails
   - Asegurar que los formularios ocupen todo el ancho disponible
   - Agrupar campos relacionados lógicamente
   - Usar tarjetas (`shared/card`) para agrupar secciones relacionadas

2. **Campos de Formulario**
   - Usar el partial `shared/form_field` para consistencia
   - Especificar siempre el tipo de campo (`input_type`)
   - Incluir etiquetas descriptivas
   - Usar placeholders cuando sea útil
   - Agregar validaciones del lado del cliente

```erb
<%= form_with model: @model, url: some_path, html: { class: 'w-full' } do |form| %>
  <%= render 'shared/card', title: 'Título del Formulario' do %>
    <div class="space-y-6">
      <%= render 'shared/form_field',
                 form: form,
                 field: :name,
                 label: 'Nombre',
                 required: true,
                 placeholder: 'Ingrese el nombre' %>
    </div>
    
    <!-- Acciones del formulario -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <div class="flex justify-between">
        <div>
          <%= link_to 'Cancelar', back_path, 
                      class: 'btn btn-secondary' %>
        </div>
        <div>
          <%= form.submit 'Guardar Cambios', 
                        class: 'btn btn-primary' %>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
```

### Botones

1. **Estilos de Botones**
   - Usar las clases de botones definidas en `application.tailwind.css`
   - Mantener consistencia en tamaños y espaciados
   - Usar íconos de Remix Icons para acciones comunes

2. **Tipos de Botones**

```erb
<!-- Botón Primario (Acción Principal, Indigo) -->
<button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
  <i class="ri-save-line mr-2"></i> Guardar Cambios
</button>

<!-- Botón Secundario (Blanco/Gris) -->
<%= link_to 'Cancelar', some_path, class: 'inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50' %>

<!-- Botón de Peligro (Rojo) -->
<%= button_to 'Eliminar', some_path, 
              method: :delete, 
              class: 'inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700',
              data: { confirm: '¿Está seguro?' } %>

<!-- Botón con Ícono (Secundario) -->
<button type="button" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
  <i class="ri-add-line"></i> Nuevo
</button>
```

> **Recomendación:** Usa siempre utilidades Tailwind directamente en los botones para máxima consistencia visual. Si necesitas reutilización, define un helper o partial, pero evita clases personalizadas salvo para casos muy específicos.
3. **Ubicación de Botones**
   - Los botones de acción principal (como Guardar) deben estar a la derecha
   - Los botones de navegación (como Cancelar) deben estar a la izquierda
   - Los botones de acción peligrosa (como Eliminar) deben estar en la parte inferior izquierda

### Validaciones y Mensajes

1. **Mensajes de Error**
   - Mostrar mensajes de error debajo del campo correspondiente
   - Usar el componente `shared/alert` para mensajes globales
   - Resaltar visualmente los campos con error

```erb
<% if @model.errors.any? %>
  <%= render 'shared/alert', 
             type: 'error',
             title: "Se encontraron #{@model.errors.count} errores",
             message: @model.errors.full_messages.to_sentence %>
<% end %>
```

2. **Estados de los Campos**
   - Campos requeridos: mostrar un asterisco rojo
   - Campos deshabilitados: usar opacidad reducida
   - Campos de solo lectura: estilo diferenciado

### Patrones Comunes

1. **Formularios en Pestañas**
   - Usar el componente `shared/tabs` para organizar formularios largos
   - Agrupar campos relacionados en pestañas lógicas
   - Incluir una pestaña de "Resumen" cuando sea apropiado

2. **Formularios en Modal**
   - Usar `data: { turbo_frame: "modal" }` para formularios modales
   - Mantener los formularios modales simples y enfocados
   - Incluir botones de acción claros

3. **Subida de Archivos**
   - Usar `direct_upload: true` para cargas de archivos grandes
   - Mostrar vista previa cuando corresponda
   - Incluir información sobre formatos y tamaños aceptados

### Accesibilidad

1. **Etiquetas y Asociaciones**
   - Usar `form.label` para todas las etiquetas
   - Asociar etiquetas con sus campos usando `for`
   - Usar `aria-describedby` para mensajes de ayuda

2. **Navegación por Teclado**
   - Asegurar que todos los controles del formulario sean accesibles por teclado
   - Usar `tabindex` apropiadamente
   - Proporcionar feedback visual para el foco

### Rendimiento

1. **Optimización**
   - Usar `local: true` para formularios que no requieren Turbo
   - Cargar selectores con opciones grandes de forma asíncrona
   - Usar `fetch` para búsquedas en tiempo real

2. **Validación del Lado del Cliente**
   - Usar atributos HTML5 como `required`, `pattern`, etc.
   - Proporcionar mensajes de validación claros
   - Validar en tiempo real cuando sea apropiado

### Ejemplo de Formulario Completo

```erb
<%= form_with model: @model, 
              url: some_path, 
              method: :patch, 
              local: true, 
              data: { turbo: false } do |form| %>
  <div class="space-y-4 max-w-2xl mx-auto">
    <!-- Grupo de campos -->
    <div class="grid grid-cols-1 gap-1">
      <%= form.label :field_name, 'Etiqueta', class: 'block text-sm font-medium text-gray-700' %>
      <%= form.text_field :field_name, 
          class: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm' %>
      <% if @model.errors[:field_name].any? %>
        <p class="mt-1 text-sm text-red-600"><%= @model.errors[:field_name].first %></p>
      <% end %>
    </div>

    <!-- Grupo de campos en línea (2 columnas en pantallas medianas) -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="grid grid-cols-1 gap-1">
        <%= form.label :first_name, 'Nombre', class: 'block text-sm font-medium text-gray-700' %>
        <%= form.text_field :first_name, class: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm' %>
      </div>
      <div class="grid grid-cols-1 gap-1">
        <%= form.label :last_name, 'Apellido', class: 'block text-sm font-medium text-gray-700' %>
        <%= form.text_field :last_name, class: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm' %>
      </div>
    </div>

    <!-- Botones de acción -->
    <div class="pt-4 border-t border-gray-200">
      <div class="flex justify-end space-x-4">
        <%= link_to 'Cancelar', some_path, 
                    class: 'bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500' %>
        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Guardar cambios
        </button>
      </div>
    </div>
  </div>
<% end %>
```

### Directrices para Formularios

1. **Estructura**
   - Usar `space-y-4` para el espaciado vertical entre grupos de campos
   - Agrupar campos relacionados con `grid grid-cols-1 gap-1`
   - Para campos en línea, usar `grid grid-cols-1 md:grid-cols-2 gap-4`
   - Limitar el ancho del formulario con `max-w-2xl mx-auto`

2. **Etiquetas**
   - Siempre usar etiquetas (`label`) para cada campo
   - Clase: `block text-sm font-medium text-gray-700`
   - Sin margen superior, solo `gap-1` en el contenedor padre

3. **Campos de Entrada**
   - **IMPORTANTE**: Siempre usar la clase centralizada `form-input` para todos los campos de texto/email/password
   - **IMPORTANTE**: Siempre usar la clase centralizada `form-select` para todos los campos de selección
   - **IMPORTANTE**: Siempre usar la clase centralizada `form-textarea` para todas las áreas de texto
   
   Estas clases están definidas en `application.tailwind.css` y contienen todos los estilos necesarios:
   ```css
   .form-input,
   .form-select,
   .form-textarea {
     @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm h-10 px-3 py-2;
   }
   
   /* Altura específica para textareas */
   .form-textarea {
     @apply min-h-[100px] h-auto py-2;
   }
   ```
   
   **Ejemplo de uso correcto**:
   ```erb
   <%= form.text_field :name, class: "form-input" %>
   <%= form.select :country, countries, {}, class: "form-select" %>
   <%= form.text_area :description, class: "form-textarea" %>
   ```
   
   **NO usar clases utilitarias directamente para estilos básicos de campos**. Si se necesita modificar la apariencia de los campos, actualizar las definiciones centralizadas en `application.tailwind.css`.

4. **Mensajes de Error**
   - Mostrar debajo del campo con error
   - Clase: `mt-1 text-sm text-red-600`
   - Usar `@model.errors[:field_name].first` para mostrar el mensaje

5. **Botones de Acción**
   - Agrupar en un contenedor con borde superior
   - Alinear a la derecha con `justify-end`
   - Espaciado horizontal entre botones: `space-x-4`
   - Botón primario: `bg-indigo-600 hover:bg-indigo-700 text-white`
   - Botón secundario: `bg-white border-gray-300 text-gray-700 hover:bg-gray-50`

### Ejemplo de Campo con Validación

```erb
<div class="grid grid-cols-1 gap-1">
  <%= form.label :email, 'Correo electrónico', class: 'block text-sm font-medium text-gray-700' %>
  <%= form.email_field :email, 
      class: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm',
      required: true %>
  <% if @user.errors[:email].any? %>
    <p class="mt-1 text-sm text-red-600"><%= @user.errors[:email].first %></p>
  <% end %>
</div>
```

### Contenedores
- **Contenedor principal**: 
  ```html
  <div class="min-h-full bg-gray-50">
    <div class="py-8 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <!-- Contenido aquí -->
      </div>
    </div>
  </div>
  ```

- **Tarjetas y secciones de contenido**:
  - Fondo: `bg-white`
  - Borde redondeado: `sm:rounded-lg`
  - Sombra: `shadow`
  - Padding interno: `px-6 py-6`
  - Borde inferior para encabezados: `border-b border-gray-200`

### Ejemplo de Estructura de Página

```html
<div class="min-h-full bg-gray-50">
  <div class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- Tarjeta principal -->
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <!-- Encabezado -->
        <div class="px-6 py-5 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-lg font-medium text-gray-900">Título de la Página</h1>
              <p class="mt-1 text-sm text-gray-500">Descripción breve de la sección.</p>
            </div>
          </div>
        </div>
        
        <!-- Contenido -->
        <div class="px-6 py-6">
          <!-- Formulario o contenido aquí -->
          <div class="space-y-6">
            <!-- Campos del formulario -->
            <div class="grid grid-cols-1 gap-1">
              <label class="block text-sm font-medium text-gray-700">Etiqueta</label>
              <input type="text" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            </div>
            
            <!-- Botones de acción -->
            <div class="pt-4 border-t border-gray-200">
              <div class="flex justify-end space-x-4">
                <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  Cancelar
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  Guardar cambios
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

### Reglas de Diseño Responsivo

1. **Breakpoints**
   - `sm`: 640px
   - `md`: 768px
   - `lg`: 1024px
   - `xl`: 1280px
   - `2xl`: 1536px

2. **Estrategias Responsivas**
   - Usar `grid-cols-1 md:grid-cols-2` para diseños de dos columnas en pantallas medianas
   - Ajustar el espaciado con `space-y-4 md:space-y-0 md:space-x-4` para cambiar de vertical a horizontal
   - Usar `text-base sm:text-lg` para aumentar el tamaño del texto en pantallas más grandes

3. **Ocultar/Mostrar Elementos**
   - `hidden md:block` para ocultar en móviles y mostrar en pantallas medianas
   - `md:hidden` para mostrar solo en móviles
   - `lg:flex` para cambiar a flex en pantallas grandes

## Patrones de Navegación

## Sistema de Pestañas

Las pestañas se utilizan para organizar contenido relacionado en secciones lógicas. **SIEMPRE usa el sistema centralizado de pestañas** mediante el partial `shared/tabs` y los helpers de `TabsHelper` para mantener la consistencia visual en toda la aplicación.

### Estructura Básica

```erb
<%= render 'shared/tabs' do |tabs| %>
  <% tabs.with_tab name: 'Información General', 
                  path: some_path(section: 'general'), 
                  active: @section == 'general',
                  icon: 'ri-information-line' %>
  
  <% tabs.with_tab name: 'Datos Fiscales', 
                  path: some_path(section: 'fiscal'), 
                  active: @section == 'fiscal',
                  icon: 'ri-bill-line' %>
                  
  <% tabs.with_tab name: 'Datos de Facturación', 
                  path: some_path(section: 'billing'), 
                  active: @section == 'billing',
                  icon: 'ri-bank-card-line' %>
                  
  <% if condition? %>
    <% tabs.with_tab name: 'Pestaña Condicional', 
                    path: some_path(section: 'conditional'), 
                    active: @section == 'conditional',
                    icon: 'ri-lock-line',
                    count: 5 %>
  <% end %>
<% end %>
```

### Parámetros del Helper

El método `tabs.with_tab` acepta los siguientes parámetros:

- **name**: Texto a mostrar en la pestaña (obligatorio)
- **path**: URL destino al hacer clic (obligatorio)
- **active**: Booleano que indica si es la pestaña activa (obligatorio)
- **icon**: Clase CSS del ícono Remix que aparecerá junto al texto (opcional)
- **count**: Número que se mostrará como contador tipo badge (opcional)

### Características del Sistema

- **Consistencia visual**: Todas las pestañas tienen el mismo estilo en toda la aplicación
- **Reutilización**: Evita duplicación de código y estilos
- **Mantenibilidad**: Cambios en el estilo se reflejan en toda la aplicación automáticamente
- **Accesibilidad**: Implementa las mejores prácticas de accesibilidad

### Estados de las Pestañas

- **Activa**: Borde inferior indigo (`border-indigo-500`) y texto indigo (`text-indigo-600`)
- **Inactiva**: Borde transparente y texto gris (`text-gray-500`)
- **Hover**: Texto más oscuro (`hover:text-gray-700`) y borde gris claro (`hover:border-gray-300`)

### Iconos Recomendados (Remix Icons)

- **Información General**: `ri-information-line` o `ri-file-list-line`
- **Datos Fiscales**: `ri-bill-line` o `ri-bank-line`
- **Facturación**: `ri-bank-card-line` o `ri-money-dollar-circle-line`
- **Colaboradores/Usuarios**: `ri-team-line` o `ri-user-line`
- **Configuración**: `ri-settings-line` o `ri-equalizer-line`
- **Acceso**: `ri-lock-unlock-line` o `ri-shield-check-line`

### ⛔️ NO USES IMPLEMENTACIONES ALTERNATIVAS

Siempre utiliza el sistema centralizado de pestañas descrito anteriormente para mantener la consistencia. Evita implementaciones manuales de pestañas con HTML personalizado.

<!-- Contenido de la pestaña activa -->
<div class="p-4 sm:px-6">
  <% if @tab == 'tab1' %>
    <%= render 'tab1_content' %>
  <% elsif @tab == 'tab2' %>
    <%= render 'tab2_content' %>
  <% end %>
</div>
```

**Notas importantes:**
- Usar `text-sm` para el tamaño de fuente de las pestañas
- Mantener un espaciado de `space-x-8` entre pestañas
- Usar transiciones suaves para los cambios de estado

## Reglas Generales
1. **Consistencia**: Mantener los mismos estilos para elementos similares en toda la aplicación.
2. **Jerarquía**: Usar tamaños de texto y pesos de fuente para establecer una jerarquía visual clara.
3. **Espaciado**: 
   - Usar múltiplos de 0.25rem (4px) para mantener la consistencia
   - Mantener un espaciado uniforme entre elementos relacionados
   - Usar `space-y-4` para separar grupos de campos en formularios
4. **Responsive**: 
   - Diseñar pensando en móvil primero
   - Usar las utilidades responsive de Tailwind (ej: `sm:`, `md:`, `lg:`)
   - Asegurar que los formularios sean legibles en todos los tamaños de pantalla
5. **Accesibilidad**: 
   - Mantener un contraste adecuado entre texto y fondo
   - Usar etiquetas semánticas
   - Proporcionar texto alternativo para imágenes
6. **Rendimiento**:
   - Minimizar el uso de estilos personalizados
   - Usar las clases de utilidad de Tailwind en lugar de CSS personalizado cuando sea posible
   - Evitar la duplicación de código

## Componentes Personalizados
Los componentes personalizados deben definirse en `app/assets/stylesheets/application.tailwind.css` usando `@layer components` para mantener la consistencia.

## Convenciones de Nombrado y Código

### Nombrado
- Usar nombres descriptivos para las clases personalizadas
- Seguir la convención de guiones bajos para nombres de archivos de vistas
- Usar snake_case para nombres de variables y métodos en Ruby
- Usar camelCase para JavaScript

### Estructura de Archivos
- Mantener una estructura de carpetas clara y consistente
- Agrupar archivos relacionados (ej: vistas, controladores, estilos)
- Usar nombres descriptivos que reflejen el propósito del archivo

### Código Limpio
- Evitar la duplicación de código
- Extraer componentes reutilizables
- Mantener los archivos de vista lo más simples posible
- Usar partials para fragmentos de código reutilizables
- Documentar código complejo con comentarios claros

### Patrones de Diseño
- Seguir el patrón MVC para la estructura de la aplicación
- Usar partials para componentes reutilizables
- Mantener la lógica de presentación fuera de las vistas cuando sea posible

## Recursos
- [Documentación de Tailwind CSS](https://tailwindcss.com/docs)
- [Paleta de colores de Tailwind](https://tailwindcss.com/docs/customizing-colors)
- [Iconos de Heroicons](https://heroicons.com/)

## Ejemplo de Formulario

```erb
<%= form_with(model: resource, url: path, class: 'space-y-6', data: { turbo: false }) do |form| %>
  <div class="bg-white shadow sm:rounded-lg overflow-hidden">
    <div class="md:grid md:grid-cols-3 md:gap-4">
      <div class="md:col-span-1 px-4 pt-4 sm:px-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Título del Formulario</h3>
      </div>
      <div class="px-4 py-4 sm:p-6 md:col-span-2">
        <div class="space-y-4">
          <!-- Campo de ejemplo -->
          <div>
            <%= form.label :field_name, "Etiqueta", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.text_field :field_name, 
                  class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
            </div>
          </div>
        </div>
        
        <!-- Acciones del formulario -->
        <div class="pt-5">
          <div class="flex justify-end">
            <%= link_to "Cancelar", cancel_path, 
                class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Guardar Cambios
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>
```

---
*Última actualización: 28 de mayo de 2025*
