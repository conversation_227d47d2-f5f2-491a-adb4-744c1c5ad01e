class AddFiscalFieldsToOrganizations < ActiveRecord::Migration[8.0]
  def change
    add_column :organizations, :email, :string, null: true, comment: 'Correo electrónico de contacto fiscal'
    add_column :organizations, :nombre_legal, :string, null: true, comment: 'Nombre legal completo de la organización'
    add_column :organizations, :actividades, :json, null: false, default: [], comment: 'Actividades económicas de la organización'
    
    # Agregar índice para búsquedas por email
    add_index :organizations, :email, unique: true
  end
end
