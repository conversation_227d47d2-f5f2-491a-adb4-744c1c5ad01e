# frozen_string_literal: true

class RenameMemberToCollaboratorInClientAssignments < ActiveRecord::Migration[8.0]
  def up
    # Get the foreign key name for member_id
    fk_name = foreign_key_name(:client_assignments, :member_id)
    
    # Remove the foreign key constraint if it exists
    if fk_name
      remove_foreign_key :client_assignments, column: :member_id, name: fk_name
    end
    
    # Rename the column
    rename_column :client_assignments, :member_id, :collaborator_id
    
    # Recreate the foreign key with the new column name
    add_foreign_key :client_assignments, :users, column: :collaborator_id
    
    # Rename the indexes if they exist
    if index_exists?(:client_assignments, [:client_id, :member_id], name: 'index_client_assignments_on_client_id_and_member_id')
      rename_index :client_assignments, 'index_client_assignments_on_client_id_and_member_id', 
                   'index_client_assignments_on_client_id_and_collaborator_id'
    end
    
    if index_exists?(:client_assignments, :member_id, name: 'index_client_assignments_on_member_id')
      rename_index :client_assignments, 'index_client_assignments_on_member_id', 
                   'index_client_assignments_on_collaborator_id'
    end
  end
  
  def down
    # Get the foreign key name for collaborator_id
    fk_name = foreign_key_name(:client_assignments, :collaborator_id)
    
    # Remove the foreign key constraint if it exists
    if fk_name
      remove_foreign_key :client_assignments, column: :collaborator_id, name: fk_name
    end
    
    # Rename the column back
    rename_column :client_assignments, :collaborator_id, :member_id
    
    # Recreate the foreign key with the original column name
    add_foreign_key :client_assignments, :users, column: :member_id
    
    # Rename the indexes back if they were changed
    if index_exists?(:client_assignments, [:client_id, :member_id], name: 'index_client_assignments_on_client_id_and_collaborator_id')
      rename_index :client_assignments, 'index_client_assignments_on_client_id_and_collaborator_id', 
                   'index_client_assignments_on_client_id_and_member_id'
    end
    
    if index_exists?(:client_assignments, :member_id, name: 'index_client_assignments_on_collaborator_id')
      rename_index :client_assignments, 'index_client_assignments_on_collaborator_id', 
                   'index_client_assignments_on_member_id'
    end
  end
  
  private
  
  # Helper method to get the foreign key name for a column
  def foreign_key_name(table, column)
    foreign_keys = ActiveRecord::Base.connection.foreign_keys(table.to_s)
    fk = foreign_keys.find { |fk| fk.column == column.to_s }
    fk&.name
  end
end
