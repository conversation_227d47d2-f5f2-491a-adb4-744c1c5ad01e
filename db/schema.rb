# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_10_234344) do
  create_table "client_assignments", force: :cascade do |t|
    t.integer "client_id", null: false
    t.integer "collaborator_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id", "collaborator_id"], name: "index_client_assignments_on_client_id_and_collaborator_id", unique: true
    t.index ["client_id"], name: "index_client_assignments_on_client_id"
    t.index ["collaborator_id"], name: "index_client_assignments_on_collaborator_id"
  end

  create_table "organization_users", force: :cascade do |t|
    t.integer "organization_id", null: false
    t.integer "user_id", null: false
    t.integer "role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "access_enabled", default: false, null: false
    t.datetime "invitation_sent_at"
    t.index ["organization_id", "user_id"], name: "index_organization_users_on_organization_id_and_user_id", unique: true
    t.index ["organization_id"], name: "index_organization_users_on_organization_id"
    t.index ["role", "access_enabled"], name: "index_organization_users_on_role_and_access_enabled"
    t.index ["user_id"], name: "index_organization_users_on_user_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "rut"
    t.string "nombre_fantasia"
    t.string "tipo_entidad"
    t.string "direccion"
    t.string "telefono"
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email_facturacion"
    t.boolean "client_access_enabled", default: false, null: false
    t.datetime "deleted_at"
    t.integer "deleted_by_id"
    t.integer "status", default: 0, null: false
    t.string "email"
    t.string "nombre_legal"
    t.json "actividades", default: [], null: false
    t.string "ciudad"
    t.string "departamento"
    t.string "estado"
    t.date "fecha_inicio_actividades"
    t.index ["deleted_at"], name: "index_organizations_on_deleted_at"
    t.index ["deleted_by_id"], name: "index_organizations_on_deleted_by_id"
    t.index ["email"], name: "index_organizations_on_email", unique: true
    t.index ["rut"], name: "index_organizations_on_rut", unique: true
    t.index ["status"], name: "index_organizations_on_status"
  end

  create_table "permissions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.text "description"
    t.json "metadata", default: "{}"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "name"], name: "index_permissions_on_user_id_and_name", unique: true
    t.index ["user_id"], name: "index_permissions_on_user_id"
  end

  create_table "system_configurations", force: :cascade do |t|
    t.string "key"
    t.string "value"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "system_settings", force: :cascade do |t|
    t.string "key"
    t.text "value"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "user_permission_overrides", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "permission_key"
    t.boolean "enabled"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_permission_overrides_on_user_id"
  end

  create_table "user_role_backups", force: :cascade do |t|
    t.integer "user_id"
    t.integer "old_role"
    t.integer "new_role"
    t.datetime "created_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }
  end

  create_table "users", force: :cascade do |t|
    t.string "email"
    t.string "password_digest"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "role", default: 3, null: false
    t.string "name"
    t.integer "admin_id"
    t.integer "assigned_to_id"
    t.string "last_name"
    t.string "phone"
    t.index ["admin_id"], name: "index_users_on_admin_id"
    t.index ["assigned_to_id"], name: "index_users_on_assigned_to_id"
    t.index ["email"], name: "index_users_on_email", unique: true
  end

# Could not dump table "users_backup" because of following StandardError
#   Unknown type 'NUM' for column 'created_at'


  add_foreign_key "client_assignments", "users", column: "client_id"
  add_foreign_key "client_assignments", "users", column: "collaborator_id"
  add_foreign_key "client_assignments", "users", column: "collaborator_id"
  add_foreign_key "organization_users", "organizations"
  add_foreign_key "organization_users", "users"
  add_foreign_key "permissions", "users"
  add_foreign_key "user_permission_overrides", "users"
  add_foreign_key "users", "users", column: "admin_id"
  add_foreign_key "users", "users", column: "assigned_to_id"
end
