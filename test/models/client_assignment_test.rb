require "test_helper"

class ClientAssignmentTest < ActiveSupport::TestCase
  self.use_transactional_tests = true

  def setup_organization
    @organization ||= Organization.create!(
      name: 'Test Organization',
      rut: '123456780012',  # RUT válido para organización (12 dígitos, primeros dos entre 1-22, dígitos 9-10 = 00)
      nombre_fantasia: 'Test Organization',
      tipo_entidad: 'Empresa',
      direccion: 'Test Address',
      telefono: '+56912345678',
      email_facturacion: '<EMAIL>',
      client_access_enabled: true,
      status: 0  # active status as integer
    )
  end

  def setup_admin
    @admin ||= User.create!(
      email: "test_admin_#{SecureRandom.hex(4)}@example.com",
      password: 'Password123',  # Must contain uppercase, lowercase, and number
      name: 'Test',
      last_name: 'Admin',
      role: :owner
    )
  end

  def setup_collaborator
    @collaborator ||= User.create!(
      email: "test_collaborator_#{SecureRandom.hex(4)}@example.com",
      password: 'Password123',  # Must contain uppercase, lowercase, and number
      name: 'Test',
      last_name: 'Collaborator',
      role: :collaborator,
      admin_id: setup_admin.id  # Relationship with admin
    )
  end

  def setup_client
    @client ||= User.create!(
      email: "test_client_#{SecureRandom.hex(4)}@example.com",
      password: 'Password123',  # Must contain uppercase, lowercase, and number
      name: 'Test',
      last_name: 'Client',
      role: :client,
      assigned_to_id: setup_collaborator.id  # Assign to collaborator
    )
  end

  def setup_organization_users
    setup_organization
    setup_admin
    setup_collaborator
    setup_client

    # Create organization relationships
    @admin_org_user = @admin.organization_users.create!(
      organization: @organization,
      role: :owner,
      access_enabled: false  # Admin doesn't need access_enabled
    )

    @collaborator_org_user = @collaborator.organization_users.create!(
      organization: @organization,
      role: :collaborator,
      access_enabled: false  # Collaborators don't need access_enabled
    )

    @client_org_user = @client.organization_users.create!(
      organization: @organization,
      role: :client,
      access_enabled: true  # Only clients can have access_enabled
    )
  end

  setup do
    setup_organization_users
    
    # Clear any existing assignments
    ClientAssignment.destroy_all
    
    # Don't create an assignment by default - let each test create what it needs
    @client_assignment = ClientAssignment.new(
      client: @client,
      collaborator: @collaborator
    )
  end

  test "valid with different client and same collaborator" do
    # Create a new client for this test only
    another_client = User.create!(
      email: "another_client_#{SecureRandom.hex(4)}@example.com",
      password: 'Password123',  # Must contain uppercase, lowercase, and number
      name: 'Another',
      last_name: 'Client',
      role: :client,
      assigned_to_id: setup_collaborator.id
    )
    
    # Create organization relationship for the new client
    another_client.organization_users.create!(
      organization: @organization,
      role: :client,
      access_enabled: true
    )
    
    # Should be valid since it's a different client with the same collaborator
    another_assignment = ClientAssignment.new(
      client: another_client,
      collaborator: @collaborator
    )
    assert another_assignment.valid?, another_assignment.errors.full_messages.join(", ")
  end

  test "valid client assignment" do
    # Create the assignment for this test only
    assignment = ClientAssignment.new(
      client: @client,
      collaborator: @collaborator
    )
    assert assignment.valid?, assignment.errors.full_messages.join(", ")
  end

  test "invalid without client" do
    @client_assignment.client = nil
    assert_not @client_assignment.valid?
    assert_includes @client_assignment.errors[:client_id], "no puede estar en blanco"
  end

  test "invalid without collaborator" do
    @client_assignment.collaborator = nil
    assert_not @client_assignment.valid?
    assert_includes @client_assignment.errors[:collaborator_id], "no puede estar en blanco"
  end

  test "invalid with duplicate assignment" do
    # Create the first assignment
    ClientAssignment.create!(
      client: @client,
      collaborator: @collaborator
    )
    
    # Try to create a duplicate
    assert_no_difference('ClientAssignment.count') do
      duplicate = ClientAssignment.new(
        client: @client,
        collaborator: @collaborator
      )
      assert_not duplicate.valid?
      assert_includes duplicate.errors[:client_id], I18n.t('errors.messages.already_assigned')
    end
  end

  test "invalid with non-client user" do
    invalid_assignment = ClientAssignment.new(client: @admin, collaborator: @collaborator)
    assert_not invalid_assignment.valid?
    assert_includes invalid_assignment.errors[:client], I18n.t('errors.messages.must_be_client')
  end

  test "invalid with non-collaborator user" do
    invalid_assignment = ClientAssignment.new(client: @client, collaborator: @admin)
    assert_not invalid_assignment.valid?
    assert_includes invalid_assignment.errors[:collaborator], I18n.t('errors.messages.must_be_collaborator')
  end
end
