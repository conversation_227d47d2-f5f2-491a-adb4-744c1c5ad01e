Rails.application.routes.draw do
  # Rutas de prueba y debug (accesibles sin autenticación)
  get 'stimulus_test', to: 'pages#stimulus_test', as: :stimulus_test
  get 'debug_js', to: 'pages#debug_js', as: :debug_js
  
  # Ruta del dashboard
  get 'dashboard', to: 'dashboard#show', as: :dashboard
  
  # Rutas principales y públicas
  namespace :main do
    resource :registration
    resource :session
    resource :password_reset
    resource :password
    root to: "main#index"
  end

  # Rutas para superadministradores
  namespace :superadmin do
    get "/", to: "admin_panel#index", as: :root
    get "system_settings", to: "admin_panel#system_settings"
    patch "update_system_settings", to: "admin_panel#update_system_settings"
    get "user_permissions", to: "admin_panel#user_permissions"
    get "documentation", to: "admin_panel#documentation"

    resources :system_settings, only: [:index, :update] do
      collection do
        post :test_rut_api
      end
    end

    resources :organizations do
      collection do
        get :deleted
        get :inactive
      end
      member do
        post :restore
        post :activate
        post :deactivate
      end
    end

    # Rutas para excepciones de permisos
    post "user_permission_overrides/:user_id/:permission_key", to: "user_permission_overrides#create", as: :create_user_permission_override
    delete "user_permission_overrides/:user_id/:permission_key", to: "user_permission_overrides#destroy", as: :destroy_user_permission_override

    # Rutas para impersonación
    resources :impersonations, only: [:index, :create]
    get 'stop_impersonation', to: 'stop_impersonation#destroy', as: :stop_impersonation
  end
  # Rutas para propietarios de organizaciones
  namespace :owner do
    # Dashboard como página principal del owner
    get "/", to: "dashboard#index", as: :root
    
    # Add profile resource for owners
    resource :profile, only: [:show, :update]
    
    # Búsqueda
    get 'search/clients', to: 'search#clients', as: :search_clients
    get 'search/fiscal_data', to: 'search#fiscal_data', as: :search_fiscal_data
    
    resources :organizations do
      resources :collaborators, controller: 'organization_users', only: [:index, :new, :create, :edit, :update, :destroy] do
        member do
          get :confirm_destroy
        end
      end
      member do
        post 'switch', to: 'organizations#switch', as: :switch_organization
        post 'toggle_client_access'
        get 'confirm_destroy'
      end
    end

    resources :users, only: [:index, :show, :new, :create, :edit, :update, :destroy] do
      resources :permissions, only: [:index, :create, :destroy]
    end
    
    resources :clients do
      member do
        post 'toggle_access'
        get 'confirm_destroy'
        get 'validate_fiscal_data', to: 'clients#validate_fiscal_data', as: 'validate_fiscal_data'
        post 'update_fiscal_data', to: 'clients#update_fiscal_data', as: 'update_fiscal_data'
        patch 'update_organization_fiscal_data', to: 'clients#update_organization_fiscal_data', as: 'update_organization_fiscal_data'
      end
      collection do
        post 'validate_rut'
        post 'validate_rut_ajax'
        post 'fetch_fiscal_data', to: 'clients#fetch_fiscal_data', as: 'fetch_fiscal_data'
        get 'export', to: 'clients#export', as: 'export', defaults: { format: :csv }
      end
    end
  end

  # Rutas para colaboradores
  namespace :collaborator do
    get "/", to: "pages#home", as: :root
    get "home", to: "pages#home"
    
    # Búsqueda fiscal (para compatibilidad con el controlador Stimulus)
    get 'search/fiscal_data', to: 'search#fiscal_data', as: :search_fiscal_data

    resources :clients do
      member do
        post 'toggle_access'
        post 'send_invitation'
        get 'confirm_destroy'
        patch 'set_active', to: 'clients#set_active'
      end
      collection do
        post 'validate_rut'
        post 'validate_rut_ajax'
        post 'create_basic'
        get 'validate_fiscal_data/:id', to: 'clients#validate_fiscal_data', as: 'validate_fiscal_data'
        post 'update_fiscal_data/:id', to: 'clients#update_fiscal_data', as: 'update_fiscal_data'
        post 'fetch_fiscal_data', to: 'clients#fetch_fiscal_data', as: 'fetch_fiscal_data'
      end
    end
  end

  # Rutas para clientes
  namespace :client do
    # Dashboard como página principal del cliente
    get "/", to: "profile#show", as: :root
    resource :profile, only: [:show, :update]
  end

  # Defines the root path route ("/")
  root 'home#index'
  get 'home', to: 'home#index', as: :home

  # Rutas de compatibilidad para redirecciones
  get 'admin_panel', to: redirect('/superadmin')
  get 'organizations', to: redirect { |_, request|
    if request.env['warden'].user&.owner?
      '/owner/organizations'
    else
      '/collaborator/organizations'
    end
  }
  get 'clients', to: redirect { |_, request|
    if request.env['warden'].user&.owner?
      '/owner/clients'
    else
      '/collaborator/clients'
    end
  }
  # Role-aware profile redirect
  get 'profile', to: redirect { |_, request|
    if request.env['warden'].user&.owner?
      '/owner/profile'
    elsif request.env['warden'].user&.collaborator?
      '/collaborator/profile'
    else
      '/client/profile'
    end
  }
end