es:
  activerecord:
    errors:
      messages:
        inclusion: "no está incluido en la lista"
        exclusion: "está reservado"
        invalid: "no es válido"
        confirmation: "no coincide con %{attribute}"
        accepted: "debe ser aceptado"
        empty: "no puede estar vacío"
        blank: "no puede estar en blanco"
        too_long:
          one: "es demasiado largo (máximo 1 caracter)"
          other: "es demasiado largo (máximo %{count} caracteres)"
        too_short:
          one: "es demasiado corto (mínimo 1 caracter)"
          other: "es demasiado corto (mínimo %{count} caracteres)"
        wrong_length:
          one: "no tiene la longitud correcta (debe ser 1 caracter)"
          other: "no tiene la longitud correcta (debe ser %{count} caracteres)"
        taken: "ya está en uso"
        not_a_number: "no es un número"
        greater_than: "debe ser mayor que %{count}"
        greater_than_or_equal_to: "debe ser mayor o igual que %{count}"
        equal_to: "debe ser igual a %{count}"
        less_than: "debe ser menor que %{count}"
        less_than_or_equal_to: "debe ser menor o igual que %{count}"
        odd: "debe ser impar"
        even: "debe ser par"
        record_invalid: "La validación falló: %{errors}"
    attributes:
      user:
        password_confirmation: "Confirmación de Contraseña"
        password: "Contraseña"
        current_password: "Contraseña Actual"
      client_assignment:
        client: "cliente"
        collaborator: "colaborador"
  hello: "¡Hola mundo!"
  organization_user:
    roles:
      admin: "Administrador"
      member: "Miembro"
      client: "Cliente"
  
  # Custom error messages
  errors:
    messages:
      must_be_client: "debe tener el rol de cliente"
      must_be_collaborator: "debe tener el rol de colaborador"
      already_assigned: "ya está asignado a este colaborador"
