module ApplicationHelper
  # Renders a Remix Icon with optional classes and title for accessibility
  # @param name [String] The name of the Remix Icon (e.g., 'ri-eye-line')
  # @param options [Hash] Additional options
  # @option options [String] :class Additional CSS classes
  # @option options [String] :title Title for accessibility (will be added as sr-only text if provided)
  # @return [String] HTML for the icon
  
  # Limpia un RUT eliminando puntos y guiones
  # @param rut [String, Integer] El RUT a limpiar
  # @return [String] El RUT limpio (solo números y letra K)
  def format_rut(rut, formatted: false)
    return '' if rut.blank?
    
    # Convertir a string y eliminar cualquier formato existente
    rut = rut.to_s.gsub(/[^0-9kK]/, '').upcase
    
    # Si se solicita el formato con puntos y guión
    if formatted && rut.length > 1
      dv = rut[-1]
      numbers = rut[0..-2].reverse.scan(/.{1,3}/).join('.').reverse
      "#{numbers}-#{dv}"
    else
      rut
    end
  end
  
  # Helper methods available across all views
end
