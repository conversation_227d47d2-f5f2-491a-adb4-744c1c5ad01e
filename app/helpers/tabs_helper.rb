# frozen_string_literal: true

# Module for handling navigation tabs with Tailwind CSS
# This helper provides a clean way to create tabbed navigation that matches the TailAdmin style
# and follows the application's design system.
module TabsHelper
  # Helper class for building tab navigation components
  class TabsBuilder
    def initialize(view_context, options = {})
      @view_context = view_context
      @tabs = []
      @options = options
    end
    
    # Add a new tab to the navigation
    # @param name [String] The display text for the tab
    # @param path [String, Hash] The URL or path helper for the tab
    # @param active [Boolean] Whether this tab is currently active
    # @param icon [String] Optional icon class (e.g., 'fas fa-home')
    # @param count [Integer, String] Optional count to display as a badge
    # @param data [Hash] Optional data attributes for the tab
    def with_tab(name:, path:, active:, icon: nil, count: nil, data: {})
      @tabs << @view_context.tab_link(
        name: name,
        path: path,
        active: active,
        icon: icon,
        count: count,
        data: data
      )
      nil
    end
    
    def to_s
      @view_context.safe_join(@tabs)
    end
  end
  
  # Generate a tab link with appropriate styling
  # @param name [String] The display text for the tab
  # @param path [String, Hash] The URL or path helper for the tab
  # @param active [Boolean] Whether this tab is currently active
  # @param icon [String] Optional icon class (e.g., 'fas fa-home')
  # @param count [Integer, String] Optional count to display as a badge
  # @param data [Hash] Optional data attributes for the tab
  # @return [String] HTML for the tab link
  def tab_link(name:, path:, active:, icon: nil, count: nil, data: {})
    base_classes = [
      'inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm leading-5',
      'focus:outline-none transition duration-150 ease-in-out',
      active ? 'border-indigo-500 text-indigo-600 focus:text-indigo-800 focus:border-indigo-700' : 
              'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:text-gray-700 focus:border-gray-300'
    ].join(' ')
    
    content_tag(:div, class: 'flex') do
      link_to(path, class: base_classes, data: data) do
        parts = []
        
        # Add icon if provided
        if icon.present?
          icon_class = "#{icon} #{active ? 'text-indigo-500' : 'text-gray-400'} mr-2"
          parts << content_tag(:i, '', class: icon_class, 'aria-hidden': 'true')
        end
        
        # Add tab text
        parts << name
        
        # Add count badge if provided
        if count.present?
          count_classes = [
            'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            active ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800'
          ].join(' ')
          parts << content_tag(:span, count, class: count_classes)
        end
        
        safe_join(parts)
      end
    end
  end
  
  # Renders a tabbed navigation component
  # @yield [TabsBuilder] The tabs builder object for defining tabs
  # @return [String] HTML for the tab navigation
  def render_tabs
    builder = TabsBuilder.new(self)
    yield(builder) if block_given?
    
    content_tag :div, class: 'w-full border-b border-gray-200' do
      content_tag :div, class: 'max-w-full overflow-x-auto' do
        content_tag :nav, class: 'flex', role: 'tablist', 'aria-label': t('common.navigation') do
          content_tag :div, class: 'flex space-x-1' do
            builder.to_s.html_safe
          end
        end
      end
    end
  end
end
