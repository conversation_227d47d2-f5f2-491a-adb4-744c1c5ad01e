# frozen_string_literal: true

module CardHelper
  # Renderiza un contenedor de tarjeta con contenido
  #
  # @example Uso básico con bloque
  #   = card title: 'Mi Tarjeta' do
  #     p Contenido de la tarjeta
  #
  # @example Con subtítulo y acción en el encabezado
  #   = card title: 'Mi Tarjeta', 
  #          subtitle: 'Descripción opcional',
  #          header_action: link_to('Editar', '#', class: 'btn btn-primary')
  #
  # @param title [String, nil] Título opcional para la tarjeta
  # @param subtitle [String, nil] Subtítulo opcional
  # @param full_width [Boolean] Si es true, la tarjeta ocupa todo el ancho disponible
  # @param padding [String] Clases de padding (por defecto: 'p-6')
  # @param header_action [String, nil] HTML para acciones en el encabezado
  # @param html_options [Hash] Atributos HTML adicionales para el contenedor principal
  # @param content [String] Contenido de la tarjeta (se usa cuando no se pasa un bloque)
  # @yield Contenido de la tarjeta (alternativa al parámetro content)
  # @return [String] HTML renderizado de la tarjeta
  def card(title: nil, 
           subtitle: nil, 
           full_width: false, 
           padding: 'p-6', 
           header_action: nil, 
           content: nil,
           **html_options,
           &block)
    
    content = capture(&block) if block_given?
    content ||= ''
    
    render 'shared/card', 
           title: title,
           subtitle: subtitle,
           full_width: full_width,
           padding: padding,
           extra_header_content: header_action,
           content: content,
           html_options: html_options
  end
  
  # Versión alternativa de card que siempre usa ancho completo
  def full_card(**options, &block)
    card(**options.merge(full_width: true), &block)
  end
  
  # Versión de card con padding reducido
  def compact_card(**options, &block)
    card(**options.merge(padding: 'p-4'), &block)
  end
end
