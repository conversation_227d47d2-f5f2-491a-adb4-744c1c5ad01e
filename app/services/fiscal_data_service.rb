# frozen_string_literal: true

# Servicio para consultar y procesar datos fiscales desde APIs externas
#
# Este servicio actúa como una capa de abstracción sobre la API de consulta fiscal,
# proporcionando métodos para obtener, normalizar y validar datos fiscales de empresas
# a partir de su RUT (Registro Único Tributario).
#
# @example Consultar datos fiscales
#   result = FiscalDataService.fetch_fiscal_data("123456780013")
#   if result[:success]
#     puts "Datos fiscales: #{result[:data]}"
#   else
#     puts "Error: #{result[:error]}"
#   end
#
# @note Depende de la configuración en SystemSetting para obtener las credenciales de la API
# @see RutApiService Para la comunicación directa con la API externa
# @see RutValidatorService Para la validación de formatos de RUT
class FiscalDataService
  class << self
    # Consulta y procesa los datos fiscales para un RUT dado
    #
    # Realiza una consulta a la API externa, procesa la respuesta y devuelve
    # los datos en un formato estandarizado. Incluye manejo de errores y
    # normalización de los datos recibidos.
    #
    # @param rut [String] RUT a consultar (puede incluir formato: 12.345.678-001)
    # @return [Hash] Hash con el resultado de la operación:
    #   - :success [Boolean] true si la operación fue exitosa
    #   - :data [Hash, nil] Datos fiscales normalizados si la operación fue exitosa
    #   - :error [String, nil] Mensaje de error si la operación falló
    #   - :status [Integer] Código de estado HTTP de la respuesta
    #
    # @example Respuesta exitosa
    #   {
    #     success: true,
    #     data: {
    #       nombre_legal: "EMPRESA EJEMPLO S.A.",
    #       nombre_fantasia: "EJEMPLO",
    #       tipo_entidad: "Sociedad Anónima",
    #       rut: "123456780013",
    #       # ... otros campos fiscales
    #     },
    #     status: 200
    #   }
    #
    # @example Error en la consulta
    #   {
    #     success: false,
    #     error: "RUT no encontrado",
    #     status: 404
    #   }
    #
    # @note Los datos se normalizan para mantener consistencia en la aplicación
    # @see #normalize_fiscal_data Para el proceso de normalización
    # @see #map_fiscal_data Para el mapeo de campos de la API al modelo interno
    def fetch_fiscal_data(rut)
      begin
        # Limpiar RUT antes de consultar
        clean_rut = sanitize_rut(rut)
        
        # Obtener configuración de la API desde SystemSettings o usar valores por defecto para pruebas
        # Usar las constantes definidas en el modelo SystemSetting
        api_token = SystemSetting.find_by(key: SystemSetting::RUT_API_KEY)&.value || 'token-para-pruebas-123'
        api_endpoint = SystemSetting.find_by(key: SystemSetting::RUT_API_ENDPOINT)&.value || 'https://www.gestioncfe.minervaconsultora.com/api'
        
        # Registrar valores para depuración
        Rails.logger.info("API fiscal usando token: #{api_token[0..5]}... y endpoint: #{api_endpoint}")
        
        # Ya no validamos que existan configuraciones, usamos valores por defecto
        
        # Construir URL para la consulta
        api_url = build_api_url(api_endpoint, clean_rut)
        
        # Realizar la consulta HTTP a la API externa
        response = make_api_request(api_url, api_token)
        
        # Procesar la respuesta
        process_api_response(response)
      rescue StandardError => e
        Rails.logger.error("Error en FiscalDataService: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        { success: false, error: "Error al consultar datos fiscales: #{e.message}" }
      end
    end
    
    private
    
    # Limpia y formatea el RUT para la consulta
    # @param rut [String] RUT con o sin formato
    # @return [String] RUT limpio (solo números y K)
    def sanitize_rut(rut)
      return '' unless rut
      rut.to_s.gsub(/[^\dkK]/i, '').upcase
    end
    
    # Construye la URL para la consulta a la API
    #
    # Ajusta la URL base según sea necesario para incluir el RUT.
    #
    # @param base_url [String] URL base de la API
    # @param rut [String] RUT limpio a consultar
    # @return [String] URL completa para la consulta
    def build_api_url(base_url, rut)
      # Si la base_url ya termina con consultar-rut, agregar solo el RUT
      if base_url.end_with?('consultar-rut')
        # Asegurar que la URL termina con /
        base_url = base_url.end_with?('/') ? base_url : "#{base_url}/"
        return "#{base_url}#{rut}"
      end
      
      # Si no, construir la URL completa (caso tradicional)
      # Asegurar que la URL base termina en /
      base_url = base_url.end_with?('/') ? base_url : "#{base_url}/"
      
      # Construir la URL completa con el RUT
      "#{base_url}consultar-rut/#{rut}"
    end
    
    # Realiza la petición HTTP a la API fiscal
    #
    # Configura el cliente HTTP y realiza la petición GET con el token de autenticación.
    #
    # @param url [String] URL de la API
    # @param token [String] Token de autenticación
    # @return [Net::HTTPResponse] Respuesta de la API
    def make_api_request(url, token)
      # Configurar cliente HTTP
      uri = URI(url)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = (uri.scheme == 'https')
      http.verify_mode = OpenSSL::SSL::VERIFY_PEER
      
      # Crear la petición con los headers necesarios
      request = Net::HTTP::Get.new(uri)
      
      # Formato de autorización según el ejemplo curl proporcionado
      # Token está siendo pasado directamente sin prefijo
      request['Authorization'] = token
      
      # Log para debug
      Rails.logger.info("Consulta API fiscal: URL=#{url}, Token=#{token[0..5]}...")
      
      request['Accept'] = 'application/json'
      
      # Enviar petición y retornar la respuesta
      http.request(request)
    end
    
    # Procesa la respuesta de la API fiscal
    #
    # Maneja diferentes códigos de estado HTTP y parsea la respuesta JSON.
    #
    # @param response [Net::HTTPResponse] Respuesta de la API
    # @return [Hash] Datos procesados o mensaje de error
    def process_api_response(response)
      case response
      when Net::HTTPSuccess
        begin
          # Parsear la respuesta JSON
          data = JSON.parse(response.body, symbolize_names: true)
          
          # Formatear los datos para uso interno
          map_fiscal_data(data)
        rescue JSON::ParserError => e
          { success: false, error: "Error al procesar la respuesta: #{e.message}" }
        end
      else
        { 
          success: false, 
          error: "Error en la API fiscal: #{response.code} - #{response.message}",
          status_code: response.code
        }
      end
    end
    
    # Mapea los datos de la API al formato interno de la aplicación
    #
    # Este método se encarga de transformar los datos recibidos de la API externa
    # al formato esperado por la aplicación, manejando diferentes estructuras
    # de respuesta y normalizando los nombres de los campos.
    #
    # @param api_data [Hash] Datos en bruto de la API
    # @return [Hash] Datos mapeados al formato interno con las claves:
    #   - :nombre_legal [String] Razón social de la empresa
    #   - :nombre_fantasia [String] Nombre comercial (opcional)
    #   - :tipo_entidad [String] Tipo de entidad (ej: "Sociedad Anónima")
    #   - :estado [String] Estado de la empresa (ej: "Activo")
    #   - :fecha_inicio_actividades [Date] Fecha de inicio de actividades
    #   - :actividades [Array<String>] Lista de actividades económicas
    #   - :direccion [String] Dirección fiscal completa
    #   - :ciudad [String] Ciudad de la dirección fiscal
    #   - :departamento [String] Departamento/Estado de la dirección fiscal
    #   - :telefono [String] Teléfono de contacto (opcional)
    #   - :email [String] Email de contacto (opcional)
    #   - :raw_data [Hash] Datos originales de la API para referencia
    #
    # @note Este método es compatible con diferentes formatos de respuesta de la API
    #       y maneja tanto claves en string como en símbolos
    # @see #extract_nested_value Para el manejo de valores anidados
    def map_fiscal_data(data)
      # Si data ya es un hash con símbolos, usarlo directamente
      # Si es un hash con strings, convertirlo a símbolos
      data = data.with_indifferent_access if data.is_a?(Hash)
      
      # Extraer raw_data o usar el hash completo si no existe
      raw_data = data[:raw_data].presence || data
      
      # Mapear campos principales, usando raw_data como respaldo
      result = {
        success: data[:success] != false, # true por defecto a menos que sea explícitamente false
        nombre_legal: data[:nombre_legal].presence || data[:name].presence || raw_data[:nombre].presence,
        nombre_fantasia: data[:nombre_fantasia].presence || data[:trade_name].presence || raw_data[:nombreFantasia].presence,
        tipo_entidad: data[:tipo_entidad].presence || data[:entity_type].presence || 
                     (raw_data[:tipoEntidad].is_a?(Hash) ? raw_data[:tipoEntidad][:descripcion] : raw_data[:tipoEntidad]).presence,
        address: build_address(data, raw_data),
        phone: data[:phone].presence || data[:telefono].presence || 
              extract_phone(raw_data[:contactos] || raw_data['contactos']),
        email: data[:email].presence || data[:correo_electronico].presence || '',
        actividades: data[:actividades].presence || raw_data[:actividades].presence || raw_data['actividades'].presence || [],
        raw_data: raw_data
      }
      
      # Asegurar que los campos sean strings vacíos si son nulos
      result.each do |k, v| 
        next if [:actividades, :raw_data, :success, :address].include?(k)
        result[k] = v.presence || '' 
      end
      
      # Asegurar que actividades sea un array
      result[:actividades] = Array.wrap(result[:actividades]).compact
      
      # Log para depuración
      Rails.logger.info("Datos mapeados: #{result.except(:raw_data).inspect}")
      
      result
    end
    
    # Construye la dirección a partir de los datos disponibles
    def build_address(data, raw_data)
      # Intentar con la dirección directa primero
      return data[:address] if data[:address].present?
      
      # Si no, intentar con domicilio
      return data[:domicilio] if data[:domicilio].present?
      
      # Si no, construir desde raw_data
      if raw_data[:domicilio].is_a?(Hash)
        domicilio = raw_data[:domicilio]
        [domicilio[:calle], domicilio[:numero], domicilio[:apartamento]].compact.join(' ')
      else
        ''
      end
    end
    
    # Extrae el teléfono de los contactos
    def extract_phone(contactos)
      return '' unless contactos.is_a?(Array)
      
      telefono = contactos.find { |c| c[:tipo] == 'TELEFONO FIJO' }
      telefono ? telefono[:valor].to_s : ''
    end
  end
end
