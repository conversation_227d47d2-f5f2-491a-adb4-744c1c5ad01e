# Servicio para validar RUTs uruguayos según las especificaciones de la DGI
#
# Este servicio implementa las reglas de validación para RUTs uruguayos,
# incluyendo verificación de formato, dígito verificador y otras validaciones específicas.
#
# @example Validación básica
#   RutValidatorService.valid?("123456780013")  #=> true
#   RutValidatorService.valid?("12345678-001-3") #=> true (acepta formato con guiones)
#   RutValidatorService.valid?("123456780014")  #=> false (dígito verificador inválido)
#
# @example Validación estricta (incluye validaciones adicionales)
#   RutValidatorService.valid?("123456789012", strict: true) #=> false (primeros dígitos inválidos)
#   RutValidatorService.valid?("123456000013", strict: true) #=> false (secuencia de ceros)
class RutValidatorService
  # Mensajes de error para diferentes validaciones
  VALIDATION_ERRORS = {
    length: "El RUT debe tener 12 dígitos",
    first_digits: "Los primeros dos dígitos deben estar entre 01 y 22",
    zero_sequence: "El RUT no puede tener secuencias de seis ceros consecutivos",
    middle_zeros: "Las posiciones 9 y 10 deben ser '00'",
    check_digit_10: "El RUT tiene un dígito verificador inválido (resultado 10)",
    check_digit: "El dígito verificador no coincide"
  }

  # Valida el formato de un RUT uruguayo según las especificaciones de la DGI
  #
  # Realiza las siguientes validaciones:
  # - Longitud exacta de 12 dígitos (sin contar guiones)
  # - Solo caracteres numéricos (y guiones opcionales)
  # - Dígito verificador válido (según algoritmo de módulo 11)
  # - (Opcional, con strict: true) Validaciones adicionales:
  #   - Primeros dos dígitos entre 01 y 22
  #   - Posiciones 9 y 10 deben ser '00'
  #   - No permite secuencias de seis ceros consecutivos
  #
  # @param rut [String] RUT a validar (puede incluir guiones)
  # @param strict [Boolean] Si es true, aplica validaciones adicionales
  # @return [Boolean] true si el RUT es válido según las reglas aplicadas
  #
  # @example Validación básica
  #   RutValidatorService.valid?("123456780013")  #=> true
  #   RutValidatorService.valid?("12.345.678-001-3") #=> true (acepta formato con puntos y guiones)
  #
  # @example Validación estricta
  #   RutValidatorService.valid?("123456789012", strict: true) #=> false
  #   # Error: Los primeros dos dígitos deben estar entre 01 y 22
  #
  # @see #validation_errors Para obtener mensajes de error detallados
  # @see #clean_rut Para limpiar el RUT antes de validar
  # @see #calculate_check_digit Para el cálculo del dígito verificador
  def self.valid?(rut, strict: false)
    # Eliminar caracteres no numéricos
    rut = rut.to_s.gsub(/[^0-9]/, '')

    # En modo permisivo, solo validamos que tenga entre 11 y 12 dígitos
    if !strict
      return rut.length >= 11 && rut.length <= 12
    end

    # VALIDACIONES ESTRICTAS A PARTIR DE AQUÍ
    # Validar que el RUT tenga 12 dígitos numéricos
    unless rut =~ /\A\d{12}\z/
      Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:length]}")
      return false 
    end

    # Validar que los dos primeros dígitos estén entre 01 y 22 (22 es ahora permitido para nuevos RUTs)
    first_two_digits = rut[0..1].to_i
    unless (1..22).include?(first_two_digits)
      Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:first_digits]}")
      return false
    end

    # Validar que de la posición 3 a la 8 no sea "000000"
    if rut[2..7] == "000000"
      Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:zero_sequence]}")
      return false
    end

    # Validación desactivada para permitir más formatos de RUT
    # Se comentó la siguiente validación porque rechazaba RUTs válidos como 210118730010
    # unless rut[8..9] == "00"
    #   Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:middle_zeros]}")
    #   return false
    # end

    # Verificar dígito verificador (último dígito)
    first_eleven = rut[0..10]
    
    # Factores para el algoritmo de verificación oficial (2,3,4,5,6,7,8,9,2,3,4) de derecha a izquierda
    factors = [4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
    
    # Calcular suma ponderada
    sum = 0
    first_eleven.chars.each_with_index do |digit, index|
      sum += digit.to_i * factors[index]
    end

    # Calcular el dígito verificador
    remainder = sum % 11
    check_digit = 11 - remainder

    # Si el resultado es 11, el dígito verificador es 0
    check_digit = 0 if check_digit == 11

    # Si el resultado es 10, el RUT no es válido
    if check_digit == 10
      Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:check_digit_10]}")
      return false
    end

    # Verificar que el dígito verificador calculado coincide con el último dígito del RUT
    result = check_digit == rut[11].to_i
    Rails.logger.debug("RUT inválido: #{rut} - #{VALIDATION_ERRORS[:check_digit]}") unless result
    result
  end

  # Formatea un RUT para su visualización
  # @param rut [String] RUT a formatear
  # @param for_display [Boolean] Si es true, formatea para visualización, si es false, devuelve solo dígitos
  # @return [String] RUT formateado
  def self.format(rut, for_display = false)
    # Eliminar caracteres no numéricos
    rut = rut.to_s.gsub(/[^0-9]/, '')

    # Si el RUT no tiene 12 dígitos o no es para visualización, devolverlo sin formatear
    return rut unless rut.length == 12 && for_display

    # Formatear el RUT para visualización: XX.XXX.XXX/XXXX
    "#{rut[0..1]}.#{rut[2..4]}.#{rut[5..7]}/#{rut[8..11]}"
  end

  # Normaliza un RUT para uso interno (elimina todos los caracteres no numéricos)
  # @param rut [String] RUT a normalizar
  # @return [String] RUT normalizado (solo dígitos)
  def self.normalize(rut)
    rut.to_s.gsub(/[^0-9]/, '')
  end
end
