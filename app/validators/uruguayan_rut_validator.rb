# frozen_string_literal: true

# Validador para RUT uruguayo
# Implementa la misma lógica que el validador JavaScript pero en el backend
class UruguayanRutValidator < ActiveModel::Validator
  def validate(record)
    # El campo a validar debe ser configurable en options
    rut_field = options[:rut_field] || :rut
    rut_value = record.send(rut_field).to_s.gsub(/[^0-9]/, '')

    # Si el RUT está vacío y es opcional, no validamos
    return if rut_value.blank? && options[:allow_blank]

    # Validar que no esté en blanco
    if rut_value.blank?
      record.errors.add(rut_field, "no puede estar en blanco")
      return
    end

    # Validar longitud
    unless rut_value.length == 12
      record.errors.add(rut_field, "debe tener 12 dígitos numéricos (actual: #{rut_value.length})")
      return
    end

    # Validar primeros dos dígitos (entre 01 y 22)
    first_two_digits = rut_value[0..1].to_i
    unless (1..22).cover?(first_two_digits)
      record.errors.add(rut_field, "los dos primeros dígitos deben estar entre 01 y 22 (actual: #{first_two_digits})")
      return
    end

    # Validar que posiciones 3-8 no sean todos ceros
    if rut_value[2..7] == "000000"
      record.errors.add(rut_field, "los dígitos del 3 al 8 no pueden ser todos ceros")
      return
    end

    # Validar que posiciones 9-10 sean "00"
    unless rut_value[8..9] == "00"
      record.errors.add(rut_field, "los dígitos 9 y 10 deben ser '00' (actual: #{rut_value[8..9]})")
      return
    end

    # Validar dígito verificador
    first_eleven = rut_value[0..10]
    last_digit = rut_value[11].to_i
    factors = [4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]

    sum = 0
    11.times do |i|
      sum += first_eleven[i].to_i * factors[i]
    end

    remainder = sum % 11
    check_digit = 11 - remainder

    # Si el resultado es 11, el dígito verificador es 0
    check_digit = 0 if check_digit == 11

    # Si el resultado es 10, el RUT no es válido
    if check_digit == 10
      record.errors.add(rut_field, "el dígito verificador no es válido (resultado 10)")
      return
    end

    # Verificar que el dígito verificador calculado coincida con el último dígito del RUT
    unless check_digit == last_digit
      record.errors.add(rut_field, "el dígito verificador no es válido (esperado: #{check_digit}, recibido: #{last_digit})")
    end
  end

  # Método de clase para validar un RUT uruguayo sin necesidad de un modelo
  # @param rut [String] RUT a validar
  # @return [Array] [is_valid, error_message]
  def self.validate_rut(rut)
    # Limpiar el RUT
    rut_value = rut.to_s.gsub(/[^0-9]/, '')

    # Validar que no esté en blanco
    return [false, "El RUT no puede estar en blanco"] if rut_value.blank?

    # Validar longitud
    unless rut_value.length == 12
      return [false, "El RUT debe tener 12 dígitos numéricos (actual: #{rut_value.length})"]
    end

    # Validar primeros dos dígitos (entre 01 y 22)
    first_two_digits = rut_value[0..1].to_i
    unless (1..22).cover?(first_two_digits)
      return [false, "Los dos primeros dígitos deben estar entre 01 y 22 (actual: #{first_two_digits})"]
    end

    # Validar que posiciones 3-8 no sean todos ceros
    if rut_value[2..7] == "000000"
      return [false, "Los dígitos del 3 al 8 no pueden ser todos ceros"]
    end

    # Validar que posiciones 9-10 sean "00"
    unless rut_value[8..9] == "00"
      return [false, "Los dígitos 9 y 10 deben ser '00' (actual: #{rut_value[8..9]})"]
    end

    # Validar dígito verificador
    first_eleven = rut_value[0..10]
    last_digit = rut_value[11].to_i
    factors = [4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]

    sum = 0
    11.times do |i|
      sum += first_eleven[i].to_i * factors[i]
    end

    remainder = sum % 11
    check_digit = 11 - remainder

    # Si el resultado es 11, el dígito verificador es 0
    check_digit = 0 if check_digit == 11

    # Si el resultado es 10, el RUT no es válido
    if check_digit == 10
      return [false, "El dígito verificador no es válido (resultado 10)"]
    end

    # Verificar que el dígito verificador calculado coincida con el último dígito del RUT
    unless check_digit == last_digit
      return [false, "El dígito verificador no es válido (esperado: #{check_digit}, recibido: #{last_digit})"]
    end

    # Si llegamos aquí, el RUT es válido
    [true, nil]
  end
end
