<!DOCTYPE html>
<html lang="es">
<head>
  <title>DigiCont</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Aplicación de gestión contable">
  <!-- Remix Icons -->
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= stylesheet_link_tag "application.tailwind", "data-turbo-track": "reload" %>
  
  <%= javascript_importmap_tags %>


</head>

<body class="bg-gray-50 text-gray-900 font-sans">
  <% if user_signed_in? %>
    <!-- Layout para usuarios autenticados -->
    <div class="flex h-screen bg-gray-100">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-lg">
        <div class="p-6">
          <h1 class="text-xl font-bold text-gray-800">DigiCont</h1>
        </div>
        
        <nav class="mt-6">
          <div class="px-6 py-2">
            <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">
              Navegación
            </h3>
          </div>
          
          <div class="mt-2">
            <%= link_to root_path, class: "flex items-center px-6 py-2 text-gray-700 hover:bg-gray-100" do %>
              <i class="ri-dashboard-line mr-3"></i>
              Dashboard
            <% end %>
            
            <%= link_to collaborator_clients_path, class: "flex items-center px-6 py-2 text-gray-700 hover:bg-gray-100" do %>
              <i class="ri-group-line mr-3"></i>
              Clientes
            <% end %>
          </div>
        </nav>
      </aside>

      <!-- Contenido principal -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
          <div class="flex items-center justify-between px-6 py-4">
            <h2 class="text-lg font-semibold text-gray-800">
              <%= content_for?(:page_title) ? yield(:page_title) : "Dashboard" %>
            </h2>
            
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600">
                <%= current_user.email %>
              </span>
              <%= link_to main_session_path, method: :delete,
                  class: "text-sm text-red-600 hover:text-red-800" do %>
                Cerrar Sesión
              <% end %>
            </div>
          </div>
        </header>

        <!-- Área de contenido -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div class="container mx-auto px-6 py-8">
            <!-- Notificaciones -->
            <% if notice %>
              <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                <%= notice %>
              </div>
            <% end %>
            
            <% if alert %>
              <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <%= alert %>
              </div>
            <% end %>
            
            <!-- Contenido de la página -->
            <%= yield %>
          </div>
        </main>
      </div>
    </div>
  <% else %>
    <!-- Contenido para usuarios no autenticados -->
    <main class="flex-grow">
      <%= yield %>
    </main>
  <% end %>
</body>
</html>
