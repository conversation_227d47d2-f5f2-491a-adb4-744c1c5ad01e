<!DOCTYPE html>
<html lang="es">
<head>
  <title>DigiCont</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Aplicación de gestión contable">
  <!-- Remix Icons -->
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  <%= stylesheet_link_tag "tailwind", "inter-font", "application.tailwind", "data-turbo-track": "reload" %>
  
  <%= javascript_importmap_tags %>
  
  <!-- Inicialización de Stimulus -->
  <script type="module">
    console.log('[Stimulus] Inicializando...');
    
    // Verificar si Stimulus ya está cargado globalmente
    if (!window.Stimulus) {
      console.warn('[Stimulus] Stimulus no está disponible globalmente. Cargando...');
      
      // Cargar Stimulus dinámicamente
      import("@hotwired/stimulus").then(({ Application, Controller }) => {
        console.log('[Stimulus] Módulo cargado correctamente');
        
        // Inicializar Stimulus
        const application = Application.start();
        window.Stimulus = application;
        
        console.log('[Stimulus] Aplicación iniciada:', application);
        
        // Registrar controladores manualmente
        const registerController = async (name, path) => {
          try {
            // Convertir el nombre del controlador a la ruta correcta para import()
            let importPath = path;
            if (name === 'fiscal--simplified-rut') {
              importPath = 'controllers/fiscal/simplified_rut_controller';
            } else if (name === 'hello') {
              importPath = 'controllers/hello_controller';
            }
            
            console.log(`[Stimulus] Intentando cargar controlador '${name}' desde:`, importPath);
            const module = await import(importPath);
            
            // Registrar el controlador con el nombre correcto (sin el namespace)
            const controllerName = name.includes('--') ? name.split('--')[1] : name;
            application.register(controllerName, module.default);
            
            console.log(`[Stimulus] Controlador '${name}' registrado correctamente como '${controllerName}'`);
            return true;
          } catch (error) {
            console.error(`[Stimulus] Error al registrar el controlador '${name}':`, error);
            return false;
          }
        };
        
        // Registrar controladores cuando el DOM esté listo
        const initControllers = async () => {
          console.log('[Stimulus] Inicializando controladores...');
          
          try {
            // Registrar controladores con sus nombres completos
            await Promise.all([
              registerController('hello', 'controllers/hello_controller'),
              registerController('fiscal--simplified-rut', 'controllers/fiscal/simplified_rut_controller')
            ]);
            
            console.log('[Stimulus] Todos los controladores registrados');
            console.log('[Stimulus] Controladores disponibles:', 
              Array.from(application.router.modulesByIdentifier?.keys() || []));
              
            // Verificar elementos con controladores
            document.querySelectorAll('[data-controller]').forEach(el => {
              console.log(`[Stimulus] Elemento con controlador:`, {
                element: el,
                controllers: el.dataset.controller,
                actions: el.dataset.action,
                targets: el.dataset.simplifiedRutTarget || 'ninguno'
              });
              
              // Verificar si el controlador está correctamente conectado
              if (el.dataset.controller === 'simplified-rut') {
                console.log('[Stimulus] Elemento simplified-rut encontrado, verificando targets...');
                console.log('  - rutInput:', el.querySelector('[data-simplified-rut-target="rutInput"]'));
                console.log('  - output:', el.querySelector('[data-simplified-rut-target="output"]'));
              }
            });
            
            // Verificar botón de consulta fiscal
            const dgiButton = document.querySelector('[data-action~="simplified-rut#fetchFiscalData"]');
            console.log('[Stimulus] Botón DGI encontrado:', dgiButton);
            
          } catch (error) {
            console.error('[Stimulus] Error durante la inicialización:', error);
          }
        };
        
        // Iniciar cuando el DOM esté listo
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initControllers);
        } else {
          initControllers();
        }
        
      }).catch(error => {
        console.error('[Stimulus] Error al cargar el módulo:', error);
      });
      
    } else {
      console.log('[Stimulus] Ya está cargado globalmente:', window.Stimulus);
    }
    
    // Verificar si Stimulus está disponible
    if (window.Stimulus) {
      console.log('[Diagnóstico] Stimulus está disponible:', window.Stimulus);
      
      // Verificar controladores registrados
      try {
        const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
        console.log('[Diagnóstico] Controladores registrados:', controllers);
        
        // Verificar elementos con data-controller
        document.querySelectorAll('[data-controller]').forEach(el => {
          console.log(`[Diagnóstico] Elemento con controlador:`, {
            element: el,
            controllers: el.dataset.controller,
            actions: el.dataset.action
          });
          console.log('[Diagnóstico] Click manual en botón DGI');
          e.preventDefault();
          alert('Botón DGI clickeado (manejador manual de diagnóstico)');
        });
      } else {
        console.warn('[Diagnóstico] Botón DGI con selector simplified-rut no encontrado');
      }
      
      // Verificar con selector alternativo
      const dgiButtonAlt = document.querySelector('[data-action="click->fiscal--simplified-rut#fetchFiscalData"]');
      if (dgiButtonAlt) {
        console.log('[Diagnóstico] Botón DGI encontrado con selector fiscal--simplified-rut:', dgiButtonAlt);
      } else {
        console.warn('[Diagnóstico] Botón DGI con selector fiscal--simplified-rut no encontrado');
      }
      
      console.log('[Diagnóstico] Diagnóstico completo finalizado');
    });
  </script>
  
  <!-- JavaScript para el sidebar -->
  <script>
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      if (!sidebar) {
        return;
      }

      // Verificar si está oculto (posición negativa)
      const currentLeft = sidebar.getBoundingClientRect().left;
      const isHidden = currentLeft < -200; // Si está muy a la izquierda, está oculto

      if (isHidden) {
        // Mostrar sidebar
        sidebar.style.transform = 'translateX(0)';
        sidebar.style.transition = 'transform 0.3s ease-in-out';
        sidebar.setAttribute('aria-hidden', 'false');

        // Añadir overlay para móviles
        if (!document.getElementById('sidebar-overlay')) {
          const overlay = document.createElement('div');
          overlay.id = 'sidebar-overlay';
          overlay.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 40; display: block;';
          overlay.onclick = toggleSidebar;
          document.body.appendChild(overlay);

          // Ocultar overlay en pantallas grandes
          if (window.innerWidth >= 768) {
            overlay.style.display = 'none';
          }
        }
      } else {
        // Ocultar sidebar

        // Remover foco de cualquier elemento dentro del sidebar antes de ocultarlo
        const focusedElement = sidebar.querySelector(':focus');
        if (focusedElement) {
          focusedElement.blur();
        }

        sidebar.style.transform = 'translateX(-100%)';
        sidebar.style.transition = 'transform 0.3s ease-in-out';
        sidebar.setAttribute('aria-hidden', 'true');

        // Remover overlay
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
          overlay.remove();
        }
      }
    }

    // Inicializar cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', function() {
      const sidebar = document.getElementById('sidebar');
      const toggleButton = document.getElementById('sidebar-toggle-button');

      // Si no hay sidebar (página de login), ocultar el botón toggle
      if (!sidebar && toggleButton) {
        toggleButton.style.display = 'none';
        return;
      }

      // Añadir event listener al botón toggle
      if (toggleButton) {
        toggleButton.addEventListener('click', function(e) {
          e.preventDefault();
          toggleSidebar();
        });
      }

      // Cerrar sidebar con tecla Escape
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar) {
          const currentLeft = sidebar.getBoundingClientRect().left;
          if (currentLeft >= -200) { // Si está visible
            toggleSidebar();
          }
        }
      });
    });
  </script>
</head>

<body class="min-h-screen bg-gray-50 flex flex-col" <%= user_signed_in? ? "data-user-role=\"#{current_user.role}\"" : "" %>>
  <% if user_signed_in? %>
    <!-- Navbar -->
    <%= render 'shared/navbar' %>
    
    <!-- Contenedor principal con sidebar y contenido -->
    <div class="flex flex-1 overflow-hidden">
      <!-- Sidebar - oculto por defecto en móviles -->
      <% if !current_page?(new_main_session_path) %>
        <%= render 'shared/sidebar' %>
      <% end %>
      
      <!-- Contenido principal -->
      <div class="flex-1 flex flex-col overflow-auto bg-gray-50" id="main-content">
        <main class="flex-grow">
          <!-- Contenedor principal del contenido -->
          <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- Mostrar notificaciones flash -->
            <% if notice %>
              <%= render 'shared/alert', type: 'success', message: notice %>
            <% end %>
            
            <% if alert %>
              <%= render 'shared/alert', type: 'error', message: alert %>
            <% end %>
            
            <% if user_signed_in? %>
              <%= render 'shared/impersonation_banner' %>
            <% end %>
            
            <!-- Contenido principal -->
            <%= yield %>
          </div>
        </main>
      </div>
    </div>
  <% else %>
    <!-- Contenido para usuarios no autenticados -->
    <main class="flex-grow">
      <%= yield %>
    </main>
  <% end %>
  <!-- Script de depuración -->
  <script>
    console.log('[Debug] Script de depuración cargado');
    
    // NO crear un Stimulus ficticio, usar solo el real desde importmap
    if (!window.Stimulus && typeof Stimulus !== 'undefined') {
      console.warn('[Debug] Stimulus está definido como variable pero no como window.Stimulus - configurando correctamente');
      window.Stimulus = Stimulus;
    }
    
    // Si aún no existe, consultar el estado pero NO crear un ficticio
    if (!window.Stimulus) {
      console.error('[Debug] ATENCIÓN: Stimulus NO está disponible - revisar la carga de importmap');
    }
    
    console.log('[Debug] Stimulus está disponible:', window.Stimulus);
    console.log('[Debug] Versión de Stimulus:', window.Stimulus.application.version);
    
    try {
      console.log('[Debug] Controladores registrados:', 
        Array.from(window.Stimulus.router.modulesByIdentifier.keys())
      );
    } catch (e) {
      console.warn('[Debug] Error al listar controladores:', e);
    }
    
    // Verificar si el controlador de prueba está registrado
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[Debug] DOM completamente cargado');
      
      // Verificar si el elemento de prueba existe
      const testElement = document.querySelector('[data-controller="test"]');
      if (testElement) {
        console.log('[Debug] Elemento de prueba encontrado:', testElement);
      } else {
        console.warn('[Debug] No se encontró ningún elemento con data-controller="test"');
      }
      
      // Verificar el botón de consulta fiscal DGI (probar diferentes selectores)
      const dgiSelectors = [
        '[data-action="click->fiscal-data#fetchFiscalData"]',
        '[data-action="click->simplified-rut#fetchFiscalData"]',
        '[data-action="click->fiscal--simplified-rut#fetchFiscalData"]',
        '[data-fiscal--simplified-rut-target="fetchButton"]',
        'button:contains("Cargar datos desde DGI")',
        'button:contains("Consultar DGI")'
      ];
      
      // Probar cada selector
      let dgiButtonFound = false;
      for (const selector of dgiSelectors) {
        try {
          let elements;
          if (selector.includes(':contains')) {
            // Implementación manual de :contains ya que no es estándar
            const text = selector.match(/:contains\("([^"]+)"\)/)[1];
            elements = Array.from(document.querySelectorAll('button')).filter(el => 
              el.textContent.includes(text)
            );
          } else {
            elements = document.querySelectorAll(selector);
          }
          
          if (elements.length > 0) {
            console.log(`[Debug] Botón DGI encontrado con selector '${selector}':`, elements);
            dgiButtonFound = true;
          }
        } catch (e) {
          console.warn(`[Debug] Error al buscar con selector '${selector}':`, e);
        }
      }
      
      if (!dgiButtonFound) {
        console.warn('[Debug] Botón DGI no encontrado con ninguno de los selectores');
        
        // Búsqueda más amplia
        console.log('[Debug] Todos los botones en la página:', document.querySelectorAll('button'));
      }
    });
  </script>
</body>
</html>
