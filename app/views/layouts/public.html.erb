<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-100">
<head>
  <title>Prueba de Stimulus - DigiCont</title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>
  
  <!-- Estilos -->
  <%= stylesheet_link_tag "tailwind", "inter-font", "application.tailwind", "data-turbo-track": "reload" %>
  
  <!-- Importmap para JavaScript -->
  <%= javascript_importmap_tags %>
  
  <!-- Cargar Stimulus de manera simplificada -->
  <script type="module">
    // 1. Definir el controlador directamente
    class HelloController {
      connect() {
        console.log('[Debug] Controlador Hello conectado');
        this.element.textContent = '¡Hola Mundo! - Controlador funcionando correctamente';
        this.element.classList.add('text-green-600', 'font-bold');
        
        // Actualizar estado
        const statusElement = document.getElementById('stimulus-status');
        if (statusElement) {
          statusElement.innerHTML = `
            <div class="p-2 bg-green-50 border border-green-200 rounded">
              <span class="text-green-700">✓ Controlador conectado y funcionando</span>
            </div>
          `;
        }
      }
    }

    // 2. Inicializar Stimulus de forma mínima
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[Debug] Inicializando Stimulus...');
      
      // Configuración mínima de Stimulus
      window.Stimulus = {
        application: {
          register: (name, controller) => {
            window.Stimulus.controllers = window.Stimulus.controllers || {};
            window.Stimulus.controllers[name] = controller;
            console.log(`[Debug] Controlador registrado: ${name}`);
          },
          start: () => {
            console.log('[Debug] Stimulus iniciado');
            return window.Stimulus;
          },
          debug: true
        },
        controllers: {}
      };

      // Registrar el controlador
      window.Stimulus.application.register('hello', HelloController);
      
      // Conectar controladores una sola vez
      document.querySelectorAll('[data-controller]').forEach(element => {
        const controllerName = element.getAttribute('data-controller');
        if (window.Stimulus.controllers[controllerName]) {
          try {
            const controller = new window.Stimulus.controllers[controllerName]();
            controller.element = element;
            if (typeof controller.connect === 'function') controller.connect();
            console.log(`[Debug] Controlador ${controllerName} conectado`);
          } catch (error) {
            console.error(`[Debug] Error conectando controlador ${controllerName}:`, error);
          }
        }
      });
    });
  </script>
  
  <!-- Favicon -->
  <link rel="icon" href="/favicon.ico" type="image/x-icon">
  
  <!-- Script de depuración simplificado -->
  <script>
    console.log('[Debug] Layout público cargado');
    
    // Verificar si Stimulus está disponible
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[Debug] DOM completamente cargado');
      
      if (window.Stimulus) {
        console.log('[Debug] Stimulus está disponible');
      } else {
        console.log('[Debug] Nota: Stimulus está inicializado de forma minimalista');
      }
    });
  </script>
</head>

<body class="h-full">
  <div class="min-h-full">
    <!-- Barra de navegación -->
    <nav class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <span class="text-xl font-bold text-indigo-600">DigiCont</span>
              <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-indigo-100 text-indigo-800 rounded-full">DEV</span>
            </div>
          </div>
          <div class="flex items-center">
            <a href="/" class="text-sm font-medium text-gray-700 hover:text-gray-900">Volver al inicio</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Contenido principal -->
    <main class="py-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <%= yield %>
      </div>
    </main>
  </div>

  <!-- Scripts adicionales -->
  <%= yield :javascripts if content_for?(:javascripts) %>
  
  <!-- Script de verificación básica -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Verificar si el elemento con data-controller="hello" muestra contenido correcto
      const helloElement = document.querySelector('[data-controller="hello"]');
      
      if (helloElement) {
        const contenidoOk = helloElement.textContent.includes('¡Hola Mundo!');
        const estilosOk = helloElement.classList.contains('text-green-600');
        
        if (contenidoOk && estilosOk) {
          console.log('[Debug] ✅ Controlador Hello verificado: texto y estilos correctos');
        } else {
          console.log('[Debug] ⚠️ Controlador Hello conectado pero con problemas de texto o estilo');
        }
      } else {
        console.log('[Debug] ❌ No se encontró elemento con data-controller="hello"');
      }
      
      // Actualizar mensaje de estado adicional
      const additionalStatus = document.getElementById('additional-status');
      if (additionalStatus) {
        if (helloElement && helloElement.textContent.includes('¡Hola Mundo!')) {
          additionalStatus.innerHTML = '';
        } else {
          additionalStatus.innerHTML = `
            <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
              <span class="text-yellow-700">⚠ El controlador 'hello' tiene problemas</span>
            </div>
          `;
        }
      }
    });
  </script>
</body>
</html>
