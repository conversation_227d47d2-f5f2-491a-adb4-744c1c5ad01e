<%# Vista compartida del dashboard que puede ser utilizada por todos los roles %>
<div class="min-h-full bg-gray-50">
  <div class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <!-- Encabezado del dashboard -->
      <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              <%= content_for(:dashboard_title) || 'Panel de Control' %>
            </h1>
            <% if content_for?(:dashboard_subtitle) %>
              <p class="mt-2 text-sm text-gray-500">
                <%= yield :dashboard_subtitle %>
              </p>
            <% end %>
          </div>
          <div class="mt-4 flex md:mt-0 md:ml-4">
            <% if content_for?(:dashboard_actions) %>
              <%= yield :dashboard_actions %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Widgets principales -->
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-8">
        <%= yield :dashboard_widgets if content_for?(:dashboard_widgets) %>
      </div>

      <!-- Contenido principal -->
      <% if content_for?(:dashboard_content) %>
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
          <div class="px-6 py-5 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              <%= content_for(:dashboard_content_title) || 'Resumen' %>
            </h3>
          </div>
          <div class="px-6 py-5">
            <%= yield :dashboard_content %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
