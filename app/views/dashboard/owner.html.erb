<% content_for :dashboard_title, 'Panel de Propietario' %>
<% content_for :dashboard_subtitle, 'Gestiona tu organización y accede a las herramientas de administración' %>

<% content_for :dashboard_widgets do %>
  <!-- Widget 1: Resumen de la Organización -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0 bg-indigo-500 rounded-lg p-3">
          <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <div class="ml-5 flex-1">
          <dt class="text-sm font-medium text-gray-500 truncate">Organización</dt>
          <dd class="flex items-baseline">
            <div class="text-2xl font-semibold text-gray-900"><%= current_organization&.name || 'No asignada' %></div>
          </dd>
        </div>
      </div>
      <div class="mt-6 border-t border-gray-200 pt-4">
        <% 
          # Obtener organizaciones del usuario
          user_orgs = current_user.organizations.to_a
          
          # Mostrar información de depuración
          Rails.logger.info "\n=== DEBUG GESTIÓN ORGANIZACIÓN ==="
          Rails.logger.info "Usuario ID: #{current_user.id}"
          Rails.logger.info "Número de organizaciones: #{user_orgs.size}"
          
          # Depurar permisos
          has_permission = current_user.has_system_permission?('multiple_organizations_enabled')
          override_status = current_user.permission_override_status('multiple_organizations_enabled')
          global_setting = SystemConfiguration.get_bool('multiple_organizations_enabled')
          
          Rails.logger.info "--- Permisos ---"
          Rails.logger.info "Tiene permiso múltiples orgs: #{has_permission}"
          Rails.logger.info "Estado de sobreescritura: #{override_status.inspect}"
          Rails.logger.info "Configuración global: #{global_setting}"
          Rails.logger.info "Es superadmin: #{current_user.superadmin?}"
          
          # Simplificamos la lógica: si solo tiene una organización, redirigir a su edición
          # sin importar los permisos de múltiples organizaciones
          should_redirect = user_orgs.one? && !current_user.superadmin?
          
          Rails.logger.info "¿Debería redirigir a edición?: #{should_redirect}"
          
          if should_redirect && (org = user_orgs.first)
            # Redirigir directamente a la edición de la organización
            redirect_url = "/owner/organizations/#{org.id}/edit?section=general"
            Rails.logger.info "URL de redirección: #{redirect_url}"
          else
            # Ir a la lista de organizaciones
            redirect_url = owner_organizations_path
            Rails.logger.info "URL de lista de organizaciones: #{redirect_url}"
          end
        %>
        <%= link_to 'Gestionar organización', 
                    redirect_url,
                    class: 'inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500',
                    'data-orgs-count': user_orgs.size,
                    'data-should-redirect': should_redirect,
                    'data-redirect-url': redirect_url %>
                    
        <!-- Mostrar información de depuración en la interfaz -->
        <div class="mt-2 text-xs text-gray-500">
          <div>Organizaciones: <%= user_orgs.size %></div>
          <div>Redirección a edición: <%= should_redirect %></div>
          <div>URL: <%= redirect_url %></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Widget 2: Acciones Rápidas -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-6">
      <h3 class="text-base font-medium text-gray-900">Acciones Rápidas</h3>
      <div class="mt-4 space-y-4">
        <%= link_to edit_owner_organization_path(current_organization, section: 'general'), 
                    class: 'group flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors' do %>
          <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-900">Editar Perfil</p>
            <p class="text-xs text-gray-500">Actualiza la información de tu organización</p>
          </div>
        <% end %>

        <%= link_to edit_owner_organization_path(current_organization, section: 'billing'), 
                    class: 'group flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors' do %>
          <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-900">Facturación</p>
            <p class="text-xs text-gray-500">Gestiona tus métodos de pago y facturas</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Widget 3: Estado de la Cuenta -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-6">
      <h3 class="text-base font-medium text-gray-900">Estado de la Cuenta</h3>
      <div class="mt-4 space-y-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-500">Plan Actual</span>
          <span class="text-sm font-medium text-gray-900">Premium</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-500">Miembros</span>
          <span class="text-sm font-medium text-gray-900">3/10</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-500">Almacenamiento</span>
          <span class="text-sm font-medium text-gray-900">1.2 GB / 5 GB</span>
        </div>
        <div class="mt-6">
          <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-indigo-600 rounded-full" style="width: 24%"></div>
          </div>
          <p class="mt-1 text-xs text-right text-gray-500">24% utilizado</p>
        </div>
      </div>
    </div>
  </div>
<% end %>

<% content_for :dashboard_content do %>
  <div class="space-y-6">
    <div>
      <h3 class="text-base font-medium text-gray-900 mb-2">Actividad Reciente</h3>
      <p class="text-sm text-gray-500 mb-4">Últimas actividades en tu organización</p>
      
      <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <p class="text-sm text-gray-500 text-center">No hay actividad reciente para mostrar</p>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-base font-medium text-gray-900 mb-2">Tareas Pendientes</h3>
        <p class="text-sm text-gray-500 mb-4">Tareas que requieren tu atención</p>
        
        <div class="space-y-3">
          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 text-indigo-500">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-3 text-sm text-gray-700">Completar la configuración de tu organización</p>
          </div>
          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 text-indigo-500">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-3 text-sm text-gray-700">Verificar información de facturación</p>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-base font-medium text-gray-900 mb-2">Notificaciones</h3>
        <p class="text-sm text-gray-500 mb-4">Mensajes y alertas importantes</p>
        
        <div class="space-y-3">
          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 text-yellow-500">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-3 text-sm text-gray-700">Tu prueba gratuita termina en 7 días</p>
          </div>
          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 text-blue-500">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h2a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-3 text-sm text-gray-700">Nuevas características disponibles</p>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%= render 'dashboard/shared_dashboard' %>
