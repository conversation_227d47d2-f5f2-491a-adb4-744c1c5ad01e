<div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-12">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Prueba de Stimulus</h1>
      <p class="text-lg text-gray-600">Página de prueba para depurar la inicialización de Stimulus</p>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Prueba de Controlador Hello</h2>
      <div class="space-y-4">
        <div data-controller="hello" class="p-4 bg-blue-50 border border-blue-200 rounded">
          <p class="text-blue-800">Este texto debería cambiar a "Hello World!" si Stimulus está funcionando correctamente.</p>
          <p class="text-sm text-blue-600 mt-2">Controlador: <code>hello_controller.js</code></p>
        </div>

        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
          <h3 class="font-medium text-gray-700 mb-2">Estado de Stimulus:</h3>
          <div id="stimulus-status" class="text-sm font-mono p-2 bg-gray-100 rounded">
            Verificando estado de Stimulus...
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-4xl mx-auto p-6 space-y-8">
      <h1 class="text-2xl font-bold mb-6">Prueba de Integración Stimulus</h1>
      
      <!-- Controlador Hello -->
      <div data-controller="hello" class="p-6 border rounded-lg bg-white shadow-sm">
        <h2 class="text-xl font-semibold mb-4">Controlador Hello</h2>
        <div class="space-y-4">
          <div data-hello-target="output" class="text-gray-700">
            Haciendo pruebas con Stimulus...
          </div>
          <button 
            data-action="click->hello#greet"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Saludar
          </button>
        </div>
      </div>
      
      <!-- Controlador Fiscal RUT -->
      <div data-controller="fiscal--simplified-rut" class="p-6 border rounded-lg bg-white shadow-sm">
        <h2 class="text-xl font-semibold mb-4">Controlador de RUT Simplificado</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Ingresa un RUT</label>
            <input 
              type="text" 
              data-fiscal--simplified-rut-target="rutInput"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="12.345.678-9"
            >
          </div>
          <div 
            data-fiscal--simplified-rut-target="output" 
            class="text-sm p-2 rounded bg-gray-50 border border-gray-200"
          >
            Ingresa un RUT para validar
          </div>
          <div class="flex space-x-4">
            <button 
              data-action="click->fiscal--simplified-rut#validateRut"
              class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
            >
              Validar RUT
            </button>
            <button 
              data-action="click->fiscal--simplified-rut#fetchFiscalData"
              class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Consultar DGI
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-4">Información de Depuración</h2>
      <div class="space-y-4">
        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
          <h3 class="font-medium text-gray-700 mb-2">Consola del Navegador:</h3>
          <p class="text-sm text-gray-600 mb-2">Abre la consola del navegador (F12 o clic derecho > Inspeccionar > Consola) para ver los mensajes de depuración.</p>
          <p class="text-sm text-gray-600">Busca mensajes que comiencen con <code class="bg-gray-200 px-1 rounded">[Stimulus]</code> o <code class="bg-gray-200 px-1 rounded">[Debug]</code>.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Script de depuración
  document.addEventListener('DOMContentLoaded', function() {
    const statusElement = document.getElementById('stimulus-status');
    
    if (window.Stimulus) {
      statusElement.innerHTML = `
        <span class="text-green-700">✓ Stimulus está cargado correctamente</span><br>
        <span class="text-sm">Versión: ${window.Stimulus.application.version}</span><br>
        <span class="text-sm">Modo depuración: ${window.Stimulus.application.debug ? 'activado' : 'desactivado'}</span>
      `;
      
      // Mostrar controladores registrados
      const controllers = Array.from(window.Stimulus.router.modulesByIdentifier.keys());
      statusElement.innerHTML += `
        <div class="mt-2">
          <span class="font-medium">Controladores registrados:</span>
          <ul class="list-disc list-inside mt-1">
            ${controllers.map(c => `<li><code>${c}</code></li>`).join('')}
          </ul>
        </div>
      `;
    } else {
      statusElement.innerHTML = '<span class="text-red-700">✗ Stimulus NO está disponible</span>';
    }
    
    // Verificar si el controlador hello está conectado
    setTimeout(() => {
      const helloElement = document.querySelector('[data-controller="hello"]');
      if (helloElement && helloElement.textContent.includes('¡Hola Mundo!')) {
        statusElement.innerHTML += `
          <div class="mt-2 p-2 bg-green-50 border border-green-200 rounded">
            <span class="text-green-700">✓ El controlador 'hello' se ha conectado correctamente</span>
          </div>
        `;
      } else {
        statusElement.innerHTML += `
          <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
            <span class="text-yellow-700">⚠ El controlador 'hello' no se ha conectado correctamente</span>
          </div>
        `;
      }
    }, 500);
  });
</script>
