<div class="bg-white shadow-md rounded-lg overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Permisos de Usuarios</h1>
    <p class="text-sm text-gray-600 mt-1">Gestiona excepciones de permisos a nivel de usuario</p>
  </div>

  <div class="p-6">
    <!-- Men<PERSON> de navegación -->
    <div class="mb-6 flex flex-wrap gap-4">
      <%= link_to admin_panel_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
        Dashboard
      <% end %>
      
      <%= link_to admin_panel_system_settings_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Configuraciones
      <% end %>
      
      <%= link_to admin_panel_user_permissions_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
        Permisos de Usuarios
      <% end %>
    </div>
    
    <!-- Explicación -->
    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
      <h3 class="text-lg font-medium text-blue-800 mb-2">Excepciones de Permisos</h3>
      <p class="text-sm text-blue-700">
        Aquí puedes configurar excepciones de permisos a nivel de usuario. Esto te permite otorgar permisos específicos a usuarios individuales, independientemente de la configuración global del sistema.
      </p>
      <p class="text-sm text-blue-700 mt-2">
        Por ejemplo, puedes permitir que un usuario específico cree múltiples organizaciones, incluso si esta opción está desactivada globalmente.
      </p>
    </div>
    
    <!-- Usuarios con excepciones -->
    <% if @users_with_overrides.any? %>
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Usuarios con Excepciones</h3>
        
        <div class="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usuario</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rol</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excepciones</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @users_with_overrides.each do |user| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900"><%= user.email %></div>
                        <div class="text-sm text-gray-500"><%= user.name %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      <%= user.role %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      <% user.permission_overrides.each do |override| %>
                        <div class="mb-1">
                          <span class="<%= override.enabled ? 'text-green-600' : 'text-red-600' %>">
                            <%= override.permission_key.humanize %>: <%= override.enabled ? 'Habilitado' : 'Deshabilitado' %>
                          </span>
                          <%= link_to destroy_user_permission_override_path(user, override.permission_key), 
                                      method: :delete, 
                                      class: "ml-2 text-red-600 hover:text-red-900",
                                      data: { confirm: "¿Estás seguro de eliminar esta excepción?" } do %>
                            <svg class="h-4 w-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button type="button" 
                            class="text-indigo-600 hover:text-indigo-900"
                            onclick="toggleUserPermissionForm('<%= user.id %>')">
                      Agregar Excepción
                    </button>
                  </td>
                </tr>
                <tr id="user-permission-form-<%= user.id %>" class="hidden">
                  <td colspan="4" class="px-6 py-4 bg-gray-50">
                    <div class="flex items-center space-x-4">
                      <div class="flex-grow">
                        <label for="permission-select-<%= user.id %>" class="block text-sm font-medium text-gray-700">Permiso</label>
                        <select id="permission-select-<%= user.id %>" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                          <% @overridable_permissions.each do |permission| %>
                            <option value="<%= permission %>"><%= permission.humanize %></option>
                          <% end %>
                        </select>
                      </div>
                      <div>
                        <label for="enabled-select-<%= user.id %>" class="block text-sm font-medium text-gray-700">Estado</label>
                        <select id="enabled-select-<%= user.id %>" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                          <option value="true">Habilitado</option>
                          <option value="false">Deshabilitado</option>
                        </select>
                      </div>
                      <div class="flex items-end">
                        <button type="button" 
                                class="btn btn-primary"
                                onclick="createUserPermissionOverride('<%= user.id %>')">
                          Guardar
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% end %>
    
    <!-- Agregar excepción a un usuario -->
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Agregar Excepción a un Usuario</h3>
      
      <div class="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <div class="p-6">
          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label for="user-select" class="block text-sm font-medium text-gray-700">Usuario</label>
              <select id="user-select" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <option value="">Selecciona un usuario</option>
                <% @users.each do |user| %>
                  <option value="<%= user.id %>"><%= user.email %> (<%= user.role %>)</option>
                <% end %>
              </select>
            </div>
            
            <div class="sm:col-span-2">
              <label for="permission-select" class="block text-sm font-medium text-gray-700">Permiso</label>
              <select id="permission-select" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <% @overridable_permissions.each do |permission| %>
                  <option value="<%= permission %>"><%= permission.humanize %></option>
                <% end %>
              </select>
            </div>
            
            <div class="sm:col-span-1">
              <label for="enabled-select" class="block text-sm font-medium text-gray-700">Estado</label>
              <select id="enabled-select" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <option value="true">Habilitado</option>
                <option value="false">Deshabilitado</option>
              </select>
            </div>
          </div>
          
          <div class="mt-6">
            <button type="button" 
                    id="add-permission-btn"
                    class="btn btn-primary"
                    onclick="createUserPermissionOverride()">
              Agregar Excepción
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript para manejar las excepciones de permisos -->
<script>
  // Función para mostrar/ocultar el formulario de excepción de permiso
  function toggleUserPermissionForm(userId) {
    const form = document.getElementById(`user-permission-form-${userId}`);
    form.classList.toggle('hidden');
  }
  
  // Función para crear una excepción de permiso
  function createUserPermissionOverride(userId = null) {
    // Si no se proporciona un ID de usuario, tomarlo del selector principal
    if (!userId) {
      const userSelect = document.getElementById('user-select');
      userId = userSelect.value;
      
      if (!userId) {
        alert('Por favor, selecciona un usuario');
        return;
      }
    }
    
    // Obtener los valores del formulario
    const permissionKey = userId ? 
      document.getElementById(`permission-select-${userId}`).value : 
      document.getElementById('permission-select').value;
    
    const enabled = userId ? 
      document.getElementById(`enabled-select-${userId}`).value : 
      document.getElementById('enabled-select').value;
    
    // Crear la excepción de permiso mediante una solicitud AJAX
    fetch(`/user_permission_overrides/${userId}/${permissionKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ enabled: enabled })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(data.message);
        location.reload(); // Recargar la página para mostrar los cambios
      } else {
        alert(data.error || 'Error al crear la excepción de permiso');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error al crear la excepción de permiso');
    });
  }
  
  // Función para eliminar una excepción de permiso
  function deleteUserPermissionOverride(userId, permissionKey) {
    if (!confirm('¿Estás seguro de eliminar esta excepción?')) {
      return;
    }
    
    // Eliminar la excepción de permiso mediante una solicitud AJAX
    fetch(`/user_permission_overrides/${userId}/${permissionKey}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(data.message);
        location.reload(); // Recargar la página para mostrar los cambios
      } else {
        alert(data.error || 'Error al eliminar la excepción de permiso');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error al eliminar la excepción de permiso');
    });
  }
</script>
