<% content_for :page_title, "Debug JavaScript" %>

<div class="max-w-4xl mx-auto">
  <h1 class="text-2xl font-bold text-gray-900 mb-6">Debug JavaScript</h1>
  
  <div class="bg-white rounded-lg shadow p-6 mb-6">
    <h2 class="text-lg font-semibold mb-4">Estado de JavaScript</h2>
    <div id="js-status">
      <p class="text-gray-600">Verificando...</p>
    </div>
  </div>
  
  <div class="bg-white rounded-lg shadow p-6">
    <h2 class="text-lg font-semibold mb-4">Logs de Consola</h2>
    <div id="console-logs" class="bg-gray-100 p-4 rounded text-sm font-mono">
      <p>Los logs aparecerán aquí...</p>
    </div>
  </div>
</div>

<script>
console.log('🔍 [Debug] Script inline ejecutándose...');

// Capturar logs de consola
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

const logs = [];

function addLog(type, ...args) {
  const message = args.map(arg => 
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
  ).join(' ');
  
  logs.push(`[${type.toUpperCase()}] ${message}`);
  updateLogsDisplay();
}

console.log = function(...args) {
  originalLog.apply(console, args);
  addLog('log', ...args);
};

console.error = function(...args) {
  originalError.apply(console, args);
  addLog('error', ...args);
};

console.warn = function(...args) {
  originalWarn.apply(console, args);
  addLog('warn', ...args);
};

function updateLogsDisplay() {
  const logsElement = document.getElementById('console-logs');
  if (logsElement) {
    logsElement.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
    logsElement.scrollTop = logsElement.scrollHeight;
  }
}

function updateStatus() {
  const statusElement = document.getElementById('js-status');
  if (!statusElement) return;
  
  const status = {
    'JavaScript básico': '✅ Funcionando',
    'Turbo': window.Turbo ? '✅ Cargado' : '❌ No cargado',
    'Stimulus': window.Stimulus ? '✅ Cargado' : '❌ No cargado',
    'Importmaps': document.querySelector('script[type="importmap"]') ? '✅ Presente' : '❌ No encontrado'
  };
  
  statusElement.innerHTML = Object.entries(status)
    .map(([key, value]) => `<p><strong>${key}:</strong> ${value}</p>`)
    .join('');
}

// Verificar estado inicial
console.log('🔍 [Debug] Verificando estado inicial...');
updateStatus();

// Verificar después de DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔍 [Debug] DOM cargado, verificando estado...');
  updateStatus();
});

// Verificar después de load
window.addEventListener('load', () => {
  console.log('🔍 [Debug] Página completamente cargada, verificando estado final...');
  setTimeout(() => {
    updateStatus();
  }, 1000);
});

console.log('🔍 [Debug] Script inline completado');
</script>
