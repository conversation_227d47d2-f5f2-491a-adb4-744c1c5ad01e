<% content_for :page_title, "Prueba de Stimulus" %>



<div class="max-w-4xl mx-auto">
  <h1 class="text-2xl font-bold text-gray-900 mb-6">Prueba de Controladores Stimulus</h1>
  
  <!-- Prueba del controlador Hello -->
  <div class="bg-white rounded-lg shadow p-6 mb-6">
    <h2 class="text-lg font-semibold mb-4">Controlador Hello</h2>
    <div data-controller="hello">
      <input data-hello-target="name" type="text" placeholder="Ingresa tu nombre" 
             class="border border-gray-300 rounded px-3 py-2 mr-2">
      <button data-action="click->hello#greet" 
              class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
        Saludar
      </button>
      <div data-hello-target="output" class="mt-2 text-green-600"></div>
    </div>
  </div>
  
  <!-- Prueba del controlador SimplifiedRut -->
  <div class="bg-white rounded-lg shadow p-6">
    <h2 class="text-lg font-semibold mb-4">Controlador SimplifiedRut</h2>
    <div data-controller="simplified-rut" data-simplified-rut-namespace-value="owner">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">RUT:</label>
        <div class="flex items-center space-x-2">
          <input data-simplified-rut-target="input" 
                 type="text" 
                 placeholder="Ej: 123456789-0" 
                 class="border border-gray-300 rounded px-3 py-2 flex-1">
          <button data-action="click->simplified-rut#fetchFiscalData" 
                  data-simplified-rut-target="fetchButton"
                  class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
            Consultar DGI
          </button>
          <button data-action="click->simplified-rut#clear" 
                  data-simplified-rut-target="clearButton"
                  class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                  style="display: none;">
            Limpiar
          </button>
        </div>
      </div>
      
      <!-- Spinner de carga -->
      <div data-simplified-rut-target="spinner" class="hidden">
        <div class="flex items-center justify-center py-4">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
          <span class="ml-2 text-gray-600">Consultando datos fiscales...</span>
        </div>
      </div>
      
      <!-- Contenedor de errores -->
      <div data-simplified-rut-target="errorContainer" class="hidden"></div>
      <div data-simplified-rut-target="error" class="hidden"></div>
      
      <!-- Contenedor de resultados -->
      <div data-simplified-rut-target="resultsContainer" class="hidden mt-4">
        <h3 class="text-md font-semibold mb-2">Resultados:</h3>
        <div class="bg-gray-50 p-4 rounded">
          <p>Los datos fiscales aparecerán aquí...</p>
        </div>
      </div>
      
      <!-- Formulario fiscal de prueba -->
      <form data-simplified-rut-target="fiscalForm" class="mt-6 space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Nombre Legal:</label>
            <input name="organization[nombre_legal]" type="text" 
                   class="w-full border border-gray-300 rounded px-3 py-2">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Nombre Fantasía:</label>
            <input name="organization[nombre_fantasia]" type="text" 
                   class="w-full border border-gray-300 rounded px-3 py-2">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Dirección:</label>
            <input name="organization[direccion]" type="text" 
                   class="w-full border border-gray-300 rounded px-3 py-2">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Ciudad:</label>
            <input name="organization[ciudad]" type="text" 
                   class="w-full border border-gray-300 rounded px-3 py-2">
          </div>
        </div>
        
        <button type="submit" 
                data-simplified-rut-target="submitBtn"
                class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
          Guardar Datos Fiscales
        </button>
      </form>
    </div>
  </div>
  
  <!-- Información de depuración -->
  <div class="bg-gray-100 rounded-lg p-6 mt-6">
    <h2 class="text-lg font-semibold mb-4">Información de Depuración</h2>
    <div id="debug-info">
      <p class="text-sm text-gray-600">Abre la consola del navegador para ver los logs de Stimulus.</p>
    </div>
  </div>
</div>

<script>
// Función para verificar Stimulus con reintentos
function checkStimulus(attempt = 1, maxAttempts = 10) {
  console.log(`[Test Page] Intento ${attempt}/${maxAttempts} - Verificando Stimulus...`);

  if (window.Stimulus) {
    console.log('✅ Stimulus está disponible:', window.Stimulus);
    console.log('✅ Versión de Stimulus:', window.Stimulus.version || 'N/A');

    // Listar controladores registrados
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log('✅ Controladores registrados:', controllers);

    // Actualizar la información de depuración en la página
    const debugInfo = document.getElementById('debug-info');
    if (debugInfo) {
      debugInfo.innerHTML = `
        <p class="text-sm text-green-600 mb-2">✅ Stimulus está funcionando correctamente (LOCAL - Importmap)</p>
        <p class="text-sm text-gray-600 mb-1"><strong>Versión:</strong> ${window.Stimulus.version || 'N/A'}</p>
        <p class="text-sm text-gray-600 mb-1"><strong>Fuente:</strong> Local (vendor/javascript/@hotwired--stimulus.js)</p>
        <p class="text-sm text-gray-600 mb-1"><strong>Controladores registrados:</strong></p>
        <ul class="text-sm text-gray-600 ml-4">
          ${controllers.map(name => `<li>• ${name}</li>`).join('')}
        </ul>
        <p class="text-sm text-gray-500 mt-2">Cargado en intento ${attempt}/${maxAttempts}</p>
      `;
    }

    // Verificar controladores específicos
    if (controllers.includes('hello')) {
      console.log('✅ Controlador "hello" registrado correctamente');
    } else {
      console.warn('⚠️ Controlador "hello" NO está registrado');
    }

    if (controllers.includes('simplified-rut')) {
      console.log('✅ Controlador "simplified-rut" registrado correctamente');
    } else {
      console.warn('⚠️ Controlador "simplified-rut" NO está registrado');
    }

    return true; // Stimulus encontrado
  } else {
    console.warn(`⚠️ Intento ${attempt}/${maxAttempts}: Stimulus no está disponible aún`);

    if (attempt < maxAttempts) {
      // Reintentar después de un breve delay
      setTimeout(() => checkStimulus(attempt + 1, maxAttempts), 100);
    } else {
      console.error('❌ Stimulus NO está disponible después de todos los intentos');
      const debugInfo = document.getElementById('debug-info');
      if (debugInfo) {
        debugInfo.innerHTML = `
          <p class="text-sm text-red-600 mb-2">❌ Error: Stimulus no está disponible</p>
          <p class="text-sm text-gray-600">Se intentó cargar ${maxAttempts} veces sin éxito.</p>
          <p class="text-sm text-gray-600">Verifica la consola para más detalles.</p>
        `;
      }
    }
    return false;
  }
}

document.addEventListener('DOMContentLoaded', () => {
  console.log('[Test Page] 📄 Página de prueba cargada, iniciando verificación de Stimulus...');

  // Iniciar verificación con reintentos
  checkStimulus();
});

// También verificar cuando todos los módulos se hayan cargado
window.addEventListener('load', () => {
  console.log('[Test Page] 🚀 Todos los recursos cargados, verificación final...');

  // Verificación final después de que todo se haya cargado
  setTimeout(() => {
    if (!window.Stimulus) {
      console.log('[Test Page] 🔄 Verificación final: Stimulus aún no disponible, reintentando...');
      checkStimulus(1, 5);
    } else {
      console.log('[Test Page] ✅ Verificación final: Stimulus disponible correctamente');
    }
  }, 500);
});
</script>
