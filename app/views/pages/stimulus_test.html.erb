<div class="max-w-4xl mx-auto p-6">
  <h1 class="text-2xl font-bold mb-6">Prueba de Stimulus</h1>
  
  <!-- Controlador de prueba -->
  <div data-controller="hello" class="mb-8 p-6 border rounded-lg bg-white shadow-sm">
    <h2 class="text-xl font-semibold mb-4">Controlador de Prueba (hello_controller)</h2>
    
    <div class="space-y-4">
      <div data-hello-target="output" class="p-3 bg-gray-50 rounded">
        Esperando interacción...
      </div>
      <button 
        data-action="click->hello#greet" 
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        Saludar
      </button>
    </div>
  </div>
  
  <!-- Controlador de RUT simplificado -->
  <div data-controller="simplified-rut" class="p-6 border rounded-lg bg-white shadow-sm">
    <h2 class="text-xl font-semibold mb-4">Controlador de RUT Simplificado</h2>
    
    <!-- Formulario para el RUT -->
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Ingresa un RUT</label>
        <div class="flex space-x-2">
          <input 
            type="text" 
            data-simplified-rut-target="input"
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="12.345.678-9"
          >
          <button 
            type="button"
            data-action="click->simplified-rut#clearInput"
            class="px-3 py-1.5 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
            data-simplified-rut-target="clearButton"
          >
            Limpiar
          </button>
        </div>
      </div>
      
      <!-- Contenedor de resultados -->
      <div 
        data-simplified-rut-target="resultsContainer" 
        class="p-4 bg-gray-50 rounded border border-gray-200 hidden"
      >
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium">Resultados de la búsqueda</h3>
          <button 
            type="button" 
            data-action="click->simplified-rut#clearResults"
            class="text-sm text-gray-500 hover:text-gray-700"
          >
            Cerrar
          </button>
        </div>
        <div data-simplified-rut-target="output" class="text-sm">
          Los resultados aparecerán aquí...
        </div>
      </div>
      
      <!-- Mensajes de error -->
      <div 
        data-simplified-rut-target="errorContainer" 
        class="p-3 bg-red-50 text-red-700 rounded border border-red-200 hidden"
      >
        <div data-simplified-rut-target="error"></div>
      </div>
      
      <!-- Acciones -->
      <div class="flex space-x-3 pt-2">
        <button 
          type="button"
          data-action="click->simplified-rut#fetchFiscalData"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors flex items-center"
          data-simplified-rut-target="fetchButton"
        >
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white hidden" data-simplified-rut-target="spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Consultar Datos Fiscales</span>
        </button>
        
        <button 
          type="button"
          data-action="click->simplified-rut#clearAll"
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Limpiar todo
        </button>
      </div>
    </div>
    
    <!-- Formulario fiscal (oculto por defecto) -->
    <div class="mt-6 pt-6 border-t border-gray-200 hidden" data-simplified-rut-target="fiscalForm">
      <h3 class="text-lg font-medium mb-4">Datos Fiscales</h3>
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Razón Social</label>
            <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" data-simplified-rut-target="businessName">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Nombre Fantasía</label>
            <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" data-simplified-rut-target="tradingName">
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-2">
          <button 
            type="button"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            data-action="click->simplified-rut#saveFiscalData"
          >
            Guardar Datos
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
