<% if user_signed_in? %>
  <!-- Sidebar -->
  <aside class="bg-black text-white w-64 flex flex-col">
    <!-- Contenido del sidebar -->
  </aside>
<% end %>

<div class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md w-full">
  <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">Inicia sesión en tu cuenta</h1>

  <%= form_with url: main_session_path, scope: :session, class: "space-y-6" do |form| %>
    <div class="mb-6">
      <label for="session_email" class="form-field-label">Correo electrónico</label>
      <%= form.email_field :email, class: "form-field-input w-full", placeholder: '<EMAIL>', required: true %>
    </div>

    <div class="mb-2">
      <label for="session_password" class="form-field-label">Contraseña</label>
      <%= form.password_field :password, class: "form-field-input w-full", placeholder: '••••••••', required: true %>
      <p class="mt-1 text-sm text-gray-500">
        <%= link_to '¿Olvidaste tu contraseña?', '#', class: 'font-semibold text-blue-600 hover:text-blue-500 float-right' %>
      </p>
    </div>

    <div class="pt-2">
      <button type="submit" class="btn btn-primary w-full">
        Ingresar
      </button>
    </div>
  <% end %>

  <div class="mt-6 text-center text-sm">
    <p class="text-gray-600">
      ¿No tienes usuario aún?
      <%= link_to "Regístrate ahora", new_main_registration_path, class: "font-semibold text-blue-600 hover:text-blue-500" %>
    </p>
  </div>
</div>
