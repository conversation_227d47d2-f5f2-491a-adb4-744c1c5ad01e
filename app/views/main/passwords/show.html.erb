<div class="max-w-2xl mx-auto">
  <div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-2">Cambiar Contraseña</h1>
    <p class="text-sm text-gray-600">Actualiza tu contraseña para mantener tu cuenta segura.</p>
  </div>

  <%= form_with model: @user, url: password_path, method: :patch, scope: :user, class: 'w-full' do |form| %>
    <% if @user.errors.any? %>
      <div class="bg-red-50 p-4 rounded-md mb-6 border border-red-200">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Se encontraron <%= @user.errors.count %> errores:</h3>
            <div class="mt-2 text-sm text-red-700">
              <ul class="list-disc pl-5 space-y-1">
                <% @user.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-6">
      <!-- Contraseña actual -->
      <div>
        <label for="user_current_password" class="form-field-label">Contraseña Actual</label>
        <%= form.password_field :current_password, 
                              class: "form-field-input w-full",
                              required: true,
                              autocomplete: "current-password" %>
      </div>

      <!-- Nueva contraseña -->
      <div>
        <label for="user_password" class="form-field-label">Nueva Contraseña</label>
        <%= form.password_field :password, 
                              class: "form-field-input w-full",
                              required: true,
                              autocomplete: "new-password" %>
        <p class="mt-1 text-xs text-gray-500">Mínimo 8 caracteres</p>
      </div>

      <!-- Confirmación de contraseña -->
      <div>
        <label for="user_password_confirmation" class="form-field-label">Confirmar Nueva Contraseña</label>
        <%= form.password_field :password_confirmation, 
                              class: "form-field-input w-full",
                              required: true,
                              autocomplete: "new-password" %>
      </div>
    </div>

    <div class="flex flex-col sm:flex-row justify-between gap-4 mt-8 pt-6 border-t border-gray-200">
      <%= link_to profile_path, class: "btn btn-secondary w-full sm:w-auto" do %>
        <i class="ri-arrow-left-line mr-2"></i>Volver al Perfil
      <% end %>

      <button type="submit" class="btn btn-primary w-full sm:w-auto">
        <i class="ri-lock-password-line mr-2"></i>Actualizar Contraseña
      </button>
    </div>
  <% end %>
</div>
