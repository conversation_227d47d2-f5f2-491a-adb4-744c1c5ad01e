<% if user_signed_in? %>
  <!-- Sidebar -->
  <aside class="bg-black text-white w-64 flex flex-col">
    <!-- Contenido del sidebar -->
  </aside>
<% end %>

<div class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md w-full">
  <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">Registro de nueva cuenta</h1>

  <% if @user&.persisted? && @user&.client? && @user&.organizations.any? %>
    <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-6 rounded">
      <h3 class="font-medium">Activación de cuenta existente</h3>
      <p class="mt-2">Hemos detectado que ya estás registrado como cliente en nuestro sistema. Al completar este formulario, activarás tu acceso a la plataforma y podrás ver la información compartida por las organizaciones con las que trabajas.</p>

      <% if @user.organizations.count > 0 %>
        <p class="mt-2">Estás asociado a las siguientes organizaciones:</p>
        <ul class="list-disc list-inside mt-1 ml-2">
          <% @user.organizations.each do |org| %>
            <li><%= org.name %></li>
          <% end %>
        </ul>
      <% end %>
    </div>
  <% end %>

  <%= form_with url: main_registration_path, scope: :user, method: :post do |form| %>
    <% if @user&.errors&.any? %>
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded">
        <% @user&.errors&.full_messages&.each do |message| %>
          <p><%= message %></p>
        <% end %>
      </div>
    <% end %>

    <div class="space-y-6">
      <!-- Información de acceso -->
      <div class="mb-4">
        <label for="user_email" class="form-field-label">Correo electrónico</label>
        <%= form.email_field :email, required: true, class: "form-field-input w-full", placeholder: '<EMAIL>' %>
      </div>

      <div class="mb-4">
        <label for="user_password" class="form-field-label">Contraseña</label>
        <%= form.password_field :password, required: true, class: "form-field-input w-full", placeholder: '••••••••' %>
      </div>

      <div class="mb-4">
        <label for="user_password_confirmation" class="form-field-label">Confirmación de Contraseña</label>
        <%= form.password_field :password_confirmation, required: true, class: "form-field-input w-full", placeholder: '••••••••' %>
      </div>

      <div class="pt-2">
        <button type="submit" class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition-colors duration-200">
          <% if @user&.persisted? && @user&.client? %>
            Activar cuenta
          <% else %>
            Crear cuenta
          <% end %>
        </button>
      </div>
    </div>

    <% if @user&.persisted? && @user&.client? %>
      <p class="mt-6 text-sm text-gray-600 text-center">Al activar tu cuenta, podrás acceder a la información compartida por las organizaciones con las que trabajas.</p>
    <% else %>
      <p class="mt-6 text-sm text-gray-600 text-center">Al registrarse, se creará automáticamente una organización asociada a su cuenta. Los datos de facturación se vincularán exclusivamente a esta organización y podrá completarlos desde su perfil.</p>
    <% end %>

    <div class="mt-6 text-center text-sm">
      <p class="text-gray-600">
        ¿Ya tienes una cuenta?
        <%= link_to "Inicia sesión", new_main_session_path, class: "font-semibold text-primary-600 hover:text-primary-500" %>
      </p>
    </div>
  <% end %>
</div>
