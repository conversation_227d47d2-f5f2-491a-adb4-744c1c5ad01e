<%#
  Actions Dropdown - Reusable component for action buttons in datatables
  
  Parameters:
  - show_path: Path for the show action
  - edit_path: Path for the edit action
  - delete_path: Path for the delete action (will use data-turbo-method="delete")
  - toggle_access_path: Optional path for toggling access
  - toggle_access_text: Text for the toggle access button
  - show_actions: <PERSON><PERSON>an to show/hide all actions (default: true)
  - show_edit: Boolean to show/hide edit button (default: true)
  - show_delete: Boolean to show/hide delete button (default: true)
  - show_toggle_access: Boolean to show/hide toggle access button (default: false)
  - show_show: Boolean to show/hide show button (default: true)
  - show_dropdown: <PERSON>olean to wrap actions in a dropdown menu (default: true)
%>

<% show_actions = local_assigns.fetch(:show_actions, true) %>
<% show_edit = local_assigns.fetch(:show_edit, true) %>
<% show_delete = local_assigns.fetch(:show_delete, true) %>
<% show_show = local_assigns.fetch(:show_show, true) %>
<% show_toggle_access = local_assigns.fetch(:show_toggle_access, false) %>
<% show_dropdown = local_assigns.fetch(:show_dropdown, true) %>

<div class="flex items-center space-x-2">
  <% if show_actions && show_show && show_path.present? %>
    <%= link_to show_path, 
                class: "btn btn-sm btn-icon btn-secondary",
                title: "Ver detalles",
                data: { turbo_frame: "_top" } do %>
      <i class="ri-eye-line"></i>
    <% end %>
  <% end %>

  <% if show_dropdown && (show_edit || show_delete || show_toggle_access) %>
    <div class="relative inline-block text-left" data-controller="dropdown">
      <div>
        <button type="button" 
                class="btn btn-sm btn-icon" 
                id="menu-button" 
                aria-expanded="true" 
                aria-haspopup="true"
                data-action="click->dropdown#toggle">
          <span class="sr-only">Abrir opciones</span>
          <i class="ri-more-2-fill"></i>
        </button>
      </div>

      <div class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none py-1" 
           role="menu" 
           aria-orientation="vertical" 
           aria-labelledby="menu-button" 
           tabindex="-1"
           data-dropdown-target="menu">
        <div class="py-1" role="none">
          <% if show_edit && edit_path.present? %>
            <%= link_to edit_path, 
                        class: 'text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left',
                        role: 'menuitem',
                        tabindex: '-1',
                        data: { turbo_frame: "_top" } do %>
              <i class="ri-pencil-line mr-2"></i> Editar
            <% end %>
          <% end %>

          <% if show_toggle_access && toggle_access_path.present? %>
            <%= link_to toggle_access_path,
                        method: :patch,
                        class: 'text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left',
                        role: 'menuitem',
                        tabindex: '-1',
                        data: { turbo_method: :patch, turbo_confirm: "¿Estás seguro de que deseas cambiar el estado de acceso?" } do %>
              <i class="ri-toggle-<%= local_assigns[:toggle_access_text] == 'Desactivar' ? 'line' : 'fill' %> mr-2"></i> 
              <%= local_assigns[:toggle_access_text] %> acceso
            <% end %>
          <% end %>

          <% if show_delete && delete_path.present? %>
            <%= button_to delete_path, 
                          method: :delete, 
                          class: 'text-red-600 block w-full text-left px-4 py-2 text-sm hover:bg-red-50',
                          role: 'menuitem',
                          tabindex: '-1',
                          form: { 
                            class: 'inline-block w-full',
                            data: { 
                              turbo_confirm: '¿Estás seguro de que deseas eliminar este registro? Esta acción no se puede deshacer.',
                              turbo_frame: '_top'
                            }
                          } do %>
              <i class="ri-delete-bin-line mr-2"></i> Eliminar
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  <% else %>
    <% if show_edit && edit_path.present? %>
      <%= link_to edit_path, 
                  class: "text-indigo-600 hover:text-indigo-900",
                  title: "Editar",
                  data: { turbo_frame: "_top" } do %>
        <i class="ri-edit-line text-lg"></i>
      <% end %>
    <% end %>

    <% if show_toggle_access && toggle_access_path.present? %>
      <%= link_to toggle_access_path,
                  method: :patch,
                  class: "text-indigo-600 hover:text-indigo-900 ml-2",
                  title: local_assigns[:toggle_access_text],
                  data: { turbo_method: :patch, turbo_confirm: "¿Estás seguro de que deseas cambiar el estado de acceso?" } do %>
        <i class="ri-toggle-<%= local_assigns[:toggle_access_text] == 'Desactivar' ? 'line' : 'fill' %> text-lg"></i>
      <% end %>
    <% end %>

    <% if show_delete && delete_path.present? %>
      <%= link_to delete_path,
                  class: "text-red-600 hover:text-red-900 ml-2",
                  title: "Eliminar",
                  data: { turbo_frame: "_top" } do %>
        <i class="ri-delete-bin-line text-lg"></i>
      <% end %>
    <% end %>
  <% end %>
</div>
