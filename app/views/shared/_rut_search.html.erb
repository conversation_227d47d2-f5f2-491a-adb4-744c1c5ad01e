<%# Componente de búsqueda de RUT con Hotwire (Turbo + Stimulus) %>
<%= form_with(url: search_clients_path, 
             method: :get, 
             id: "rut-search-form",
             data: { 
               controller: "rut",
               turbo_frame: "search-results",
               action: "turbo:submit-start->rut#handleSubmit"
             }) do |f| %>
  
  <div class="w-full max-w-md">
    <div class="relative">
      <div class="flex">
        <%# Input de búsqueda con icono integrado %>
        <div class="relative flex-grow">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <%= inline_svg_tag("icons/identification.svg", class: "h-5 w-5 text-gray-400") %>
          </div>
          
          <%# Input principal con validación de RUT %>
          <%= text_field_tag :rut, 
                           params[:rut], 
                           class: "form-field-input pl-10",
                           placeholder: "Ej: 12.345.678-9",
                           autocomplete: "off",
                           spellcheck: "false",
                           data: {
                             rut_target: "input",
                             action: "input->rut#formatRut"
                           } %>
                            
          <%# Botón para limpiar (se muestra dinámicamente con Stimulus) %>
          <button type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  data-action="click->rut#clear"
                  data-rut-target="clearButton">
            <span class="text-gray-400 hover:text-gray-600">
              <%= inline_svg_tag("icons/x.svg", class: "h-4 w-4") %>
            </span>
          </button>
        </div>
        
        <%# Botón de búsqueda %>
        <button type="submit" 
                class="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
          <%= inline_svg_tag("icons/search.svg", class: "h-5 w-5") %>
        </button>
      </div>
      
      <%# Mensajes de validación se insertarán aquí automáticamente %>
    </div>
  </div>
  
  <%# Campo oculto para el formulario %>
  <%= hidden_field_tag :search_type, "rut" %>
<% end %>
