<%# 
  Componente de tarjeta estandarizada
  
  Uso:
  = card title: 'Títu<PERSON> de la tarjeta', subtitle: 'Subtítulo opcional' do
    p Contenido de la tarjeta
  
  Opciones:
  - title: Tí<PERSON><PERSON> de la tarjeta (opcional)
  - subtitle: <PERSON><PERSON><PERSON><PERSON><PERSON> (opcional)
  - padding: Clases de padding (por defecto: 'p-6')
  - extra_header_content: Contenido adicional para el encabezado (opcional)
  - full_width: Si es true, ocupa todo el ancho disponible
  - html_options: Hash con atributos HTML adicionales para el contenedor
  - content: Contenido de la tarjeta (alternativa al bloque)
%>

<% 
  padding_classes = local_assigns[:padding] || 'p-6'
  full_width_class = local_assigns[:full_width] ? 'w-full' : ''
  
  # Clases base para el contenedor
  container_classes = [
    'bg-white shadow-md rounded-lg overflow-hidden border border-gray-200',
    full_width_class,
    local_assigns.dig(:html_options, :class)
  ].compact.join(' ')
  
  # Eliminar la clase de html_options para evitar duplicados
  html_options = local_assigns[:html_options] || {}
  html_options = html_options.except(:class) if html_options.key?(:class)
%>

<%= content_tag :div, class: container_classes, **html_options do %>
  <%# Encabezado de la tarjeta (opcional) %>
  <% if local_assigns[:title] || local_assigns[:extra_header_content] %>
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <div>
          <% if local_assigns[:title] %>
            <h3 class="text-lg font-medium text-gray-900"><%= local_assigns[:title] %></h3>
          <% end %>
          <% if local_assigns[:subtitle] %>
            <p class="mt-1 text-sm text-gray-500"><%= local_assigns[:subtitle] %></p>
          <% end %>
        </div>
        
        <% if local_assigns[:extra_header_content] %>
          <div class="flex-shrink-0">
            <%= local_assigns[:extra_header_content] %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
  
  <%# Contenido de la tarjeta %>
  <div class="<%= padding_classes %>">
    <% content = local_assigns[:content] || yield if block_given? %>
    <% if local_assigns[:full_width] %>
      <%= content.try(:html_safe) || content %>
    <% else %>
      <div class="max-w-3xl mx-auto w-full">
        <%= content.try(:html_safe) || content %>
      </div>
    <% end %>
  </div>
<% end %>
