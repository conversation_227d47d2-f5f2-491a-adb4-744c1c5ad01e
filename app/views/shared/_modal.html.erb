<%# Componente de Modal reutilizable %>
<div data-controller="modal" class="fixed inset-0 flex items-center justify-center z-50 hidden" aria-modal="true">
  <%# Overlay/Backdrop %>
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" data-action="click->modal#close"></div>
  
  <%# Contenedor del modal %>
  <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-auto overflow-hidden transform transition-all z-10">
    <%# Encabezado del modal %>
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900" data-modal-target="title">
        <%= title || "Modal" %>
      </h3>
      <button type="button" data-action="modal#close" class="text-gray-400 hover:text-gray-500">
        <span class="sr-only">Cerrar</span>
        <i class="ri-close-line text-xl"></i>
      </button>
    </div>
    
    <%# Contenido del modal - será reemplazado con la respuesta AJAX %>
    <div data-modal-target="content" class="px-6 py-4">
      <%= content %>
    </div>
    
    <%# Pie del modal con botones de acción %>
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button type="button" data-action="modal#close" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Cancelar
      </button>
      <% if local_assigns[:show_confirm] %>
        <button type="button" data-action="<%= confirm_action if local_assigns[:confirm_action] %>" class="btn btn-primary">
          <%= confirm_text || "Confirmar" %>
        </button>
      <% end %>
    </div>
  </div>
</div>
