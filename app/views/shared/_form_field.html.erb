<%# 
  Este es un componente de campo de formulario estandarizado. Ver documentación en UI_DESIGN_GUIDELINES.md 
%>

<% 
  # Valores por defecto
  input_type = local_assigns[:input_type]&.to_s || 'text'
  required = local_assigns[:required] || false
  autofocus = local_assigns[:autofocus] || false
  disabled = local_assigns[:disabled] || false
  readonly = local_assigns[:readonly] || false
  field_name = field.to_s
  model = form.object

  # ID del input
  input_id = "#{field_name}_#{Time.current.to_i}"

  # Manejo de errores
  error = nil
  if model.present? && model.respond_to?(:errors)
    error = model.errors[field].first
    # Generar un ID más específico para formularios con modelo
    if model.respond_to?(:model_name)
      input_id = "#{model.model_name.singular}_#{field_name}"
    end
  end
  
  # Clases base
  wrapper_class = ["form-field-container", local_assigns[:wrapper_class] || "mb-4"].join(' ').strip
  label_class = ["form-field-label block mb-1.5 text-sm font-medium text-gray-700", 
                ('form-field-required' if required), 
                local_assigns[:label_class]].compact.join(' ').strip
  
  # Clases para el input - usando la clase estandarizada form-field-input definida en components/form_fields.css
  input_base_classes = 'form-field-input w-full'
  input_classes = [input_base_classes, 
                  ('form-field-input-error' if error),
                  local_assigns[:input_class]].compact.join(' ').strip
  
  # Clases para el wrapper del input
  input_wrapper_class = local_assigns[:input_wrapper_class] || 'relative'
  
  # Clases para mensajes
  error_class = local_assigns[:error_class] || 'mt-1 text-sm text-red-600'
  hint_class = local_assigns[:hint_class] || 'mt-1 text-sm text-gray-500'
  
  # Atributos comunes para inputs
  input_attributes = {
    class: input_classes,
    required: required,
    disabled: disabled,
    readonly: readonly,
    autofocus: autofocus,
    disabled: disabled,
    readonly: readonly,
    id: input_id,
    placeholder: local_assigns[:placeholder],
    data: local_assigns[:data] || {}
  }.compact
%>

<div class="<%= wrapper_class %>">
  <% if local_assigns[:label] != false %>
    <% if model.present? %>
      <%= form.label field, label, class: label_class %>
    <% else %>
      <label for="<%= input_id %>" class="<%= label_class %>"><%= label %></label>
    <% end %>
  <% end %>
  
  <div class="<%= input_wrapper_class %>">
    <% case input_type.to_sym %>
    <% when :select %>
      <% 
        select_options = {
          include_blank: local_assigns[:include_blank],
          class: "form-field-select #{local_assigns[:select_class]}".strip,
          required: required,
          autofocus: autofocus,
          disabled: disabled,
          id: input_id
        }.merge(local_assigns[:select_html] || {})
      %>
      <%= form.select field, local_assigns[:options], 
                     { include_blank: local_assigns[:include_blank] },
                     select_options %>
    <% when :text_area %>
      <% 
        textarea_attributes = input_attributes.merge({
          class: "#{input_classes} textarea",
          rows: local_assigns[:rows] || 4
        })
      %>
      <%= form.text_area field, textarea_attributes %>
    <% when :check_box %>
      <div class="flex items-center">
        <% 
          checkbox_attributes = {
            class: "form-field-checkbox",
            id: input_id,
            required: required,
            disabled: disabled,
            data: input_attributes[:data]
          }.compact
        %>
        <%= form.check_box field, checkbox_attributes %>
        <% if local_assigns[:check_box_label] %>
          <label for="<%= input_id %>" class="ml-2 block text-sm text-gray-900">
            <%= local_assigns[:check_box_label] %>
          </label>
        <% end %>
      </div>
    <% else %>
      <% 
        # Para inputs estándar, actualizamos las clases y atributos
        input_attributes[:type] = input_type unless input_type == 'text'
        
        case input_type.to_sym 
      %>
      <% when :tel, 'tel' %>
        <%= form.telephone_field(field, input_attributes) %>
      <% when :email, 'email' %>
        <%= form.email_field(field, input_attributes) %>
      <% when :password, 'password' %>
        <%= form.password_field(field, input_attributes) %>
      <% when :number, 'number' %>
        <%= form.number_field(field, input_attributes) %>
      <% when :date, 'date' %>
        <%= form.date_field(field, input_attributes) %>
      <% when :datetime, 'datetime' %>
        <%= form.datetime_field(field, input_attributes) %>
      <% when :time, 'time' %>
        <%= form.time_field(field, input_attributes) %>
      <% when :url, 'url' %>
        <%= form.url_field(field, input_attributes) %>
      <% when :search, 'search' %>
        <% input_attributes[:class] = input_attributes[:class].to_s.gsub(/form-field-input/, 'search-input') %>
        <%= form.search_field(field, input_attributes) %>
      <% when :color, 'color' %>
        <%= form.color_field(field, input_attributes) %>
      <% when :range, 'range' %>
        <%= form.range_field(field, input_attributes) %>
      <% when :file, 'file' %>
        <%= form.file_field(field, input_attributes) %>
      <% else %>
        <%= form.text_field(field, input_attributes) %>
      <% end %>
    <% end %>
  </div>
  
  <% if local_assigns[:hint] %>
    <p class="<%= hint_class %>"><%= hint %></p>
  <% end %>
  
  <% if error %>
    <p class="<%= error_class %>"><%= error %></p>
  <% end %>
</div>
