<%# 
  Componente de alerta estandarizado
  
  Uso:
  render 'shared/alert', 
         type: 'success', 
         title: '¡Éxito!', 
         message: 'La operación se completó correctamente'
  
  Tipos disponibles:
  - success: Operación exitosa
  - error: Error o fallo
  - warning: Advertencia
  - info: Información
%>

<% 
  alert_classes = case type.to_sym
                  when :success
                    'bg-green-50 border-green-400 text-green-700'
                  when :error
                    'bg-red-50 border-red-400 text-red-700'
                  when :warning
                    'bg-yellow-50 border-yellow-400 text-yellow-700'
                  when :info
                    'bg-blue-50 border-blue-400 text-blue-700'
                  else
                    'bg-gray-50 border-gray-400 text-gray-700'
                  end
  
  icon_classes = case type.to_sym
                  when :success
                    'text-green-400'
                  when :error
                    'text-red-400'
                  when :warning
                    'text-yellow-400'
                  when :info
                    'text-blue-400'
                  else
                    'text-gray-400'
                  end
  
  icon_name = case type.to_sym
               when :success
                 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
               when :error
                 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
               when :warning
                 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
               when :info
                 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
               else
                 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
               end
%>

<div class="rounded-md p-4 mb-4 border-l-4 <%= alert_classes %>" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 <%= icon_classes %>" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="<%= icon_name %>" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <% if local_assigns[:title].present? %>
        <h3 class="text-sm font-medium"><%= local_assigns[:title] %></h3>
      <% end %>
      
      <% if local_assigns[:message].present? %>
        <div class="mt-2 text-sm">
          <p><%= local_assigns[:message] %></p>
        </div>
      <% end %>
      
      <% if local_assigns[:details] %>
        <div class="mt-2 text-sm">
          <pre class="whitespace-pre-wrap"><code class="text-xs"><%= details %></code></pre>
        </div>
      <% end %>
    </div>
  </div>
</div>
