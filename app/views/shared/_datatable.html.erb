<!--
DataTable - Componente reutilizable para mostrar datos tabulares con funcionalidades avanzadas

Este componente proporciona una tabla de datos con funcionalidades de búsqueda, filtrado,
ordenación, paginación, selección múltiple y exportación de datos.

Para que la tabla ocupe todo el ancho de la pantalla, agrega en tu vista:
content_for :full_width, true

Parámetros:
- columns: Array de hashes con las columnas a mostrar. Cada hash debe tener:
  - label: Etiqueta a mostrar en el encabezado
  - field: Campo a mostrar (puede ser un método o un atributo)
  - sortable: Booleano que indica si la columna es ordenable (opcional, por defecto true)
  - filterable: Booleano que indica si la columna es filtrable (opcional)
  - filter_options: Array de opciones para el filtro (opcional)
  - width: Ancho de la columna (opcional, ej: '20%', '200px')
  - align: Alineación del texto (opcional, por defecto 'left')
  - format: Proc para formatear el valor (opcional)

- collection: Colección de objetos a mostrar
- search_enabled: Booleano que indica si se debe mostrar el campo de búsqueda (opcional, por defecto true)
- filters_enabled: Booleano que indica si se deben mostrar los filtros (opcional, por defecto true)
- pagination_enabled: Booleano que indica si se debe mostrar la paginación (opcional, por defecto true)
- per_page: Número de elementos por página (opcional, por defecto 10)
- actions: Hash con acciones a mostrar en cada fila (opcional). Cada acción debe tener:
  - label: Etiqueta a mostrar
  - path: Proc que recibe el objeto y devuelve la ruta
  - icon: Nombre del icono a mostrar (opcional)
  - method: Método HTTP a usar (opcional, por defecto 'get')
  - confirm: Mensaje de confirmación (opcional)
  - condition: Proc que recibe el objeto y devuelve un booleano (opcional)
- selection_enabled: Booleano que indica si se debe mostrar la selección múltiple (opcional, por defecto false)
- export_enabled: Booleano que indica si se debe mostrar la exportación de datos (opcional, por defecto false)
- table_id: Identificador único para la tabla (opcional, para guardar preferencias)
- theme: Hash con opciones de tema (opcional). Puede incluir:
  - header_bg: Color de fondo del encabezado (opcional, por defecto 'bg-gray-50')
  - header_text: Color del texto del encabezado (opcional, por defecto 'text-gray-500')
  - row_bg: Color de fondo de las filas (opcional, por defecto 'bg-white')
  - row_hover: Color de fondo al pasar el mouse (opcional, por defecto 'hover:bg-gray-50')
  - primary_button: Clases para botones primarios (opcional)
  - secondary_button: Clases para botones secundarios (opcional)
-->

<%
  # Valores por defecto
  search_enabled = local_assigns.fetch(:search_enabled, true)
  filters_enabled = local_assigns.fetch(:filters_enabled, true)
  pagination_enabled = local_assigns.fetch(:pagination_enabled, true)
  per_page = local_assigns.fetch(:per_page, 10)
  actions = local_assigns.fetch(:actions, {})
  selection_enabled = local_assigns.fetch(:selection_enabled, false)
  export_enabled = local_assigns.fetch(:export_enabled, false)
  table_id = local_assigns.fetch(:table_id, "datatable_#{SecureRandom.hex(4)}")

  # Opciones de tema
  theme = local_assigns.fetch(:theme, {})
  header_bg = theme[:header_bg] || "bg-gray-50"
  header_text = theme[:header_text] || "text-gray-500"
  row_bg = theme[:row_bg] || "bg-white"
  row_hover = theme[:row_hover] || "hover:bg-gray-50"
  primary_button = theme[:primary_button] || "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
  secondary_button = theme[:secondary_button] || "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"

  # Verificar si hay columnas ordenables
  has_sortable = columns.any? { |col| col[:sortable] }
%>

<div data-controller="datatable"
     data-datatable-per-page-value="<%= per_page %>"
     data-datatable-current-page-value="1"
     data-datatable-table-id-value="<%= table_id %>"
     data-action="datatable:selectionChanged->datatable#handleSelectionChanged"
     class="bg-white shadow-md rounded-lg overflow-hidden w-full">

  <!-- Cabecera con búsqueda y exportación -->
  <div class="px-6 py-4 border-b border-gray-200">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
      <!-- Título y búsqueda -->
      <div class="flex-1 min-w-0">
        <% if search_enabled %>
          <div class="max-w-md">
            <label for="datatable-search-<%= table_id %>" class="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
            <div class="relative" data-controller="search" data-search-search-in-value="both">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i class="ri-search-line text-gray-400"></i>
              </div>
              <input type="search"
                     id="datatable-search-<%= table_id %>"
                     data-search-target="input"
                     data-action="input->search#filter keydown->search#handleKeydown"
                     placeholder="Buscar..."
                     class="search-input pl-10 pr-4 py-2 w-full"
                     autocomplete="off">
              <button type="button" 
                      class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                      data-action="click->search#clear"
                      data-search-target="clearButton"
                      title="Limpiar búsqueda">
                <i class="ri-close-line"></i>
              </button>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Acciones y exportación -->
      <div class="flex items-center space-x-2">
        <% if export_enabled %>
          <div class="relative inline-block text-left" data-controller="dropdown">
            <div>
              <button type="button"
                      class="<%= secondary_button %> flex items-center"
                      data-action="dropdown#toggle"
                      aria-expanded="false"
                      aria-haspopup="true">
                <i class="ri-download-2-line -ml-1 mr-2 h-5 w-5"></i>
                Exportar
                <i class="ri-arrow-down-s-line -mr-1 ml-2 h-5 w-5"></i>
              </button>
            </div>
            <div class="hidden origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical"
                 aria-labelledby="menu-button"
                 tabindex="-1">
              <div class="py-1" role="none">
                <button type="button"
                        class="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                        data-action="datatable#exportCSV"
                        role="menuitem"
                        tabindex="-1">
                  Exportar a CSV
                </button>
                <button type="button"
                        class="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                        data-action="datatable#exportExcel"
                        role="menuitem"
                        tabindex="-1">
                  Exportar a Excel
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Tabla -->
  <div class="table-sticky-container">
      <table data-datatable-target="table" class="table-with-sticky-column divide-y divide-gray-200">
      <thead class="<%= header_bg %>">
        <!-- Encabezados de columna con ordenación -->
        <tr>
          <% if selection_enabled %>
            <th scope="col" class="px-4 py-3 w-10 checkbox-column" rowspan="2">
              <div class="flex items-center">
                <input type="checkbox"
                       data-datatable-target="checkAll"
                       data-action="change->datatable#toggleSelectAll"
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
              </div>
            </th>
          <% end %>

          <% columns.each do |column| %>
            <%
              # Determinar si la columna es ordenable (por defecto, todas son ordenables a menos que se especifique lo contrario)
              is_sortable = column.key?(:sortable) ? column[:sortable] : true
            %>
            <th scope="col"
                <% if is_sortable %>
                data-datatable-target="sortHeader"
                data-sort-column="<%= column[:field].is_a?(Proc) ? column[:field].object_id.to_s : column[:field] %>"
                data-action="click->datatable#sort"
                <% end %>
                class="px-6 py-3 text-xs font-medium <%= header_text %> uppercase tracking-wider <%= column[:align] == 'right' ? 'text-right' : column[:align] == 'center' ? 'text-center' : 'text-left' %> <%= is_sortable ? 'cursor-pointer hover:bg-gray-100' : '' %>"
                style="<%= column[:width] ? "width: #{column[:width]};" : "" %>">
              <div class="flex items-center space-x-1 <%= column[:align] == 'right' ? 'justify-end' : column[:align] == 'center' ? 'justify-center' : 'justify-start' %>">
                <span><%= column[:label] %></span>
                <% if is_sortable %>
                  <span class="sort-icon">
                    <i class="ri-arrow-up-down-line h-4 w-4 text-gray-400"></i>
                  </span>
                <% end %>
              </div>
            </th>
          <% end %>

          <% if actions.present? %>
            <th scope="col" 
                class="px-6 py-3 text-left text-xs font-medium <%= header_text %> uppercase tracking-wider sticky-column-header">
              <div class="flex items-center space-x-1">
                <span>Acciones</span>
              </div>
            </th>
          <% end %>
        </tr>

        <!-- Fila de filtros eliminada -->
      </thead>
      <tbody data-datatable-target="body" class="<%= row_bg %> divide-y divide-gray-200">
        <% if collection.any? %>
          <% collection.each do |item| %>
            <%
              # Preparar datos para búsqueda, filtros y ordenación
              searchable_values = []
              filter_data = {}
              sort_data = {}

              columns.each do |column|
                # Obtener el valor de la columna
                raw_value = column[:field].is_a?(Proc) ? column[:field].call(item) : item.send(column[:field])

                # Formatear el valor si hay un formateador
                display_value = column[:format] ? column[:format].call(raw_value) : raw_value

                # Convertir a string y limpiar
                string_value = display_value.to_s.strip

                # Agregar a los valores de búsqueda
                searchable_values << string_value

                # Generar nombre de campo
                field_name = column[:field].is_a?(Proc) ? column[:field].object_id.to_s : column[:field]

                # Datos para filtros
                if column[:filterable]
                  # Para filtros, usar el valor mostrado (después de formatear)
                  filter_data[field_name] = string_value
                end

                # Datos para ordenación (por defecto, todas las columnas son ordenables a menos que se especifique lo contrario)
                is_sortable = column.key?(:sortable) ? column[:sortable] : true
                if is_sortable
                  # Valor para ordenación (puede ser diferente del valor mostrado)
                  sort_value = column[:sort_value] ? column[:sort_value].call(item) : raw_value.to_s

                  # Si es un número, asegurarse de que se ordene como número
                  if sort_value.to_s =~ /\A[+-]?\d+(\.\d+)?\z/
                    sort_data["sort#{field_name}"] = sort_value.to_s.rjust(20, '0')
                  else
                    sort_data[field_name] = sort_value.to_s.downcase
                  end
                end
              end

              # Crear texto de búsqueda combinando todos los valores
              searchable_text = searchable_values.join(" ").gsub(/\s+/, " ").strip
            %>
            <tr data-datatable-target="row"
                data-searchable-text="<%= searchable_text.downcase %>"
                data-filtered="false"
                class="<%= row_hover %>"
                <% filter_data.each do |key, value| %>
                  data-<%= key %>=<%= value %>
                <% end %>
                <% sort_data.each do |key, value| %>
                  data-<%= key %>=<%= value %>
                <% end %>>


              <% if selection_enabled %>
                <td class="px-4 py-4 whitespace-nowrap w-10 checkbox-cell">
                  <div class="flex items-center">
                    <input type="checkbox"
                           data-datatable-target="checkRow"
                           data-action="change->datatable#handleRowCheckboxChange"
                           data-item-id="<%= item.id %>"
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                  </div>
                </td>
              <% end %>

              <% columns.each do |column| %>
                <td class="px-6 py-4 whitespace-nowrap text-sm <%= column[:align] == 'right' ? 'text-right' : column[:align] == 'center' ? 'text-center' : 'text-left' %>">
                  <%
                    value = column[:field].is_a?(Proc) ? column[:field].call(item) : item.send(column[:field])
                    formatted_value = column[:format] ? column[:format].call(value) : value
                  %>
                  <%= formatted_value %>
                </td>
              <% end %>

              <% if actions.present? %>
                <td class="px-6 py-4 whitespace-nowrap text-sm sticky-column-cell">
                  <div class="flex items-center space-x-3">
                    <% actions.each do |action_name, action| %>
                      <% if !action[:condition] || action[:condition].call(item) %>
                        <%
                          path = action[:path].is_a?(Proc) ? action[:path].call(item) : action[:path]
                          method = action[:method] || 'get'
                          confirm = action[:confirm]
                          btn_class = action[:class] || "text-indigo-600 hover:text-indigo-900"
                        %>

                        <% if method == 'get' %>
                          <%= link_to path, class: "#{btn_class} p-1 hover:bg-gray-100 rounded-md flex items-center justify-center", title: action[:label] do %>
                            <% if action[:icon] %>
                              <i class="<%= action[:icon] %> " title="<%= action[:label] %>"></i>
                            <% else %>
                              <span class="text-sm"><%= action[:label] %></span>
                            <% end %>
                          <% end %>
                        <% else %>
                          <%= button_to path,
                                      method: method,
                                      class: "#{btn_class} p-1 hover:bg-gray-100 rounded-md flex items-center justify-center",
                                      title: action[:label],
                                      form: { class: "inline" },
                                      data: { turbo_confirm: confirm } do %>
                            <% if action[:icon] %>
                              <i class="<%= action[:icon] %> " title="<%= action[:label] %>"></i>
                            <% else %>
                              <span class="text-sm"><%= action[:label] %></span>
                            <% end %>
                          <% end %>
                        <% end %>
                      <% end %>
                    <% end %>
                  </div>
                </td>
              <% end %>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="<%= columns.size + (actions.present? ? 1 : 0) %>" class="px-6 py-4 text-center text-sm text-gray-500">
              No hay elementos para mostrar
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Mensaje de no resultados -->
  <div data-datatable-target="noResults" class="hidden px-6 py-4 text-center text-sm text-gray-500">
    No se encontraron resultados que coincidan con los criterios de búsqueda
  </div>

  <!-- Paginación y resumen -->
  <% if pagination_enabled %>
    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
      <div class="flex-1 flex justify-between sm:hidden">
        <button type="button"
                data-datatable-target="prevButton"
                data-action="click->datatable#prevPage"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
          Anterior
        </button>
        <button type="button"
                data-datatable-target="nextButton"
                data-action="click->datatable#nextPage"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
          Siguiente
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700" data-datatable-target="paginationInfo">
            Mostrando <span class="font-medium" data-datatable-target="startItem">1</span> a
            <span class="font-medium" data-datatable-target="endItem">10</span> de
            <span class="font-medium" data-datatable-target="totalItems"><%= collection.size %></span> resultados
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button type="button"
                    data-datatable-target="firstPageButton"
                    data-action="click->datatable#firstPage"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Primera página</span>
              <i class="ri-arrow-left-double-line h-5 w-5"></i>
            </button>
            <button type="button"
                    data-datatable-target="prevPageButton"
                    data-action="click->datatable#prevPage"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Anterior</span>
              <i class="ri-arrow-left-s-line h-5 w-5"></i>
            </button>
            <div class="flex items-center px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-700">
              Página <span data-datatable-target="currentPage" class="mx-1 font-semibold">1</span>
              de <span data-datatable-target="totalPages" class="ml-1">1</span>
            </div>
            <button type="button"
                    data-datatable-target="nextPageButton"
                    data-action="click->datatable#nextPage"
                    class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Siguiente</span>
              <i class="ri-arrow-right-s-line h-5 w-5"></i>
            </button>
            <button type="button"
                    data-datatable-target="lastPageButton"
                    data-action="click->datatable#lastPage"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Última página</span>
              <i class="ri-arrow-right-double-line h-5 w-5"></i>
            </button>
          </nav>
        </div>
      </div>
    </div>
  <% end %>
</div>
