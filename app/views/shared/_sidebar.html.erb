<aside
  id="sidebar"
  class="bg-black text-white flex flex-col overflow-y-auto shadow-xl"
  style="position: fixed; top: 0; left: 0; width: 16rem; height: 100vh; z-index: 50; transform: translateX(-100%); transition: transform 0.3s ease-in-out;"
  aria-hidden="true">
  <!-- Bot<PERSON> de cierre -->
  <button
    onclick="toggleSidebar(); return false;"
    class="absolute top-4 right-4 text-white p-2 rounded-full bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50"
    aria-label="Cerrar menú"
    aria-expanded="false"
    aria-controls="sidebar"
    id="sidebar-close-button"
  >
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  </button>

  <!-- Navigation -->
  <nav class="overflow-y-auto overflow-x-hidden flex-grow">
    <ul class="flex flex-col py-4 space-y-1">
      <li class="px-5">
        <div class="flex flex-row items-center h-8">
          <div class="text-sm font-light tracking-wide text-gray-500">Menu</div>
        </div>
      </li>
      <li>
        <%= link_to dashboard_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6 #{'bg-gray-800 text-white border-indigo-500' if current_page?(dashboard_path)}" do %>
          <span class="inline-flex justify-center items-center ml-4">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>
          </span>
          <span class="ml-2 text-sm tracking-wide truncate">Dashboard</span>
        <% end %>
      </li>

      <!-- Mi Cuenta -->
      <li class="px-5 mt-4">
        <div class="flex flex-row items-center h-8">
          <div class="text-sm font-light tracking-wide text-gray-500">Mi Cuenta</div>
        </div>
      </li>
      <li>
        <%= link_to client_profile_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
          <span class="inline-flex justify-center items-center ml-4">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </span>
          <span class="ml-2 text-sm tracking-wide truncate">Mi Perfil</span>
        <% end %>
      </li>

      <!-- Organizaciones -->
      <li class="px-5 mt-4">
        <div class="flex flex-row items-center h-8">
          <div class="text-sm font-light tracking-wide text-gray-500">Organizaciones</div>
        </div>
      </li>
      <li>
        <%= link_to owner_organizations_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
          <span class="inline-flex justify-center items-center ml-4">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </span>
          <span class="ml-2 text-sm tracking-wide truncate">Gestionar Organizaciones</span>
        <% end %>
      </li>

      <li>
        <% if current_user&.owner? %>
          <%= link_to owner_clients_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
            <span class="inline-flex justify-center items-center ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Gestionar Clientes</span>
          <% end %>
        <% else %>
          <%= link_to collaborator_clients_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
            <span class="inline-flex justify-center items-center ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Gestionar Clientes</span>
          <% end %>
        <% end %>
      </li>

      <!-- Administración (solo para superadmin) -->
      <% if current_user&.superadmin? %>
        <li class="px-5 mt-4">
          <div class="flex flex-row items-center h-8">
            <div class="text-sm font-light tracking-wide text-gray-500">Administración</div>
          </div>
        </li>
        <li>
          <%= link_to superadmin_root_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
            <span class="inline-flex justify-center items-center ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Panel de Control</span>
          <% end %>
        </li>
      <% end %>

      <!-- Gestión de usuarios (solo para superadmin y support) -->
      <% if current_user&.superadmin? || current_user&.support? %>
        <li>
          <%= link_to owner_users_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
            <span class="inline-flex justify-center items-center ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Gestión de Usuarios</span>
          <% end %>
        </li>
      <% end %>

      <!-- Impersonación (solo para superadmin y soporte) -->
      <% if current_user&.can_impersonate? %>
        <li>
          <%= link_to superadmin_impersonations_path, class: "relative flex flex-row items-center h-11 focus:outline-none hover:bg-gray-800 text-gray-300 hover:text-white border-l-4 border-transparent hover:border-indigo-500 pr-6" do %>
            <span class="inline-flex justify-center items-center ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Impersonación</span>
          <% end %>
        </li>
      <% end %>

      <!-- Separador -->
      <li class="px-5 mt-4">
        <div class="flex flex-row items-center h-8">
          <div class="flex-grow border-t border-gray-700"></div>
        </div>
      </li>

      <!-- Botón de Logout -->
      <li class="px-5 py-2">
        <%= form_with url: main_session_path, method: :delete, html: { class: "w-full" } do %>
          <button type="submit" class="w-full flex items-center h-11 px-3 rounded bg-gray-800 text-gray-300 hover:bg-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span class="ml-2 text-sm tracking-wide">Salir</span>
          </button>
        <% end %>
      </li>
    </ul>
  </nav>
</aside>
