<%# 
  Partial para mostrar el estado de acceso del cliente y acciones relacionadas
  
  Parámetros:
  - client: El objeto cliente (requerido)
  - namespace: El namespace actual ('owner' o 'collaborator') (requerido)
  - organization: La organización actual (requerido)
%>

<% 
  # Determinar las rutas según el namespace
  base_path = namespace == 'owner' ? 'owner' : 'collaborator'
  toggle_access_path = send("toggle_access_#{base_path}_client_path", client)
  send_invitation_path = send("send_invitation_#{base_path}_client_path", client) if respond_to?("send_invitation_#{base_path}_client_path")
  
  # Obtener el estado de acceso
  org_user = client.organization_users.find_by(organization: organization, role: :client)
  access_enabled = org_user&.access_enabled?
%>

<% if organization&.client_access_enabled? %>
  <% if access_enabled %>
    <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex justify-between w-full">
          <div>
            <p class="text-sm text-green-700">
              Este cliente tiene acceso a la plataforma.
              <% if org_user&.invitation_sent_at %>
                Última invitación enviada: <%= l(org_user.invitation_sent_at, format: :short) %>
              <% else %>
                No se ha enviado ninguna invitación.
              <% end %>
            </p>
          </div>
          <div class="flex space-x-2">
            <% if send_invitation_path %>
              <%= button_to send_invitation_path, method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" do %>
                <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Reenviar Invitación
              <% end %>
            <% end %>
            <%= button_to toggle_access_path, method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-red-600 hover:bg-red-700" do %>
              <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
              </svg>
              Deshabilitar Acceso
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% else %>
    <div class="bg-gray-50 border-l-4 border-gray-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex justify-between w-full">
          <div>
            <p class="text-sm text-gray-700">
              Este cliente no tiene acceso a la plataforma. Puede habilitarlo haciendo clic en "Habilitar Acceso".
            </p>
          </div>
          <div>
            <%= button_to toggle_access_path, method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" do %>
              <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
              </svg>
              Habilitar Acceso
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>
<% end %>

<div class="mt-2 text-xs text-gray-500">
  <p class="italic">
    <% if access_enabled %>
      <span class="font-medium text-green-600">Estado actual: Acceso habilitado.</span> 
      Este cliente puede acceder a la plataforma para ver y gestionar sus propios documentos e información fiscal.
    <% else %>
      <span class="font-medium text-gray-600">Estado actual: Sin acceso.</span> 
      Este cliente no tiene acceso a la plataforma. Puede habilitarlo usando el botón "Habilitar Acceso".
    <% end %>
  </p>
</div>
