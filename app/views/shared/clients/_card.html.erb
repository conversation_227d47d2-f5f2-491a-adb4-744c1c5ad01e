<%# 
  Partial para mostrar una tarjeta estándar en páginas de clientes
  
  Parámetros:
  - title: <PERSON><PERSON><PERSON><PERSON> de la tarjeta (requerido)
  - subtitle: Subtítulo opcional para la tarjeta
  - card_id: ID opcional para la tarjeta (útil para JavaScript/CSS)
  - card_class: Clases CSS adicionales para la tarjeta
  - header_class: Clases CSS adicionales para el encabezado de la tarjeta
  - body_class: Clases CSS adicionales para el cuerpo de la tarjeta
  - header_actions: Contenido HTML para botones o acciones en el encabezado
%>

<div id="<%= local_assigns[:card_id] %>" class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 <%= local_assigns[:card_class] %> <%= 'mt-6' unless local_assigns[:no_margin] %>">
  <div class="px-6 py-4 border-b border-gray-200 <%= local_assigns[:header_class].presence || 'bg-gray-50' %> flex justify-between items-center">
    <div>
      <h3 class="text-lg font-medium text-gray-900"><%= title %></h3>
      <% if local_assigns[:subtitle].present? %>
        <p class="mt-1 text-sm text-gray-500"><%= subtitle %></p>
      <% end %>
    </div>
    
    <% if local_assigns[:header_actions].present? %>
      <div class="flex items-center space-x-2">
        <%= header_actions %>
      </div>
    <% end %>
  </div>
  
  <div class="p-6 <%= local_assigns[:body_class] %>">
    <%= yield %>
  </div>
</div>
