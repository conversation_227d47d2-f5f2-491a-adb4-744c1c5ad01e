<%# 
  Partial para mostrar el formulario de información personal del cliente
  
  Parámetros:
  - form: El objeto form para el cliente (requerido)
  - disabled_fields: Array con nombres de campos que deben estar deshabilitados (opcional)
  - wrapper_class: Clases adicionales para el contenedor (opcional)
%>

<div class="<%= local_assigns[:wrapper_class] || 'space-y-6' %>">
  <h4 class="text-sm font-medium text-gray-700 mb-2">Datos de Contacto</h4>
  
  <div class="mb-4">
    <%= render 'shared/form_field', 
              form: form, 
              field: :email, 
              label: 'Correo electrónico', 
              input_type: 'email', 
              disabled: (local_assigns[:disabled_fields] || []).include?(:email),
              wrapper_class: 'mb-4' %>
              
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <%= render 'shared/form_field', 
                form: form, 
                field: :name, 
                label: 'Nombre', 
                input_type: 'text',
                disabled: (local_assigns[:disabled_fields] || []).include?(:name),
                wrapper_class: 'mb-4' %>
                
      <%= render 'shared/form_field', 
                form: form, 
                field: :last_name, 
                label: 'Apellido', 
                input_type: 'text',
                disabled: (local_assigns[:disabled_fields] || []).include?(:last_name),
                wrapper_class: 'mb-4' %>
    </div>
  </div>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <%= render 'shared/form_field', 
              form: form, 
              field: :phone, 
              label: 'Teléfono', 
              input_type: 'tel',
              disabled: (local_assigns[:disabled_fields] || []).include?(:phone),
              wrapper_class: 'mb-4' %>
              
    <%= render 'shared/form_field', 
              form: form, 
              field: :position, 
              label: 'Cargo', 
              input_type: 'text',
              placeholder: 'Ej: Gerente de Finanzas',
              disabled: (local_assigns[:disabled_fields] || []).include?(:position),
              wrapper_class: 'mb-4' %>
  </div>
</div>
