<%# 
  Partial para mostrar un encabezado de sección estándar en páginas de clientes
  
  Parámetros:
  - title: <PERSON><PERSON><PERSON><PERSON> de la sección (requerido)
  - subtitle: Texto para mostrar como subtítulo (opcional)
  - actions: Contenido HTML para los botones de acción (opcional)
%>

<div class="w-full mb-4 flex justify-between items-center">
  <div>
    <h1 class="text-2xl font-bold text-gray-800"><%= title %></h1>
    <% if defined?(subtitle) && subtitle.present? %>
      <p class="mt-1 text-sm text-gray-500"><%= subtitle %></p>
    <% end %>
  </div>
  
  <% if defined?(actions) && actions.present? %>
    <div class="flex items-center space-x-2">
      <%= actions %>
    </div>
  <% end %>
</div>
