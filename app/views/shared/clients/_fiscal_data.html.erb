<% client ||= @client %>
<% client_organization ||= defined?(@client_organization) ? @client_organization : client&.organizations&.first %>
<% fiscal_data ||= defined?(@fiscal_data) ? @fiscal_data : nil %>
<% namespace ||= controller.class.name.split('::').first.downcase %>
<% update_url = namespace == 'owner' ? update_organization_fiscal_data_owner_clients_path(client) : update_organization_fiscal_data_collaborator_clients_path(client) %>
<% validate_url = namespace == 'owner' ? validate_fiscal_data_owner_clients_path(client) : validate_fiscal_data_collaborator_clients_path(client) %>
<% client_path = namespace == 'owner' ? owner_client_path(client) : collaborator_client_path(client) %>

<%# Ensure no default values are shown for fiscal data if we're not explicitly showing fetched fiscal data %>
<% if client_organization && !fiscal_data.present? %>
  <% 
  # Reset organization fiscal fields to prevent default values from showing
  client_organization.nombre_legal = nil
  client_organization.nombre_fantasia = nil 
  client_organization.tipo_entidad = nil
  client_organization.direccion = nil
  client_organization.telefono = nil
  client_organization.actividades = []
  %>
<% end %>

<% if fiscal_data.present? %>
  <%# Vista para mostrar y confirmar los datos fiscales %>
  <div class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
    <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
      <h1 class="text-2xl font-bold text-gray-800">Confirmar Datos Fiscales</h1>
      <p class="text-gray-600 mt-1 text-sm">
        Revisa y confirma los datos fiscales del cliente
      </p>
    </div>

    <div class="p-6">
      <div class="bg-blue-50 p-4 rounded-md border border-blue-100 mb-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h2 class="text-lg font-medium text-blue-800">Cliente: <%= client.name %> <%= client.last_name %></h2>
            <p class="mt-1 text-sm text-blue-700">
              Estás confirmando los datos fiscales para <span class="font-medium"><%= client.email %></span>
            </p>
            <p class="mt-1 text-sm text-blue-700">
              Organización: <span class="font-medium"><%= client_organization.name %></span>
            </p>
          </div>
        </div>
      </div>

      <%= form_with model: client_organization, 
                url: update_url,
                method: :patch,
                class: "space-y-6",
                data: { 
                  controller: "simplified-rut", 
                  simplified_rut_target: "fiscalForm",
                  simplified_rut_namespace_value: namespace
                } do |form| %>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <%= form.label :rut, "RUT", class: "form-field-label" %>
            <%= form.text_field :rut, 
                              value: fiscal_data[:rut],
                              class: "form-field-input !w-48",
                              style: "width: 12rem !important;",
                              maxlength: 12,
                              placeholder: "123456789012" %>
          </div>

          <div class="space-y-2">
            <%= form.label :nombre_legal, "Razón Social", class: "form-field-label" %>
            <%= form.text_field :nombre_legal, 
                             value: fiscal_data[:nombre_legal],
                             class: "form-field-input w-full",
                             placeholder: "Razón Social" %>
          </div>

          <div class="space-y-2">
            <%= form.label :nombre_fantasia, "Nombre de Fantasía", class: "form-field-label" %>
            <%= form.text_field :nombre_fantasia, 
                             value: fiscal_data[:nombre_fantasia],
                             class: "form-field-input w-full",
                             placeholder: "Nombre de Fantasía" %>
          </div>

          <div class="space-y-2">
            <%= form.label :tipo_entidad, "Tipo de Entidad", class: "form-field-label" %>
            <%= form.text_field :tipo_entidad, 
                             value: fiscal_data[:tipo_entidad],
                             class: "form-field-input w-full",
                             placeholder: "Tipo de Entidad" %>
          </div>

          <div class="space-y-2 md:col-span-2">
            <%= form.label :direccion, "Dirección", class: "form-field-label" %>
            <%= form.text_field :direccion, 
                             value: fiscal_data[:direccion],
                             class: "form-field-input w-full",
                             placeholder: "Dirección" %>
          </div>

          <div class="space-y-2">
            <%= form.label :telefono, "Teléfono", class: "form-field-label" %>
            <%= form.text_field :telefono, 
                             value: fiscal_data[:telefono],
                             class: "form-field-input w-full",
                             placeholder: "Teléfono" %>
          </div>

          <div class="space-y-2">
            <%= form.label :email, "Email", class: "form-field-label" %>
            <%= form.email_field :email, 
                              value: fiscal_data[:email],
                              class: "form-field-input w-full",
                              placeholder: "<EMAIL>" %>
          </div>

          <div class="space-y-2">
            <%= form.label :email_facturacion, "Email de facturación", class: "form-field-label" %>
            <%= form.email_field :email_facturacion, 
                             value: fiscal_data[:email],
                             class: "form-field-input w-full",
                             placeholder: "<EMAIL>" %>
          </div>
        </div>

        <div class="pt-4 border-t border-gray-200 mt-8">
          <div class="flex flex-col sm:flex-row justify-between gap-3">
            <%= link_to "Cancelar", 
                      client_path, 
                      class: "btn btn-secondary w-full sm:w-auto" %>
                      
            <div class="flex flex-col sm:flex-row gap-3">
              <%= link_to "Volver a validar", 
                        validate_url, 
                        class: "btn btn-outline w-full sm:w-auto" %>
                        
              <button type="submit" class="btn btn-primary w-full sm:w-auto">
                <i class="ri-save-line mr-2"></i>
                Guardar Datos Fiscales
              </button>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <%# Elemento de prueba para verificar Stimulus %>
  <div class="fixed bottom-4 right-4 p-4 bg-white shadow-lg rounded-lg border border-gray-200 z-50"
       data-controller="test">
    <h3 class="font-medium text-gray-900 mb-2 text-sm">Prueba de Stimulus</h3>
    <p class="text-xs text-gray-600 mb-3" data-test-target="message">
      Esperando conexión con Stimulus...
    </p>
    <button type="button" 
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            data-action="click->test#showAlert">
      Probar Alerta
    </button>
  </div>

  <%# Vista para validar el RUT %>
  <div class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
    <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
      <h1 class="text-2xl font-bold text-gray-800">Validar Datos Fiscales</h1>
      <p class="text-gray-600 mt-1 text-sm">
        Ingresa el RUT del cliente para validarlo y obtener sus datos fiscales
      </p>
    </div>

    <div class="p-6 relative" id="fiscal-data-section">
      <div class="hidden absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10" id="fiscal-spinner">
        <div class="loader"></div>
      </div>

      <%= form_with url: update_url,
                  method: :post, 
                  class: "space-y-6", 
                  data: { 
                    controller: "fiscal--simplified-rut", 
                    fiscal__simplified_rut_target: "form",
                    fiscal__simplified_rut_namespace_value: namespace
                  } do |form| %>
        
        <div class="bg-blue-50 p-4 rounded-md border border-blue-100 mb-6">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h2 class="text-lg font-medium text-blue-800">Cliente: <%= client.name %> <%= client.last_name %></h2>
              <p class="mt-1 text-sm text-blue-700">
                Estás agregando datos fiscales para el cliente <span class="font-medium"><%= client.email %></span>
              </p>
              <% if client_organization %>
              <p class="mt-1 text-sm text-blue-700">
                Organización: <span class="font-medium"><%= client_organization.name %></span>
              </p>
              <% end %>
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <div class="flex items-center justify-between mb-1">
            <label for="rut" class="form-field-label">RUT</label>
            <span class="text-xs text-gray-500">Formato: 12.345.678-9</span>
          </div>
          <div class="flex space-x-2 items-center">
            <!-- Campo RUT con ancho reducido -->
            <div class="relative w-48">
              <%= text_field_tag :rut, "", 
                class: "form-field-input w-full pl-10 pr-8 !w-48",
                style: "width: 12rem !important;",
                placeholder: "217090160018",
                required: true,
                data: { 
                  simplified_rut_target: "input", 
                  action: "input->simplified-rut#formatRut keypress->simplified-rut#handleKeypress" 
                } %>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="ri-building-line text-gray-400"></i>
              </div>
              
              <!-- Botón de limpiar -->
              <button type="button" 
                    class="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                    data-action="click->simplified-rut#clear"
                    data-simplified-rut-target="clearButton">
                <i class="ri-close-line"></i>
              </button>
            </div>
            
            <!-- Botón de consulta DGI -->
            <button type="button"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  data-action="click->fiscal--simplified-rut#fetchFiscalData"
                  data-fiscal--simplified-rut-target="fetchButton"
                  onclick="fiscalFallbackHandler(event)">
              <i class="ri-search-line mr-1"></i>
              <span>Consultar DGI</span>
            </button>
            
            <!-- Script de respaldo para cuando Stimulus no está disponible -->
            <script>
              // Handler de respaldo para el botón DGI si Stimulus falla
              function fiscalFallbackHandler(event) {
                console.log('[Fiscal Fallback] Handler ejecutado');
                event.preventDefault();
                
                // Verificar si Stimulus real está funcionando
                if (window.Stimulus && 
                    window.Stimulus.router && 
                    window.Stimulus.router.modulesByIdentifier &&
                    window.Stimulus.router.modulesByIdentifier.has('fiscal--simplified-rut')) {
                  console.log('[Fiscal Fallback] Stimulus funciona, dejando que Stimulus maneje el evento');
                  return; // Dejar que Stimulus maneje el evento
                }
                
                console.log('[Fiscal Fallback] Stimulus no disponible, usando handler manual');
                
                // Encontrar el input RUT (buscar más exhaustivamente)
                let rutInput = document.querySelector('[data-simplified-rut-target="input"], [data-fiscal--simplified-rut-target="input"]');
                
                // Si no se encuentra, buscar por otros selectores comunes
                if (!rutInput) {
                  const posibleRutSelectors = [
                    '#rut',
                    '#rut_uruguayo',
                    '[name="organization[rut_uruguayo]"]',
                    '[name="rut"]',
                    '[placeholder="217090160018"]',
                    'input.form-field-input:first-child'
                  ];
                  
                  for (const selector of posibleRutSelectors) {
                    rutInput = document.querySelector(selector);
                    if (rutInput) {
                      console.log(`[Fiscal Fallback] RUT input encontrado con selector: ${selector}`);
                      break;
                    }
                  }
                }
                
                // Último recurso: buscar cualquier input visible en el formulario fiscal
                if (!rutInput) {
                  const formInputs = document.querySelectorAll('form input:not([type="hidden"])');
                  if (formInputs.length > 0) {
                    rutInput = formInputs[0];
                    console.log('[Fiscal Fallback] Usando primer input del formulario como fallback');
                  }
                }
                
                if (!rutInput || !rutInput.value.trim()) {
                  alert('Por favor ingrese un RUT para consultar. No se pudo encontrar el campo RUT.');
                  return;
                }
                
                const rut = rutInput.value.trim();
                console.log(`[Fiscal Fallback] Consultando datos fiscales para RUT: ${rut}`);
                
                // Mostrar indicador de carga si existe
                const spinner = document.getElementById('fiscal-spinner');
                if (spinner) spinner.classList.remove('hidden');
                
                // Realizar la petición AJAX
                fetch(`/<%= namespace %>/search/fiscal_data?rut=${encodeURIComponent(rut)}`, {
                  method: 'GET',
                  headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                  },
                  credentials: 'same-origin'
                })
                .then(response => {
                  if (!response.ok) {
                    throw new Error(`Error ${response.status}: ${response.statusText}`);
                  }
                  return response.json();
                })
                .then(data => {
                  console.log('[Fiscal Fallback] Datos recibidos:', data);
                  
                  if (data.error) {
                    throw new Error(data.error);
                  }
                  
                  // Autocompletar el formulario
                  if (data) {
                    // Mapear los campos
                    const fieldMappings = {
                      'nombre_legal': 'nombre_legal',
                      'nombre_fantasia': 'nombre_fantasia',
                      'direccion': 'direccion',
                      'telefono': 'telefono',
                      'email': 'email',
                      'giro': 'giro',
                      'actividades': 'actividades'
                    };
                    
                    // Completar campos automáticamente
                    Object.entries(fieldMappings).forEach(([apiField, formField]) => {
                      const input = document.querySelector(`[name="organization[${formField}]"]`);
                      if (input && data[apiField]) {
                        input.value = data[apiField];
                      }
                    });
                    
                    alert('Datos fiscales cargados correctamente');
                  } else {
                    alert('No se encontraron datos fiscales para este RUT');
                  }
                })
                .catch(error => {
                  console.error('[Fiscal Fallback] Error:', error);
                  alert(`Error al consultar datos fiscales: ${error.message}`);
                })
                .finally(() => {
                  // Ocultar spinner
                  if (spinner) spinner.classList.add('hidden');
                });
              }
            </script>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            Ingresa el RUT uruguayo completo con o sin guiones y puntos.
          </p>
          <div data-simplified-rut-target="errorContainer" class="mt-2 hidden"></div>
          <div data-simplified-rut-target="resultsContainer" class="mt-2 hidden"></div>
        </div>

        <div class="pt-4 border-t border-gray-200 mt-8">
          <div class="flex flex-col sm:flex-row justify-end gap-3">
            <%= link_to "Cancelar", 
                      client_path, 
                      class: "btn btn-secondary w-full sm:w-auto" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

<style>
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
