<%# 
  Partial para mostrar el sistema de pestañas en la vista de detalle de cliente
  
  Parámetros:
  - client: El objeto cliente (requerido)
  - namespace: El namespace actual ('owner' o 'collaborator')
  - current_tab: La pestaña actualmente activa
%>

<% 
  # Determinar las rutas según el namespace
  client_path = namespace == 'owner' ? owner_client_path(client) : collaborator_client_path(client)
  
  # Definir las pestañas disponibles
  tabs = [
    {
      name: 'Información General',
      path: "#{client_path}?tab=info",
      active: current_tab == 'info',
      icon: 'ri-information-line'
    },
    {
      name: 'Datos Fiscales',
      path: "#{client_path}?tab=fiscal",
      active: current_tab == 'fiscal',
      icon: 'ri-file-list-3-line'
    },
    {
      name: 'Documentos',
      path: "#{client_path}?tab=documents",
      active: current_tab == 'documents',
      icon: 'ri-folder-line'
    },
    {
      name: 'Actividad',
      path: "#{client_path}?tab=activity",
      active: current_tab == 'activity',
      icon: 'ri-time-line'
    }
  ]
%>

<div class="mb-8 border-b border-gray-200" data-controller="client-tabs">
  <nav class="flex space-x-12" aria-label="Tabs">
    <% tabs.each do |tab| %>
      <a href="<%= tab[:path] %>" 
         data-client-tabs-target="tab"
         data-action="click->client-tabs#handleTabChange"
         data-tab-name="<%= tab[:name].parameterize %>"
         data-tab-path="<%= tab[:path] %>"
         class="<%= tab[:active] ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center">
        <i class="<%= tab[:icon] %> mr-2 text-lg"></i>
        <%= tab[:name] %>
      </a>
    <% end %>
  </nav>
  
  <!-- Formulario oculto para el guardado -->
  <%= form_with(model: [namespace.to_sym, client], 
               id: 'client-form',
               data: { client_tabs_target: 'form' },
               class: 'hidden') do |f| %>
    <%= f.hidden_field :id %>
    <!-- Los campos se actualizarán dinámicamente -->
  <% end %>
  
  <div id="tab-loading" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-4 rounded-lg shadow-lg">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mr-3"></div>
        <span>Guardando cambios...</span>
      </div>
    </div>
  </div>
</div>
