<%# 
  Partial para mostrar información de la organización del cliente
  
  Parámetros:
  - client_organization: La organización del cliente (requerido)
  - editable: Booleano que indica si los campos son editables (default: false)
  - form: El objeto form si editable es true
  - namespace: El namespace actual ('owner' o 'collaborator')
  
  Uso:
  <%= render 'shared/clients/organization_info', 
             client_organization: @client_organization,
             editable: true,
             form: organization_form, 
             namespace: 'owner' %>
%>

<% 
  # Determinar si se debe mostrar como campos editables o como información de solo lectura
  editable = local_assigns[:editable] || false
  organization = client_organization
%>

<div class="w-full">
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-lg font-medium text-gray-900">Información de la Organización</h2>
    <% if organization&.rut.present? %>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
        Datos fiscales validados
      </span>
    <% else %>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
        Sin datos fiscales
      </span>
    <% end %>
  </div>

  <% if editable && form.present? %>
    <!-- Vista editable con formulario -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <%= render 'shared/form_field', 
                  form: form, 
                  field: :name, 
                  label: 'Nombre de la organización', 
                  input_type: 'text',
                  value: organization&.name,
                  wrapper_class: 'mb-4' %>
      </div>
      
      <div>
        <div class="<%= organization.errors[:rut].any? ? 'has-error' : '' %>">
          <%= render 'shared/form_field', 
                    form: form, 
                    field: :rut, 
                    label: 'RUT', 
                    input_type: 'text',
                    value: organization&.rut,
                    maxlength: 12,
                    data: { 
                      controller: 'rut',
                      rut_target: 'input',
                      action: 'input->rut#formatRut keypress->rut#handleKeypress change->rut#syncHiddenField',
                      rut_format_value: 'false'  # Desactiva el formateo automático
                    },
                    wrapper_class: 'mb-1' %>
          <% if organization.errors[:rut].any? %>
            <p class="mt-1 text-sm text-red-600"><%= organization.errors.full_messages_for(:rut).join(', ') %></p>
          <% else %>
            <div class="mt-1 text-xs text-gray-500">
              Formato: 12 dígitos, donde los dos primeros deben ser entre 01 y 22, seguidos de 8 dígitos y 2 ceros
            </div>
          <% end %>
        </div>
      </div>
      
      <div class="md:col-span-2">
        <%= render 'shared/form_field', 
                  form: form, 
                  field: :direccion, 
                  label: 'Dirección', 
                  input_type: 'text',
                  value: organization&.direccion,
                  wrapper_class: 'mb-4' %>
      </div>
      
      <div>
        <%= render 'shared/form_field', 
                  form: form, 
                  field: :telefono, 
                  label: 'Teléfono', 
                  input_type: 'tel',
                  value: organization&.telefono,
                  wrapper_class: 'mb-4' %>
      </div>
      
      <div>
        <%= render 'shared/form_field', 
                  form: form, 
                  field: :email_facturacion, 
                  label: 'Email de Facturación', 
                  input_type: 'email',
                  value: organization&.email_facturacion,
                  wrapper_class: 'mb-4' %>
      </div>
    </div>
  <% else %>
    <!-- Vista de solo lectura -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-500">Nombre</h3>
        <p class="mt-1 text-sm text-gray-900"><%= organization&.name || "-" %></p>
      </div>

      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-500">RUT</h3>
        <div class="flex items-center">
          <p class="mt-1 text-sm text-gray-900">
            <%= organization&.rut.present? ? organization.rut : "-" %>
          </p>
          <% if organization&.rut.blank? && local_assigns[:show_fiscal_link] && local_assigns[:namespace] %>
            <% validate_path = namespace == 'owner' ? 
                              validate_fiscal_data_owner_clients_path : 
                              validate_fiscal_data_collaborator_clients_path %>
            <%= link_to validate_path, class: "ml-2 text-xs text-indigo-600 hover:text-indigo-900" do %>
              <span>Agregar</span>
            <% end %>
          <% end %>
        </div>
      </div>

      <div class="mb-4 md:col-span-2">
        <h3 class="text-sm font-medium text-gray-500">Dirección</h3>
        <p class="mt-1 text-sm text-gray-900"><%= organization&.direccion || "-" %></p>
      </div>

      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-500">Teléfono</h3>
        <p class="mt-1 text-sm text-gray-900"><%= organization&.telefono || "-" %></p>
      </div>

      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-500">Email de Facturación</h3>
        <p class="mt-1 text-sm text-gray-900"><%= organization&.email_facturacion || "-" %></p>
      </div>
    </div>
  <% end %>
</div>
