<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON><PERSON> de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Mi Perfil</h1>
      <p class="mt-1 text-sm text-gray-500">Administra tu información personal y configuración de cuenta.</p>
    </div>

    <!-- Barra de navegación de pestañas -->
    <%= render 'shared/tabs' do |tabs| %>
      <% tabs.with_tab name: 'Datos Personales', 
                      path: client_profile_path(tab: 'personal'), 
                      active: @tab == 'personal',
                      icon: 'ri-user-line' %>
      
      <% tabs.with_tab name: 'Seguridad', 
                      path: client_profile_path(tab: 'password'), 
                      active: @tab == 'password',
                      icon: 'ri-lock-line' %>
    <% end %>


    <!-- Contenido de la pestaña Datos Personales -->
    <% if @tab == 'personal' %>
      <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Información Personal</h3>
          </div>
        </div>

        <div class="p-6">
          <%= form_with model: @user, 
                      url: client_profile_path(tab: 'personal'), 
                      method: :patch,
                      class: 'w-full' do |form| %>
            <div class="max-w-3xl mx-auto w-full">
              <% if @user.errors.any? %>
                <div class="mb-6">
                  <%= render 'shared/alert', 
                             type: 'error',
                             title: "Se encontraron #{@user.errors.count} errores",
                             message: @user.errors.full_messages.to_sentence %>
                </div>
              <% end %>

              <div class="space-y-6">
                <div class="mb-4">
                  <%= form.label :email, 'Correo electrónico', class: 'block text-sm font-medium text-gray-700' %>
                  <div class="mt-1">
                    <%= form.email_field :email, 
                                      class: 'form-field-input w-full bg-gray-100 cursor-not-allowed', 
                                      disabled: true %>
                  </div>
                  <p class="mt-1 text-xs text-gray-500">El correo electrónico no puede ser modificado. Contacta al soporte para realizar cambios.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="mb-4">
                    <%= form.label :name, 'Nombre', class: 'block text-sm font-medium text-gray-700' %>
                    <div class="mt-1">
                      <%= form.text_field :name, 
                                        class: 'form-field-input w-full' %>
                    </div>
                  </div>

                  <div class="mb-4">
                    <%= form.label :last_name, 'Apellido', class: 'block text-sm font-medium text-gray-700' %>
                    <div class="mt-1">
                      <%= form.text_field :last_name, 
                                        class: 'form-field-input w-full' %>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex flex-col sm:flex-row justify-between gap-4">
                  <div class="flex flex-wrap gap-3">
                    <%= link_to client_profile_path, 
                                class: 'inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50' do %>
                      <i class="ri-arrow-left-line mr-2"></i>
                      Volver
                    <% end %>
                  </div>
                  
                  <div class="flex-shrink-0">
                    <%= form.button type: 'submit', 
                                  class: 'inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700' do %>
                      <i class="ri-save-line mr-2"></i>
                      <span>Guardar Cambios</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Contenido de la pestaña Seguridad -->
    <% if @tab == 'password' %>
      <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg font-medium text-gray-900">Cambiar Contraseña</h3>
        </div>

        <div class="p-6">
          <%= form_with url: main_password_path, 
                      method: :patch, 
                      scope: :user,
                      class: 'w-full' do |form| %>
            <div class="max-w-3xl mx-auto w-full">
              <div class="space-y-6">
                <div class="mb-4">
                  <%= form.label :current_password, 'Contraseña Actual', class: 'block text-sm font-medium text-gray-700' %>
                  <div class="mt-1">
                    <%= form.password_field :current_password, 
                                          class: 'form-field-input w-full',
                                          required: true,
                                          autocomplete: 'current-password' %>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="mb-4">
                    <%= form.label :password, 'Nueva Contraseña', class: 'block text-sm font-medium text-gray-700' %>
                    <div class="mt-1">
                      <%= form.password_field :password, 
                                            class: 'form-field-input w-full',
                                            required: true,
                                            autocomplete: 'new-password' %>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Mínimo 8 caracteres</p>
                  </div>

                  <div class="mb-4">
                    <%= form.label :password_confirmation, 'Confirmar Contraseña', class: 'block text-sm font-medium text-gray-700' %>
                    <div class="mt-1">
                      <%= form.password_field :password_confirmation, 
                                            class: 'form-field-input w-full',
                                            required: true,
                                            autocomplete: 'new-password' %>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex flex-col sm:flex-row justify-between gap-4">
                  <div class="flex flex-wrap gap-3">
                    <%= link_to client_profile_path, 
                                class: 'inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50' do %>
                      <i class="ri-arrow-left-line mr-2"></i>
                      Volver
                    <% end %>
                  </div>
                  
                  <div class="flex-shrink-0">
                    <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                      <i class="ri-lock-password-line mr-2"></i>
                      <span>Cambiar Contraseña</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
