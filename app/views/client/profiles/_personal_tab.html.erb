<%= form_with model: @user, url: profile_path(tab: 'personal'), method: :patch, class: 'w-full' do |form| %>
  <% if @user.errors.any? %>
    <div class="bg-red-50 p-4 rounded-md mb-6 border border-red-200">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Se encontraron <%= @user.errors.count %> errores:</h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 w-full space-y-6">
    <!-- Campos de información personal -->
    <div class="space-y-4">
      <div>
        <label for="user_email" class="form-field-label">Email</label>
        <%= form.email_field :email, class: "form-field-input w-full" %>
      </div>

      <div>
        <label for="user_name" class="form-field-label">Nombre</label>
        <%= form.text_field :name, class: "form-field-input w-full" %>
      </div>

      <div>
        <label for="user_last_name" class="form-field-label">Apellido</label>
        <%= form.text_field :last_name, class: "form-field-input w-full" %>
      </div>
    </div>

    <div class="pt-2">
      <button type="submit" class="btn btn-primary w-full sm:w-auto">
        <i class="ri-save-line mr-2"></i>Guardar cambios
      </button>
    </div>
  </div>
<% end %>
