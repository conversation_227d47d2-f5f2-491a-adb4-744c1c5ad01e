<% content_for :full_width, true %>

<%= render 'shared/clients/section_header', title: 'Gestionar Clientes' do |header| %>
  <% header.with_actions do %>
    <%= link_to "Nuevo Cliente", new_collaborator_client_path, class: "btn btn-primary" %>
  <% end %>
<% end %>

<%
  # Definir las columnas para el DataTable
  columns = [
    {
      label: 'Email',
      field: 'email',
      sortable: true,
      width: '20%'
    },
    {
      label: 'Nombre',
      field: ->(client) { [client.name, client.last_name].compact.join(' ') },
      sortable: true,
      width: '20%'
    },
    {
      label: 'RUT',
      field: ->(client) { client.client_organizations.first&.rut || "-" },
      filterable: true,
      sortable: true,
      width: '15%',
      filter_options: @clients.map { |c| c.client_organizations.first&.rut }.compact.uniq
    },
    {
      label: 'Dirección',
      field: ->(client) { client.client_organizations.first&.direccion || "-" },
      sortable: true,
      width: '15%'
    },
    {
      label: 'Teléfono',
      field: ->(client) { client.client_organizations.first&.telefono || "-" },
      sortable: true,
      width: '10%'
    },
    {
      label: 'Asignado a',
      field: ->(client) { client.assigned_to&.email || "-" },
      filterable: true,
      sortable: true,
      width: '20%',
      filter_options: @clients.map { |c| c.assigned_to&.email }.compact.uniq
    }
  ]

  # Agregar columna de acceso si está habilitado
  if @organization.client_access_enabled
    columns << {
      label: 'Acceso',
      field: ->(client) {
        org_user = OrganizationUser.find_by(user: client, organization: @organization, role: :client)
        if org_user&.access_enabled
          content_tag(:span, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800") do
            concat content_tag(:svg, content_tag(:circle, nil, cx: "4", cy: "4", r: "3"), class: "-ml-0.5 mr-1.5 h-2 w-2 text-green-400", fill: "currentColor", viewBox: "0 0 8 8")
            concat "Habilitado"
          end
        else
          content_tag(:span, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800") do
            concat content_tag(:svg, content_tag(:circle, nil, cx: "4", cy: "4", r: "3"), class: "-ml-0.5 mr-1.5 h-2 w-2 text-gray-400", fill: "currentColor", viewBox: "0 0 8 8")
            concat "Deshabilitado"
          end
        end
      },
      filterable: true,
      sortable: true,
      width: '10%',
      filter_options: [
        { value: "Habilitado", label: "Habilitado" },
        { value: "Deshabilitado", label: "Deshabilitado" }
      ]
    }
  end

  # Definir las acciones
  actions = {
    view: {
      label: 'Ver detalles',
      path: ->(client) { collaborator_client_path(client) },
      icon: 'ri-eye-line',
      class: 'text-blue-600 hover:text-blue-800',
      title: 'Ver detalles del cliente'
    }
  }
  
  # Agregar columna para seleccionar como activo
  columns << {
    label: 'Activo',
    field: ->(client) {
      org_user = OrganizationUser.find_by(user: client, organization: @organization, role: :client)
      is_active = org_user&.access_enabled?
      
      content_tag(:div, 
        data: { 
          controller: 'client',
          client_url_value: set_active_collaborator_client_path(client),
          client_active_value: is_active
        }) do
        button_tag type: 'button',
                  class: 'p-1 focus:outline-none',
                  data: { 
                    action: 'click->client#toggleActive',
                    client_target: 'button'
                  } do
          content_tag(:span, 
            data: { client_target: 'icon' },
            class: is_active ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500') do
            content_tag(:i, '', class: is_active ? 'ri-star-fill text-xl' : 'ri-star-line text-xl')
          end
        end
      end
    },
    sortable: false,
    width: '5%',
    align: 'center'
  }

  # Definir opciones de tema
  theme = {
    header_bg: "bg-gray-50",
    header_text: "text-gray-500",
    row_bg: "bg-white",
    row_hover: "hover:bg-gray-50",
    primary_button: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
    secondary_button: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
  }
%>

<%= render partial: 'shared/datatable', locals: {
  columns: columns,
  collection: @clients,
  search_enabled: true,
  filters_enabled: true,
  pagination_enabled: true,
  per_page: 25,
  actions: actions,
  selection_enabled: true,
  export_enabled: true,
  table_id: "clients_table",
  theme: theme
} %>
