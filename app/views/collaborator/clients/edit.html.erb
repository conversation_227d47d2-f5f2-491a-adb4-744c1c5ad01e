<div>
  <div class="mb-2">
    <h1 class="text-2xl font-bold text-gray-800">Editar Cliente</h1>
  </div>
  <p class="text-sm text-gray-600 mb-2">Aquí puedes editar los datos de este cliente.</p>

  <!-- Pestañas de navegación -->
  <%= render 'shared/tabs' do |tabs| %>
    <% tabs.with_tab name: 'Información General', 
                    path: edit_collaborator_client_path(@client, section: 'general'), 
                    active: @section == 'general',
                    icon: 'ri-information-line' %>
    
    <% tabs.with_tab name: '<PERSON><PERSON> Fiscales', 
                    path: edit_collaborator_client_path(@client, section: 'fiscal'), 
                    active: @section == 'fiscal',
                    icon: 'ri-bill-line' %>
                    
    <% tabs.with_tab name: 'Datos de Facturación', 
                    path: edit_collaborator_client_path(@client, section: 'billing'), 
                    active: @section == 'billing',
                    icon: 'ri-bank-card-line' %>
                    
    <% tabs.with_tab name: 'Asignación de Colaboradores', 
                    path: edit_collaborator_client_path(@client, section: 'members'), 
                    active: @section == 'members',
                    icon: 'ri-team-line' %>
                    
    <% if current_organization&.client_access_enabled %>
      <% tabs.with_tab name: 'Acceso a Plataforma', 
                      path: edit_collaborator_client_path(@client, section: 'access'), 
                      active: @section == 'access',
                      icon: 'ri-lock-unlock-line' %>
    <% end %>
  <% end %>

  <div class="bg-white shadow-sm rounded-lg overflow-hidden w-full border border-gray-200">
    <div class="p-6">
    <%= form_with model: @client, url: collaborator_client_path(@client, section: @section), method: :patch, local: true, class: "space-y-8 w-full" do |form| %>
      <% if @client.errors.any? || @client_organization.errors.any? %>
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="ri-error-warning-line text-red-400 text-xl"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Se encontraron errores:</h3>
              <div class="mt-2 text-sm text-red-700">
                <ul role="list" class="list-disc pl-5 space-y-1">
                  <% @client.errors.full_messages.each do |error| %>
                    <li><%= error %></li>
                  <% end %>
                  <% if @client_organization.present? %>
                    <% @client_organization.errors.full_messages.each do |error| %>
                      <li><%= error %></li>
                    <% end %>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <% if @section == 'general' %>
      <!-- Información General -->
      <div class="space-y-6">
        <div class="mb-4">
          <h3 class="text-lg font-medium text-gray-900">Información del Contacto</h3>
          <p class="text-sm text-gray-500 mt-1">Datos básicos del contacto del cliente.</p>
        </div>

        <div class="space-y-6">
          <!-- Nombre de la Organización -->
          <div class="w-full">
            <label for="organization_name" class="form-field-label">Nombre de la Organización</label>
            <input type="text" 
                  name="organization[name]" 
                  id="organization_name" 
                  value="<%= @client_organization.name %>" 
                  class="form-field-input w-full" 
                  required
                  placeholder="Ingrese el nombre de la organización">
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Email del Contacto -->
            <div class="w-full">
              <%= form.label :email, "Email del Contacto", class: "form-field-label" %>
              <%= form.email_field :email, 
                                class: "form-field-input w-full", 
                                required: true,
                                placeholder: "<EMAIL>" %>
            </div>

            <!-- Teléfono -->
            <div class="w-full">
              <label for="organization_telefono" class="form-field-label">Teléfono</label>
            <input type="text" name="organization[telefono]" id="organization_telefono" value="<%= @client_organization.telefono %>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
          </div>
        </div>
      </div>
      <% elsif @section == 'fiscal' %>
      <!-- Datos Fiscales -->
      <div class="space-y-6">
        <div class="mb-4">
          <h3 class="text-lg font-medium text-gray-900">Datos Fiscales</h3>
          <p class="text-sm text-gray-500 mt-1">Información fiscal del cliente.</p>
        </div>

        <div class="space-y-6">
          <!-- RUT con botón de búsqueda -->
          <div class="w-full">
            <label for="organization_rut" class="form-field-label">RUT</label>
            <div class="flex">
              <input type="text" 
                    name="organization[rut]" 
                    id="organization_rut" 
                    value="<%= @client_organization.rut %>" 
                    class="form-field-input rounded-r-none border-r-0" 
                    required 
                    placeholder="217090160018"
                    data-controller="rut"
                    data-action="blur->rut#format">
              <button type="button" 
                      id="fetch-fiscal-data-btn" 
                      class="btn btn-secondary rounded-l-none px-3"
                      data-action="click->rut#fetchFiscalData">
                <i class="ri-search-line"></i>
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Formato: 12 dígitos numéricos</p>
          </div>

          <!-- Nombre de Fantasía -->
          <div class="w-full">
            <label for="organization_nombre_fantasia" class="form-field-label">Nombre de Fantasía</label>
            <input type="text" 
                  name="organization[nombre_fantasia]" 
                  id="organization_nombre_fantasia" 
                  value="<%= @client_organization.nombre_fantasia %>" 
                  class="form-field-input w-full"
                  placeholder="Nombre comercial de la empresa">
          </div>

          <!-- Tipo de Entidad -->
          <div class="w-full">
            <label for="organization_tipo_entidad" class="form-field-label">Tipo de Entidad</label>
            <div class="relative">
              <select name="organization[tipo_entidad]" 
                     id="organization_tipo_entidad" 
                     class="form-field-select w-full">
                <option value="" <%= @client_organization.tipo_entidad.blank? ? 'selected' : '' %>>Seleccionar tipo...</option>
                <option value="Empresa Unipersonal" <%= @client_organization.tipo_entidad == 'Empresa Unipersonal' ? 'selected' : '' %>>Empresa Unipersonal</option>
                <option value="Sociedad Anónima" <%= @client_organization.tipo_entidad == 'Sociedad Anónima' ? 'selected' : '' %>>Sociedad Anónima</option>
                <option value="Sociedad de Responsabilidad Limitada" <%= @client_organization.tipo_entidad == 'Sociedad de Responsabilidad Limitada' ? 'selected' : '' %>>Sociedad de Responsabilidad Limitada</option>
                <option value="Sociedad Colectiva" <%= @client_organization.tipo_entidad == 'Sociedad Colectiva' ? 'selected' : '' %>>Sociedad Colectiva</option>
                <option value="Sociedad en Comandita" <%= @client_organization.tipo_entidad == 'Sociedad en Comandita' ? 'selected' : '' %>>Sociedad en Comandita</option>
                <option value="Sociedad de Hecho" <%= @client_organization.tipo_entidad == 'Sociedad de Hecho' ? 'selected' : '' %>>Sociedad de Hecho</option>
                <option value="Cooperativa" <%= @client_organization.tipo_entidad == 'Cooperativa' ? 'selected' : '' %>>Cooperativa</option>
                <option value="Asociación Civil" <%= @client_organization.tipo_entidad == 'Asociación Civil' ? 'selected' : '' %>>Asociación Civil</option>
                <option value="Fundación" <%= @client_organization.tipo_entidad == 'Fundación' ? 'selected' : '' %>>Fundación</option>
                <option value="Otro" <%= @client_organization.tipo_entidad == 'Otro' ? 'selected' : '' %>>Otro</option>
              </select>
              <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <i class="ri-arrow-down-s-line"></i>
              </div>
            </div>
          </div>

          <!-- Dirección -->
          <div class="w-full">
            <label for="organization_direccion" class="form-field-label">Dirección</label>
            <input type="text" 
                  name="organization[direccion]" 
                  id="organization_direccion" 
                  value="<%= @client_organization.direccion %>" 
                  class="form-field-input w-full"
                  placeholder="Dirección fiscal completa">
          </div>

          <!-- Teléfono -->
          <div class="w-full">
            <label for="organization_telefono_facturacion" class="form-field-label">Teléfono</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="ri-phone-line text-gray-400"></i>
              </div>
              <input type="tel" 
                    name="organization[telefono_facturacion]" 
                    id="organization_telefono_facturacion" 
                    value="<%= @client_organization.telefono_facturacion %>" 
                    class="form-field-input pl-10 w-full"
                    placeholder="+56 9 1234 5678">
            </div>
          </div>

          <!-- Email de Facturación -->
          <div class="w-full">
            <label for="organization_email_facturacion" class="form-field-label">Email de Facturación</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="ri-mail-line text-gray-400"></i>
              </div>
              <input type="email" 
                    name="organization[email_facturacion]" 
                    id="organization_email_facturacion" 
                    value="<%= @client_organization.email_facturacion %>" 
                    class="form-field-input pl-10 w-full"
                    placeholder="<EMAIL>">
            </div>
          </div>

          <!-- Información sobre obtención de datos fiscales -->
          <div class="w-full">
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="ri-information-line text-blue-400 text-xl"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-blue-700">
                    Puedes obtener automáticamente los datos fiscales del cliente utilizando su RUT. 
                    Haz clic en el botón de búsqueda junto al campo RUT o utiliza el botón de abajo 
                    para obtener todos los datos fiscales.
                  </p>
                  <div class="mt-3">
                    <button type="button" 
                            id="fetch-all-fiscal-data-btn" 
                            class="btn btn-primary text-sm"
                            data-action="click->rut#fetchFiscalData">
                      <i class="ri-refresh-line mr-1"></i>
                      Obtener Todos los Datos Fiscales
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mensaje de carga -->
          <div id="fiscal-data-loading" class="w-full hidden">
            <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="ri-loader-4-line animate-spin text-green-500 text-xl"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-green-700">
                    Obteniendo datos fiscales, por favor espere...
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Mensaje de error -->
          <div id="fiscal-data-error" class="w-full hidden">
            <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="ri-error-warning-line text-red-500 text-xl"></i>
                </div>
                <div class="ml-3">
                  <p id="fiscal-data-error-message" class="text-sm text-red-700">
                    Error al obtener datos fiscales.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- JavaScript para la obtención de datos fiscales -->
        <!-- El botón de consulta fiscal ahora está gestionado por el controlador Stimulus rut_controller.js -->
        <!-- No se necesita JavaScript adicional para esta funcionalidad -->
      </div>
      <% elsif @section == 'billing' %>
      <!-- Datos de Facturación -->
      <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-4 mb-6">
        <div class="mb-4 border-b border-gray-200 pb-2">
          <h3 class="text-md font-medium text-gray-800">Datos de Facturación</h3>
          <p class="text-sm text-gray-600">Información para la facturación del cliente.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Dirección de Facturación -->
          <div class="md:col-span-2">
            <label for="organization_direccion" class="block text-sm font-medium text-gray-700 mb-1">Dirección de Facturación</label>
            <input type="text" name="organization[direccion]" id="organization_direccion" value="<%= @client_organization.direccion %>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
          </div>

          <!-- Email de Facturación -->
          <div>
            <label for="organization_email_facturacion" class="block text-sm font-medium text-gray-700 mb-1">Email de Facturación</label>
            <input type="email" name="organization[email_facturacion]" id="organization_email_facturacion" value="<%= @client_organization.email_facturacion %>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
          </div>

          <!-- Teléfono de Facturación -->
          <div>
            <label for="organization_telefono_facturacion" class="block text-sm font-medium text-gray-700 mb-1">Teléfono de Facturación</label>
            <input type="text" name="organization[telefono]" id="organization_telefono_facturacion" value="<%= @client_organization.telefono %>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
          </div>

          <!-- Información adicional de facturación -->
          <div class="md:col-span-2 mt-4">
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-700">
                    Estos datos se utilizarán para la facturación electrónica. Asegúrate de que sean correctos.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <% elsif @section == 'members' %>
      <!-- Asignación de Colaboradores -->
      <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-4 mb-6">
        <div class="mb-4 border-b border-gray-200 pb-2">
          <h3 class="text-md font-medium text-gray-800">Asignación de Colaboradores</h3>
          <p class="text-sm text-gray-600">Selecciona los colaboradores que pueden gestionar este cliente.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Asignación Legacy -->
          <div>
            <%= form.label :assigned_to_id, "Asignar a Colaborador (Legacy)", class: "block text-sm font-medium text-gray-700 mb-1" %>
            <%= form.collection_select :assigned_to_id,
                                      current_organization.collaborators,
                                      :id,
                                      :email,
                                      { include_blank: "Seleccionar colaborador..." },
                                      { class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" } %>
            <p class="mt-1 text-xs text-gray-500">Este campo se mantiene por compatibilidad. Recomendamos usar la asignación múltiple a continuación.</p>
          </div>
        </div>

        <div class="mt-6">
          <fieldset>
            <legend class="text-base font-medium text-gray-700">Asignación Múltiple de Colaboradores</legend>
            <p class="text-sm text-gray-500 mb-4">Selecciona los colaboradores que pueden gestionar este cliente</p>

            <!-- Botones de selección rápida -->
            <div class="flex space-x-2 mb-4">
              <button type="button" id="select-all-members" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-1 px-2 rounded">
                Seleccionar todos
              </button>
              <button type="button" id="deselect-all-members" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-1 px-2 rounded">
                Deseleccionar todos
              </button>
            </div>

            <div class="mt-4 border-t border-gray-200 pt-4">
              <div class="grid grid-cols-1 gap-y-2 sm:grid-cols-2 lg:grid-cols-3">
                <% current_organization.collaborators.each do |collaborator| %>
                  <div class="relative flex items-start">
                    <div class="flex items-center h-5">
                      <%= check_box_tag 'user[collaborator_ids][]', collaborator.id,
                                      @client.assigned_collaborators.include?(collaborator) || @client.assigned_to_id == collaborator.id,
                                      id: "collaborator_#{collaborator.id}",
                                      class: "collaborator-checkbox focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="collaborator_<%= collaborator.id %>" class="font-medium text-gray-700"><%= collaborator.email %></label>
                      <p class="text-gray-500"><%= collaborator.name.present? ? collaborator.name : "Sin nombre" %></p>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </fieldset>

          <!-- JavaScript para los botones de selección rápida -->
          <script>
            document.addEventListener('DOMContentLoaded', function() {
              const selectAllBtn = document.getElementById('select-all-members');
              const deselectAllBtn = document.getElementById('deselect-all-members');
              const checkboxes = document.querySelectorAll('.member-checkbox');

              selectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = true);
              });

              deselectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => checkbox.checked = false);
              });
            });
          </script>
        </div>
      </div>
      <% elsif @section == 'access' && current_organization&.client_access_enabled %>
      <!-- Acceso a Plataforma -->
      <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-4 mb-6">
        <div class="mb-4 border-b border-gray-200 pb-2">
          <h3 class="text-md font-medium text-gray-800">Acceso a Plataforma</h3>
          <p class="text-sm text-gray-600">Gestiona el acceso del cliente a la plataforma.</p>
        </div>

        <% organization = current_organization %>
        <% if organization&.client_access_enabled %>
          <% org_user = OrganizationUser.find_by(user: @client, organization: organization, role: :client) %>

          <div class="bg-white p-6 rounded border border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-lg font-medium text-gray-900">Estado de acceso</h4>
                <p class="text-sm text-gray-600 mt-1">
                  <% if org_user&.access_enabled %>
                    Este cliente tiene acceso a la plataforma.
                    <% if org_user.invitation_sent_at %>
                      <br>Última invitación enviada: <%= l(org_user.invitation_sent_at, format: :short) %>
                    <% else %>
                      <br>No se ha enviado ninguna invitación.
                    <% end %>
                  <% else %>
                    Este cliente no tiene acceso a la plataforma.
                  <% end %>
                </p>
              </div>

              <div class="flex space-x-2">
                <% if org_user&.access_enabled %>
                  <%= button_to send_invitation_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700" do %>
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Reenviar Invitación
                  <% end %>
                  <%= button_to toggle_access_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700" do %>
                    Deshabilitar Acceso
                  <% end %>
                <% else %>
                  <%= button_to toggle_access_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700" do %>
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                    </svg>
                    Habilitar Acceso
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>

          <div class="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  Al habilitar el acceso, el cliente recibirá una invitación por correo electrónico para acceder a la plataforma.
                </p>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <% end %>

      <div class="flex flex-col sm:flex-row sm:justify-between gap-3 mt-6 pt-4 border-t border-gray-200">
        <div class="flex gap-2">
          <%= link_to collaborator_clients_path, class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Volver a Clientes
          <% end %>
        </div>

        <%= form.submit "Guardar Cambios", class: "inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700" %>
      </div>
    <% end %>
  </div>
</div>
