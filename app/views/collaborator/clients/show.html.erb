<% content_for :full_width, true %>

<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON><PERSON> de la página -->
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Detalles del Cliente</h1>
        <p class="mt-1 text-sm text-gray-500">Información del cliente y su acceso a la plataforma.</p>
      </div>
      <div class="flex space-x-2">
        <% if @client_organization&.rut.blank? && can_edit_client?(@client) %>
          <%= link_to validate_fiscal_data_collaborator_clients_path, class: "btn btn-success" do %>
            <i class="ri-magic-line mr-2"></i>
            Obtener Datos Fiscales
          <% end %>
        <% end %>
        <%= link_to edit_collaborator_client_path(@client), class: "btn btn-primary" do %>
          <i class="ri-edit-line mr-2"></i>
          Editar
        <% end %>
        <%= link_to collaborator_clients_path, class: "btn btn-secondary" do %>
          <i class="ri-arrow-left-line mr-2"></i>
          Volver
        <% end %>
      </div>
    </div>

    <!-- Estado de acceso a la plataforma -->
    <% organization = current_organization %>
    <% if organization&.client_access_enabled %>
      <% org_user = OrganizationUser.find_by(user: @client, organization: organization, role: :client) %>
      <% if org_user&.access_enabled %>
        <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3 flex justify-between w-full">
              <div>
                <p class="text-sm text-green-700">
                  Este cliente tiene acceso a la plataforma.
                  <% if org_user.invitation_sent_at %>
                    Última invitación enviada: <%= l(org_user.invitation_sent_at, format: :short) %>
                  <% else %>
                    No se ha enviado ninguna invitación.
                  <% end %>
                </p>
              </div>
              <div class="flex space-x-2">
                <%= button_to send_invitation_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" do %>
                  <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  Reenviar Invitación
                <% end %>
                <%= button_to toggle_access_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-red-600 hover:bg-red-700" do %>
                  <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                  </svg>
                  Deshabilitar Acceso
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-gray-50 border-l-4 border-gray-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3 flex justify-between w-full">
              <div>
                <p class="text-sm text-gray-700">
                  Este cliente no tiene acceso a la plataforma.
                </p>
              </div>
              <div>
                <%= button_to toggle_access_collaborator_client_path(@client), method: :post, class: "inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700" do %>
                  <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                  </svg>
                  Habilitar Acceso
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>

    <!-- Alerta de RUT faltante -->
    <% if @client_organization&.rut.blank? %>
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              Este cliente no tiene RUT registrado. Para obtener los datos fiscales automáticamente, haz clic en "Obtener Datos Fiscales".
            </p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Pestañas -->
    <%= render 'shared/clients/client_tabs', client: @client, namespace: 'collaborator', current_tab: @tab || 'info' %>

    <% if @tab.nil? || @tab == 'info' %>
      <!-- Información General -->
      <%= render 'shared/clients/card', title: 'Información General', card_id: 'info-card' do %>
        <div class="max-w-3xl mx-auto w-full">
          <!-- Estado de Acceso -->
          <%= render 'shared/clients/access_status', 
                     client: @client, 
                     namespace: 'collaborator', 
                     organization: current_organization %>
          
          <div class="grid grid-cols-1 gap-6">
            <!-- Información de la organización -->
            <%= render 'shared/clients/organization_info', 
                       client_organization: @client_organization,
                       show_fiscal_link: true,
                       namespace: 'collaborator' %>
            
            <!-- Información personal -->
            <div class="mt-8">
              <h2 class="text-lg font-medium text-gray-900 mb-4">Información de Contacto</h2>
              
              <div class="mb-4">
                <h3 class="text-sm font-medium text-gray-500">Email</h3>
                <p class="mt-1 text-sm text-gray-900"><%= @client.email %></p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="mb-4">
                  <h3 class="text-sm font-medium text-gray-500">Nombre</h3>
                  <p class="mt-1 text-sm text-gray-900"><%= @client.name %></p>
                </div>

                <div class="mb-4">
                  <h3 class="text-sm font-medium text-gray-500">Apellido</h3>
                  <p class="mt-1 text-sm text-gray-900"><%= @client.last_name %></p>
                </div>
                
                <div class="mb-4">
                  <h3 class="text-sm font-medium text-gray-500">Teléfono</h3>
                  <p class="mt-1 text-sm text-gray-900"><%= @client.phone || "-" %></p>
                </div>
                
                <div class="mb-4">
                  <h3 class="text-sm font-medium text-gray-500">Cargo</h3>
                  <p class="mt-1 text-sm text-gray-900"><%= @client.position || "-" %></p>
                </div>
              </div>

              <div class="mb-4">
                <div class="flex justify-between items-center">
                  <h3 class="text-sm font-medium text-gray-500">Miembros Asignados</h3>
                  <% if @client.assigned_members.any? || @client.assigned_to.present? %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                      <%= @client.assigned_members.count + (@client.assigned_to.present? && !@client.assigned_members.include?(@client.assigned_to) ? 1 : 0) %> miembro(s)
                    </span>
                  <% end %>
                </div>

                <% if @client.assigned_members.any? || @client.assigned_to.present? %>
                  <div class="mt-2 border border-gray-200 rounded-md overflow-hidden">
                    <ul class="divide-y divide-gray-200">
                      <% if @client.assigned_to.present? && !@client.assigned_members.include?(@client.assigned_to) %>
                        <li class="px-4 py-2 flex items-center justify-between bg-gray-50">
                          <div>
                            <span class="text-sm font-medium text-gray-900"><%= @client.assigned_to.email %></span>
                            <% if @client.assigned_to.name.present? %>
                              <p class="text-xs text-gray-500"><%= @client.assigned_to.name %> <%= @client.assigned_to.last_name %></p>
                            <% end %>
                            <%
                              # Obtener las organizaciones del miembro
                              member_orgs = @client.assigned_to.organizations
                              if member_orgs.any?
                            %>
                              <p class="text-xs text-gray-400 mt-1">
                                <% if member_orgs.include?(current_organization) %>
                                  <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <%= current_organization.name %>
                                  </span>
                                <% end %>
                                <% member_orgs.reject { |org| org == current_organization }.each do |org| %>
                                  <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <%= org.name %>
                                  </span>
                                <% end %>
                              </p>
                            <% end %>
                          </div>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Principal</span>
                        </li>
                      <% end %>
                      <% @client.assigned_members.each do |member| %>
                        <li class="px-4 py-2 flex items-center justify-between">
                          <div>
                            <span class="text-sm font-medium text-gray-900"><%= member.email %></span>
                            <% if member.name.present? %>
                              <p class="text-xs text-gray-500"><%= member.name %> <%= member.last_name %></p>
                            <% end %>
                            <%
                              # Obtener las organizaciones del miembro
                              member_orgs = member.organizations
                              if member_orgs.any?
                            %>
                              <p class="text-xs text-gray-400 mt-1">
                                <% if member_orgs.include?(current_organization) %>
                                  <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <%= current_organization.name %>
                                  </span>
                                <% end %>
                                <% member_orgs.reject { |org| org == current_organization }.each do |org| %>
                                  <span class="inline-flex items-center mr-1 px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <%= org.name %>
                                  </span>
                                <% end %>
                              </p>
                            <% end %>
                          </div>
                          <div class="flex items-center">
                            <% if member.id == @client.assigned_to_id %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Principal</span>
                            <% end %>
                            <% unless member_orgs.include?(current_organization) %>
                              <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">Externa</span>
                            <% end %>
                          </div>
                        </li>
                      <% end %>
                    </ul>
                  </div>
                <% else %>
                  <p class="mt-1 text-sm text-gray-900 italic">No hay miembros asignados</p>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% elsif @tab == 'fiscal' %>
      <!-- Pestaña de Datos Fiscales -->
      <%= render 'shared/clients/card', title: 'Datos Fiscales', card_id: 'fiscal-card' do %>
        <% if @client_organization.present? %>
          <% if @client_organization.rut.present? %>
            <%= form_with url: update_fiscal_data_collaborator_clients_path, method: :patch, class: 'w-full' do |form| %>
              <div class="max-w-3xl mx-auto w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <%= hidden_field_tag "organization[id]", @client_organization.id %>
                  
                  <div>
                    <label for="organization_nombre_legal" class="block text-sm font-medium text-gray-700 mb-1">Razón Social</label>
                    <%= text_field_tag "organization[nombre_legal]", @client_organization.nombre_legal, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                  
                  <div>
                    <label for="organization_nombre_fantasia" class="block text-sm font-medium text-gray-700 mb-1">Nombre de Fantasía</label>
                    <%= text_field_tag "organization[nombre_fantasia]", @client_organization.nombre_fantasia, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                  
                  <div>
                    <label for="organization_rut" class="block text-sm font-medium text-gray-700 mb-1">RUT</label>
                    <%= text_field_tag "organization[rut]", RutValidatorService.format(@client_organization.rut, true), 
                                    disabled: true, 
                                    class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md bg-gray-50",
                                    placeholder: "12345678-9", 
                                    data: { controller: "rut", action: "change->rut#format" } %>
                  </div>

                  <div>
                    <label for="organization_email_facturacion" class="block text-sm font-medium text-gray-700 mb-1">Email de Facturación</label>
                    <%= email_field_tag "organization[email_facturacion]", @client_organization.email_facturacion, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                  
                  <div class="md:col-span-2">
                    <label for="organization_direccion" class="block text-sm font-medium text-gray-700 mb-1">Dirección</label>
                    <%= text_field_tag "organization[direccion]", @client_organization.direccion, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                  </div>
                </div>

                <div class="mt-8 pt-6 border-t border-gray-200">
                  <div class="flex justify-end">
                    <div class="flex-shrink-0">
                      <%= form.button type: 'submit', 
                                  class: 'inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700' do %>
                        <i class="ri-save-line mr-2"></i>
                        <span>Guardar Datos Fiscales</span>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          <% else %>
            <div class="text-center py-8">
              <p class="text-gray-500 mb-4">El cliente no tiene datos fiscales validados.</p>
              <% if can_edit_client?(@client) %>
                <%= link_to validate_fiscal_data_collaborator_clients_path, 
                          class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700" do %>
                  <i class="ri-magic-line mr-2"></i>
                  Obtener Datos Fiscales
                <% end %>
              <% end %>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">El cliente no tiene ninguna organización asociada.</p>
          </div>
        <% end %>
      <% end %>
    <% elsif @tab == 'documents' %>
      <!-- Pestaña de Documentos -->
      <%= render 'shared/clients/card', title: 'Documentos', card_id: 'documents-card' do %>
        <div class="p-6">
          <p class="text-gray-500 text-sm italic">La sección de documentos estará disponible próximamente.</p>
        </div>
      <% end %>
    <% elsif @tab == 'activity' %>
      <!-- Pestaña de Actividad -->
      <%= render 'shared/clients/card', title: 'Historial de Actividad', card_id: 'activity-card' do %>
        <div class="p-6">
          <p class="text-gray-500 text-sm italic">El historial de actividad estará disponible próximamente.</p>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
