<div class="bg-white shadow-sm rounded-lg overflow-hidden w-full border border-gray-200">
  <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
    <h1 class="text-2xl font-bold text-gray-800">Nuevo Cliente</h1>
    <p class="text-gray-600 mt-1 text-sm">
      Ingresa los datos básicos del cliente
    </p>
  </div>

  <div class="p-6">
    <%= form_with url: create_basic_collaborator_clients_path, method: :post, class: "space-y-8 w-full", data: { turbo: false } do |form| %>
      <!-- Datos básicos de la organización -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Datos de la organización</h3>
        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <div class="space-y-4">
            <div class="w-full">
              <%= form.label :organization_name, "Nombre de la Organización", class: "form-field-label" %>
              <%= form.text_field :organization_name, 
                                class: "form-field-input w-full", 
                                required: true,
                                placeholder: "Ej: Empresa Ejemplo S.A." %>
            </div>

            <div class="w-full">
              <%= form.label :organization_address, "Dirección", class: "form-field-label" %>
              <%= form.text_field :organization_address, 
                                class: "form-field-input w-full",
                                placeholder: "Dirección de la organización" %>
            </div>
          </div>
        </div>
      </div>

      <!-- Datos básicos del cliente -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Datos de contacto</h3>
        <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Datos del usuario (contacto) -->
            <div class="w-full">
              <%= form.label :name, "Nombre", class: "form-field-label" %>
              <%= form.text_field :name, 
                                class: "form-field-input w-full", 
                                required: true,
                                placeholder: "Nombre del contacto" %>
            </div>

            <div class="w-full">
              <%= form.label :last_name, "Apellido", class: "form-field-label" %>
              <%= form.text_field :last_name, 
                                class: "form-field-input w-full",
                                placeholder: "Apellido del contacto" %>
                </div>
              </div>

              <div>
                <%= form.label :email, "Email", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.email_field :email, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>

              <div>
                <%= form.label :phone, "Teléfono", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.telephone_field :phone, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Asignación de cliente -->
        <% if current_user.owner? || current_user.superadmin? || current_user.support? %>
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">Asignación</h3>
            <div class="bg-gray-50 p-6 rounded border border-gray-200 max-w-2xl mx-auto">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
          </div>

          <div class="w-full">
            <%= form.label :phone, "Teléfono", class: "form-field-label" %>
            <%= form.telephone_field :phone, 
                                   class: "form-field-input w-full",
                                   placeholder: "+56 9 1234 5678" %>
          </div>
        </div>
      </div>

      <!-- Asignación de cliente -->
      <% if current_user.owner? || current_user.superadmin? || current_user.support? %>
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-3">Asignación</h3>
          <div class="bg-gray-50 p-6 rounded border border-gray-200 max-w-2xl mx-auto">
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div class="sm:col-span-2">
                <%= form.label :assigned_to_id, "Asignar a Colaborador", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.collection_select :assigned_to_id,
                                          current_organization.collaborators,
                                          :id,
                                          :email,
                                          { include_blank: "Seleccionar colaborador...", selected: current_user.id },
                                          { class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" } %>
                </div>
                <p class="mt-1 text-xs text-gray-500">Si no seleccionas un colaborador, el cliente será asignado a ti.</p>
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <%= form.hidden_field :assigned_to_id, value: current_user.id %>
      <% end %>

      <!-- Campos ocultos para datos fiscales -->
      <%= form.hidden_field :fiscal_razon_social, id: "fiscalRazonSocial" %>
      <%= form.hidden_field :fiscal_rut, id: "fiscalRut" %>
      <%= form.hidden_field :fiscal_tipo_entidad, id: "fiscalTipoEntidad" %>
      <%= form.hidden_field :fiscal_direccion, id: "fiscalDireccion" %>
      <%= form.hidden_field :fiscal_telefono, id: "fiscalTelefono" %>
      <%= form.hidden_field :fiscal_email, id: "fiscalEmail" %>

      <div class="pt-4 border-t border-gray-200 mt-8">
        <div class="flex flex-col sm:flex-row justify-end gap-3">
          <%= link_to "Cancelar", 
                    collaborator_clients_path, 
                    class: "btn btn-secondary w-full sm:w-auto" %>
          <button type="submit" class="btn btn-primary w-full sm:w-auto">
            <i class="ri-arrow-right-line mr-2"></i>Siguiente
          </button>
        </div>
      </div>
    <% end %>
  </div>
</div>
