<%# Vista de búsqueda de clientes con Hotwire (Turbo Frames) %>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow rounded-lg p-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">Buscar Cliente</h1>
    
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Buscar por RUT</h2>
      <%= render 'shared/rut_search' %>
    </div>
    
    <div class="border-t border-gray-200 pt-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Resultados</h2>
      
      <%# Turbo Frame para los resultados de búsqueda %>
      <%= turbo_frame_tag "search-results", 
                         class: "w-full",
                         data: { 
                           controller: "search-results",
                           action: "turbo:before-fetch-response->search-results#handleResponse" 
                         } do %>
        
        <%# Estado inicial %>
        <% if params[:rut].blank? %>
          <div class="text-center py-8">
            <p class="text-gray-500">
              Ingrese un RUT para buscar clientes
            </p>
          </div>
        <% else %>
          <%# Estado de carga (se muestra temporalmente hasta que llegue la respuesta) %>
          <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-3 text-gray-700">Buscando clientes...</span>
          </div>
        <% end %>
      <% end %>
      
      <%# Plantilla para mostrar cuando no hay resultados %>
      <template id="no-results-template">
        <div class="text-center py-8">
          <p class="text-gray-500">No se encontraron clientes con el RUT proporcionado.</p>
        </div>
      </template>
      
      <%# Plantilla para mostrar cuando hay un error %>
      <template id="error-template">
        <div class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error en la búsqueda</h3>
              <div class="mt-2 text-sm text-red-700">
                <p>No se pudo completar la búsqueda. Por favor, intente nuevamente.</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</div>
