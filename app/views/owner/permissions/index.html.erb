<div class="bg-white shadow-md rounded-lg overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Permisos de Usuario</h1>
    <p class="text-gray-600 mt-1">
      Gestiona los permisos para <span class="font-medium"><%= @user.email %></span>
      (<span class="capitalize"><%= @user.role %></span>)
    </p>
  </div>

  <div class="p-6">
    <% if @user.member? %>
      <div class="mb-8">
        <h2 class="text-lg font-semibold text-gray-700 mb-4">Permisos Actuales</h2>
        
        <% if @permissions.any? %>
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
              <% @permissions.each do |permission| %>
                <li class="px-4 py-4 sm:px-6 flex items-center justify-between">
                  <div>
                    <h3 class="text-sm font-medium text-indigo-600"><%= permission.name %></h3>
                    <p class="text-sm text-gray-500"><%= permission.description_text %></p>
                  </div>
                  <div>
                    <%= button_to user_permission_path(@user, permission), 
                                method: :delete, 
                                class: "inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",
                                form: { data: { turbo_confirm: "¿Estás seguro de que deseas revocar este permiso?" } } do %>
                      Revocar
                    <% end %>
                  </div>
                </li>
              <% end %>
            </ul>
          </div>
        <% else %>
          <div class="bg-gray-50 p-4 rounded-md">
            <p class="text-gray-500 text-center">Este usuario no tiene permisos adicionales.</p>
          </div>
        <% end %>
      </div>

      <% if @available_permissions.any? %>
        <div class="mb-8">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Asignar Nuevo Permiso</h2>
          
          <%= form_with url: user_permissions_path(@user), method: :post, class: "space-y-4" do |form| %>
            <div>
              <label for="permission_name" class="block text-sm font-medium text-gray-700">Permiso</label>
              <select name="permission_name" id="permission_name" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <% @available_permissions.each do |permission_name| %>
                  <% unless @current_permissions.include?(permission_name) %>
                    <option value="<%= permission_name %>"><%= permission_name %> - <%= Permission.permission_descriptions[permission_name] %></option>
                  <% end %>
                <% end %>
              </select>
            </div>
            
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700">Descripción (opcional)</label>
              <textarea name="description" id="description" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border border-gray-300 rounded-md"></textarea>
              <p class="mt-2 text-sm text-gray-500">Puedes proporcionar una descripción personalizada para este permiso.</p>
            </div>
            
            <div class="flex justify-end">
              <button type="submit" class="btn btn-primary">
                Asignar Permiso
              </button>
            </div>
          <% end %>
        </div>
      <% end %>
    <% else %>
      <div class="bg-yellow-50 p-4 rounded-md">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Información</h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>
                Los permisos adicionales solo pueden ser asignados a usuarios con rol de Miembro.
                <% case @user.role %>
                <% when 'superadmin' %>
                  Los Superadministradores tienen todos los permisos por defecto.
                <% when 'support' %>
                  Los usuarios de Soporte tienen permisos especiales por defecto.
                <% when 'admin' %>
                  Los Administradores tienen todos los permisos de gestión por defecto.
                <% when 'client' %>
                  Los Clientes no pueden tener permisos adicionales.
                <% end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
    <%= link_to "Volver a detalles del usuario", user_path(@user), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    <%= link_to "Volver a lista de usuarios", users_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
  </div>
</div>
