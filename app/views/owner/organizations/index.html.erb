<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Gestionar Organizaciones</h1>
      <p class="mt-1 text-sm text-gray-500">Administra las organizaciones de tu cuenta.</p>
    </div>

    <!-- Tarjeta principal -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
      <!-- Encabezado con título y acciones -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Lista de Organizaciones</h2>
          <% if current_user.has_system_permission?('multiple_organizations_enabled') || @organizations.empty? %>
            <%= link_to new_owner_organization_path, class: "btn btn-primary" do %>
              <i class="ri-add-line mr-2"></i>
              Nueva Organización
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Contenido principal -->
      <div class="p-6">

        <% if @organizations.size > 1 && !current_user.has_system_permission?('multiple_organizations_enabled') %>
          <div class="mb-6 rounded-md bg-blue-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="ri-information-line text-blue-400 text-xl"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Nota sobre múltiples organizaciones</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>Actualmente tienes múltiples organizaciones. Puedes seguir gestionándolas normalmente, pero la creación de nuevas organizaciones ha sido desactivada por el administrador del sistema.</p>
                  <% if current_user.permission_override_status('multiple_organizations_enabled') == true %>
                    <p class="mt-2 font-medium">Sin embargo, se te ha otorgado permiso especial para crear múltiples organizaciones.</p>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <% if @organizations.any? %>
          <div class="space-y-4">
            <% @organizations.each do |org| %>
              <div class="bg-white p-6 rounded-lg border <%= org.id == current_organization&.id ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200' %> relative">
                <% if org.id == current_organization&.id %>
                  <span class="absolute top-0 right-0 inline-flex items-center px-3 py-1 rounded-bl-md text-xs font-medium bg-indigo-100 text-indigo-800">
                    <i class="ri-check-line mr-1"></i>
                    Activa
                  </span>
                <% end %>
                
                <div class="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                  <div class="min-w-0">
                    <h3 class="text-lg font-medium text-gray-900 truncate"><%= org.name %></h3>
                    <% if org.rut.present? %>
                      <p class="mt-1 text-sm text-gray-500">RUT: <%= org.rut %></p>
                    <% end %>
                    <% if org.email_facturacion.present? %>
                      <p class="mt-1 text-sm text-gray-500">Email: <%= org.email_facturacion %></p>
                    <% end %>
                  </div>

                  <div class="flex flex-wrap gap-2">
                    <%= link_to edit_owner_organization_path(org, section: 'general'), 
                                class: "btn btn-sm btn-secondary inline-flex items-center" do %>
                      <i class="ri-pencil-line mr-1.5"></i>
                      Editar
                    <% end %>
                    
                    <% if org.id == current_organization&.id %>
                      <button class="btn btn-sm btn-secondary inline-flex items-center">
                        <i class="ri-check-line mr-1.5"></i>
                        Activa
                      </button>
                    <% else %>
                      <%= form_with(url: switch_organization_owner_organization_path(org), method: :post, class: "inline-block") do |f| %>
                        <button type="submit" class="btn btn-sm btn-secondary inline-flex items-center">
                          <i class="ri-swap-line mr-1.5"></i>
                          Usar esta
                        </button>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-12">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
              <i class="ri-building-line text-gray-400 text-xl"></i>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No hay organizaciones</h3>
            <p class="mt-1 text-sm text-gray-500">Comienza creando una nueva organización.</p>
            <div class="mt-6">
              <%= link_to new_owner_organization_path, class: "btn btn-primary" do %>
                <i class="ri-add-line mr-2"></i>
                Nueva Organización
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
