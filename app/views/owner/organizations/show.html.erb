<div class="bg-white shadow-md rounded-lg overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Detalles de la Organización</h1>
    <p class="text-sm text-gray-600 mt-1">Aquí puedes ver todos los datos de tu organización, sus miembros y la información de facturación.</p>
  </div>

  <div class="p-6">
    <!-- Información General -->
    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
      <div class="flex flex-wrap justify-between items-center mb-4 gap-2">
        <div>
          <h2 class="text-lg font-medium text-gray-900">Información General</h2>
          <p class="text-sm text-gray-600 mt-1">Datos básicos de la organización.</p>
        </div>
        <% if current_user.admin_of?(@organization) %>
          <%= link_to edit_owner_organization_path(@organization, section: 'general'), class: 'btn btn-primary' do %>
            <i class="ri-pencil-line mr-2"></i>
            Editar
          <% end %>
        <% end %>
      </div>

      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2 bg-white p-4 rounded-md shadow-sm">
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Nombre</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.name %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Fecha de creación</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.created_at.strftime("%d/%m/%Y %H:%M") %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Última actualización</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.updated_at.strftime("%d/%m/%Y %H:%M") %></dd>
        </div>
      </dl>
    </div>

    <!-- Sección de Miembros -->
    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 mb-8">
      <div class="flex flex-wrap justify-between items-center mb-4 gap-2">
        <div>
          <h2 class="text-lg font-medium text-gray-900">Miembros de la Organización</h2>
          <p class="text-sm text-gray-600 mt-1">Gestiona los usuarios que pertenecen a esta organización.</p>
        </div>
        <% if current_user.admin_of?(@organization) %>
          <%= link_to edit_owner_organization_path(@organization, section: 'members'), class: 'btn btn-primary' do %>
            <i class="ri-team-line mr-2"></i>
            Gestionar
          <% end %>
        <% end %>
      </div>

      <div class="bg-white p-4 rounded-md shadow-sm">
        <div class="flex justify-between items-center">
          <% total_members = (@organization.owners + @organization.collaborators + @organization.clients).count %>
          <p class="text-sm text-gray-900">
            <% if total_members > 0 %>
              Esta organización tiene <%= total_members %> <%= total_members == 1 ? 'miembro' : 'miembros' %>.
            <% else %>
              No hay miembros en esta organización.
            <% end %>
          </p>
          <% if current_user.admin_of?(@organization) %>
            <%= link_to edit_owner_organization_path(@organization, section: 'members'), class: 'btn btn-secondary' do %>
              <i class="ri-user-add-line mr-2"></i>
              Agregar Miembro
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Datos de Facturación -->
    <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
      <div class="flex flex-wrap justify-between items-center mb-4 gap-2">
        <div>
          <h2 class="text-lg font-medium text-gray-900">Datos de Facturación</h2>
          <p class="text-sm text-gray-600 mt-1">Información fiscal necesaria para la emisión de facturas.</p>
        </div>
        <% if current_user.admin_of?(@organization) %>
          <%= link_to edit_owner_organization_path(@organization, section: 'billing'), class: 'btn btn-primary' do %>
            <i class="ri-pencil-line mr-2"></i>
            Editar
          <% end %>
        <% end %>
      </div>

      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2 bg-white p-4 rounded-md shadow-sm">
        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Razón Social</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.name %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">RUT</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.rut.present? ? @organization.rut : "No especificado" %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Dirección</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.direccion.present? ? @organization.direccion : "No especificada" %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Email de Facturación</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.email_facturacion.present? ? @organization.email_facturacion : "No especificado" %></dd>
        </div>

        <div class="sm:col-span-1">
          <dt class="text-sm font-medium text-gray-500">Teléfono</dt>
          <dd class="mt-1 text-sm text-gray-900"><%= @organization.telefono.present? ? @organization.telefono : "No especificado" %></dd>
        </div>
      </dl>
    </div>
  </div>

  <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-wrap gap-2">
    <%= link_to owner_organizations_path, class: 'btn btn-secondary' do %>
      <i class="ri-arrow-left-line mr-2"></i>
      Volver a Organizaciones
    <% end %>
  </div>
</div>
