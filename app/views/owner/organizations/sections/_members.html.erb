<%# Sección de Colaboradores - Organización %>
<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Gestión de Colaboradores</h1>
      <p class="mt-1 text-sm text-gray-500">Administra los usuarios que tienen acceso a esta organización.</p>
    </div>

    <!-- Tarjeta principal -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
      <!-- Encabezado con título y acciones -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Lista de Colaboradores</h2>
          <% if current_user&.permission?('invite_collaborator') %>
            <a href="#"
               data-action="click->modal#open"
               data-modal-url="<%= new_owner_organization_collaborator_path(@organization) %>"
               class="btn btn-primary">
              <i class="ri-add-line mr-2"></i>
              Nuevo Colaborador
            </a>
          <% end %>
        </div>
      </div>

    <!-- Contenido principal -->
    <div class="p-6">
      <!-- Barra de búsqueda -->
      <div class="mb-6">
        <label for="collaborator-search" class="block text-sm font-medium text-gray-700 mb-1">Buscar colaboradores</label>
        <div class="relative flex items-stretch w-full">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-search-line text-gray-400"></i>
          </div>
          <input type="search" 
                 id="collaborator-search" 
                 class="search-input pl-10 pr-4 py-2 w-full" 
                 placeholder="Buscar por nombre o email..."
                 data-controller="search"
                 data-action="input->search#filter"
                 data-search-target="input"
                 autocomplete="off">
          <button type="button" 
                  class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                  data-action="click->search#clear"
                  title="Limpiar búsqueda">
            <i class="ri-close-line"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="flex flex-col">
      <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          <% if @organization.collaborators.any? %>
            <div class="space-y-4">
              <% @organization.collaborators.each do |collaborator| %>
                <div class="bg-white p-6 rounded-lg border border-gray-200 relative searchable" 
                     data-search="<%= collaborator.user.full_name.downcase %> <%= collaborator.user.email.downcase %>">
                  
                  <% if current_user == collaborator.user %>
                    <span class="absolute top-0 right-0 inline-flex items-center px-3 py-1 rounded-bl-md text-xs font-medium bg-indigo-100 text-indigo-800">
                      <i class="ri-check-line mr-1"></i>
                      Tú
                    </span>
                  <% end %>
                  
                  <div class="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                    <div class="min-w-0">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 font-medium">
                          <%= collaborator.user.initials %>
                        </div>
                        <div class="ml-4">
                          <h3 class="text-lg font-medium text-gray-900"><%= collaborator.user.full_name %></h3>
                          <p class="mt-1 text-sm text-gray-500"><%= collaborator.user.email %></p>
                          <% if collaborator.user.phone.present? %>
                            <p class="mt-1 text-sm text-gray-500"><i class="ri-phone-line mr-1"></i><%= collaborator.user.phone %></p>
                          <% end %>
                        </div>
                      </div>
                    </div>

                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 mt-2 sm:mt-0">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="ri-shield-user-line mr-1"></i>
                        <%= collaborator.role.humanize %>
                      </span>
                      
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= collaborator.active? ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                        <i class="ri-<%= collaborator.active? ? 'check' : 'time' %>-line mr-1"></i>
                        <%= collaborator.active? ? 'Activo' : 'Inactivo' %>
                      </span>
                    </div>

                    <div class="flex items-center space-x-2">
                      <button type="button" 
                              data-action="click->modal#open" 
                              data-modal-url="<%= edit_owner_organization_collaborator_path(@organization, collaborator) %>"
                              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                        <i class="ri-pencil-line mr-1.5"></i>
                        Editar
                      </button>
                      
                      <% if current_user != collaborator.user %>
                        <button type="button" 
                                data-action="click->modal#open" 
                                data-modal-url="<%= owner_organization_collaborator_path(@organization, collaborator) %>"
                                data-modal-method="delete"
                                data-confirm="¿Estás seguro de eliminar a este colaborador?"
                                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200">
                          <i class="ri-delete-bin-line mr-1.5"></i>
                          Eliminar
                        </button>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
                <i class="ri-team-line text-gray-400 text-xl"></i>
              </div>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No hay colaboradores</h3>
              <p class="mt-1 text-sm text-gray-500">Comienza agregando colaboradores a esta organización.</p>
              <div class="mt-6">
                <a href="#" 
                   data-action="click->modal#open" 
                   data-modal-url="<%= new_owner_organization_collaborator_path(@organization) %>"
                   class="btn btn-primary">
                  <i class="ri-user-add-line mr-2"></i>
                  Nuevo Colaborador
                </a>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
