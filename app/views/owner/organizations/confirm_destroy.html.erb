<div class="max-w-4xl mx-auto px-4 py-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Confirmar eliminación de organización
      </h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">
        Esta acción no se puede deshacer. Por favor, confirme que desea eliminar esta organización.
      </p>
    </div>

    <div class="bg-white px-4 py-5 sm:p-6">
      <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">
              <strong>¡Advertencia!</strong> Estás a punto de eliminar la organización <strong><%= @organization.name %></strong>. 
              Esta acción es irreversible y eliminará todos los datos asociados a esta organización.
            </p>
          </div>
        </div>
      </div>

      <div class="mt-6">
        <p class="text-sm text-gray-700 mb-4">
          Para confirmar que deseas eliminar esta organización, escribe <code class="bg-gray-100 px-2 py-1 rounded">ELIMINAR</code> en el siguiente campo:
        </p>

        <%= form_with(model: @organization, url: owner_organization_path(@organization, section: @section), method: :delete, class: "space-y-6") do |f| %>
          <div class="sm:col-span-3">
            <label for="confirmation" class="block text-sm font-medium text-gray-700">Confirmar eliminación</label>
            <div class="mt-1">
              <input type="text" name="confirmation" id="confirmation" required
                     class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
            </div>
            <p class="mt-2 text-sm text-gray-500">
              Escribe exactamente <code class="bg-gray-100 px-1 py-0.5 rounded">ELIMINAR</code> para confirmar.
            </p>
          </div>

          <div class="pt-5">
            <div class="flex justify-end space-x-3">
              <%= link_to 'Cancelar', edit_owner_organization_path(@organization, section: @section), 
                        class: 'btn btn-secondary' %>
              <button type="submit" class="btn btn-danger">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Eliminar organización permanentemente
              </button>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
