<div>
  <h1 class="text-2xl font-bold mb-2 text-gray-800"><PERSON>rear Nueva Organización</h1>
  <p class="text-sm text-gray-600 mb-2">Ingresa el nombre de tu organización. Podrás completar los datos de facturación más adelante.</p>

  <%= form_with model: @organization, url: owner_organizations_path, method: :post do |form| %>
    <% if @organization.errors.any? %>
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded">
        <h2 class="font-bold mb-2">Se encontraron <%= @organization.errors.count %> errores:</h2>
        <ul class="list-disc pl-5">
          <% @organization.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-4">
      <div class="grid grid-cols-1 gap-4">
        <!-- Nombre de la organización -->
        <div>
          <%= form.label :name, "Nombre de la organización", class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" %>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row sm:justify-between gap-3 mt-6 pt-4">
        <%= link_to home_path, class: "btn btn-secondary inline-flex items-center" do %>
          <i class="ri-arrow-left-line mr-2"></i>
          Cancelar
        <% end %>

        <button type="submit" class="btn btn-primary inline-flex items-center">
          <i class="ri-check-line mr-2"></i>
          Crear Organización
        </button>
      </div>
    </div>
  <% end %>
</div>
