<%# Tarjeta principal del formulario - Fuera del formulario para evitar anidación incorrecta %>
<%= card title: 'Información de la Organización' do %>
  <%= form_with model: @organization, 
                url: owner_organization_path(@organization, section: @section), 
                method: :patch, 
                class: 'w-full',
                data: { 
                  controller: 'form',
                  action: 'turbo:submit-end->form#submitEnd'
                } do |form| %>
    <div class="max-w-4xl mx-auto w-full">
    
    <%# Mensajes de error globales %>
    <% if @organization.errors.any? %>
      <div class="mb-6">
        <%= render 'shared/alert', 
                   type: 'error',
                   title: "Se encontraron #{@organization.errors.count} errores",
                   message: @organization.errors.full_messages.to_sentence %>
      </div>
    <% end %>

    <%# Contenido dinámico según la sección %>
    <div class="space-y-6">
      <% if @section == 'general' %>
        <%= render 'owner/organizations/sections/general', form: form %>
      <% elsif @section == 'billing' %>
        <%= render 'owner/organizations/sections/billing', form: form %>
      <% elsif @section == 'client_access' %>
        <%= render 'owner/organizations/sections/client_access', form: form %>
      <% end %>
    </div>

    <%# Acciones del formulario %>
    <div class="mt-8 pt-6 border-t border-gray-200">
      <div class="flex flex-col sm:flex-row justify-between gap-4">
        <div class="flex flex-wrap gap-3">
          <%= link_to owner_organizations_path, 
                      class: 'btn btn-secondary',
                      data: { turbo_frame: '_top' } do %>
            <i class="ri-arrow-left-line mr-2"></i>
            Volver a Organizaciones
          <% end %>
          
          <% unless local_assigns[:single_org] || @organization.new_record? %>
            <%= link_to confirm_destroy_owner_organization_path(@organization, section: @section), 
                        class: 'btn btn-danger',
                        data: { turbo_frame: 'modal' } do %>
              <i class="ri-delete-bin-line mr-2"></i>
              Eliminar Organización
            <% end %>
          <% end %>
        </div>
        
        <div class="flex-shrink-0">
          <%= form.button type: 'submit', 
                        class: 'btn btn-primary',
                        data: { form_target: 'submitButton', disable_with: "<i class='ri-loader-4-line animate-spin mr-2'></i> Guardando..." } do %>
            <i class="ri-save-line mr-2"></i>
            <span>Guardar Cambios</span>
          <% end %>
        </div>
      </div>
    </div>
    </div><%# Cierre del div max-w-4xl %>
  <% end %><%# Cierre del form_with %>
<% end %><%# Cierre del card %>
