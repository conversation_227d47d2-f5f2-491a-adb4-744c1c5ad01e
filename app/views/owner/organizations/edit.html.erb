<%# Variables de la vista -%>
<% multiple_orgs_enabled = current_user.has_system_permission?('multiple_organizations_enabled') %>
<% single_org = current_user.organizations.count == 1 && !multiple_orgs_enabled && !current_user.superadmin? %>

<div class="space-y-6">
  <!-- Encabezado -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">
        <%= single_org ? 'Mi Organización' : 'Editar Organización' %>
      </h1>
      <p class="mt-1 text-sm text-gray-500">
        <%= single_org ? 'Aquí puedes gestionar los datos de tu organización.' : 'Aquí puedes editar los datos de esta organización.' %>
      </p>
    </div>
  </div>

  <!-- Pestañas de navegación -->
  <%= render 'shared/tabs' do |tabs| %>
    <% tabs.with_tab name: 'Información General', 
                    path: edit_owner_organization_path(@organization, section: 'general'), 
                    active: @section == 'general',
                    icon: 'ri-information-line' %>
    
    <% tabs.with_tab name: 'Datos de Facturación', 
                    path: edit_owner_organization_path(@organization, section: 'billing'), 
                    active: @section == 'billing',
                    icon: 'ri-bill-line' %>
    
    <% tabs.with_tab name: 'Acceso de Clientes', 
                    path: edit_owner_organization_path(@organization, section: 'client_access'), 
                    active: @section == 'client_access',
                    icon: 'ri-user-shared-line' %>
    
    <% tabs.with_tab name: 'Colaboradores', 
                    path: edit_owner_organization_path(@organization, section: 'collaborators'), 
                    active: @section == 'collaborators',
                    icon: 'ri-team-line' %>
  <% end %>

  <!-- Contenido de la sección actual -->
  <% if ['general', 'billing'].include?(@section) %>
    <%= render 'owner/organizations/form', single_org: single_org %>
  <% elsif @section == 'client_access' %>
    <%= render 'owner/organizations/sections/client_access' %>
  <% elsif @section == 'collaborators' %>
    <%= render 'owner/organizations/sections/collaborators' %>
  <% end %>

  <!-- Botones de acción para la sección de miembros -->
  <% if @section == 'members' %>
    <div class="flex flex-col sm:flex-row justify-between gap-4 mt-6 pt-6 border-t border-gray-200">
      <div class="flex flex-wrap gap-3">
        <% unless single_org %>
          <%= button_to owner_organization_path(@organization),
                      method: :delete,
                      params: { confirmation: "ELIMINAR" },
                      class: "btn btn-danger",
                      form: { class: "inline" },
                      data: { turbo_confirm: "¿Estás seguro de que deseas eliminar esta organización? Esta acción no se puede deshacer." } do %>
            <i class="ri-delete-bin-line mr-2"></i>
            Eliminar Organización
          <% end %>
        <% end %>
      </div>
      <div class="flex-shrink-0">
        <%= link_to owner_organization_path(@organization), class: "btn btn-secondary" do %>
          <i class="ri-arrow-left-line mr-2"></i>
          Volver a Detalles
        <% end %>
      </div>
    </div>
  <% end %>
</div>
</div>
