<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- Elemento de prueba para Stimulus -->
    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h2 class="text-lg font-medium text-blue-800 mb-2">Prueba de Stimulus</h2>
      <div data-controller="hello" class="text-blue-700">
        Este texto debería cambiar a "Hello World!" si Stimulus está funcionando correctamente.
      </div>
    </div>
    
    <!-- T<PERSON><PERSON><PERSON> de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Detalles del Cliente</h1>
      <p class="mt-1 text-sm text-gray-500">Información del cliente y su acceso a la plataforma.</p>
    </div>
    
    <!-- Alerta de RUT faltante -->
    <% if @client.client_organizations&.first&.rut.blank? && can_edit_client?(@client) %>
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              Este cliente no tiene RUT registrado. Para obtener los datos fiscales automáticamente, haz clic en "Obtener Datos Fiscales".
            </p>
          </div>
        </div>
      </div>
    <% end %>

    
    <!-- Pestañas -->
    <%= render 'shared/clients/client_tabs', client: @client, namespace: 'owner', current_tab: @tab %>

    <% if @tab == 'info' %>
      <!-- Contenido de Información General -->
      <%= render 'shared/clients/card', title: 'Información General', card_id: 'info-card' do %>
        <%= form_with model: @client, 
                    url: owner_client_path(@client, tab: 'info'), 
                    method: :patch,
                    class: 'w-full' do |form| %>
          <div class="max-w-3xl mx-auto w-full">
            <% if @client.errors.any? %>
              <div class="mb-6">
                <%= render 'shared/alert', 
                          type: 'error',
                          title: "Se encontraron #{@client.errors.count} errores",
                          message: @client.errors.full_messages.to_sentence %>
              </div>
            <% end %>

            <div class="space-y-6">
              <!-- Sección de Datos de la Organización -->
              <div class="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Datos de la Organización</h3>
                </div>
                
                <% if @client.organizations.any? %>
                  <% organization = @client.organizations.first %>
                  <div class="space-y-4">
                    <!-- Nombre de la Organización -->
                    <div class="space-y-1">
                      <%= form.label 'organization[name]', 'Nombre de la Organización', class: 'block text-sm font-medium text-gray-700' %>
                      <div class="relative">
                        <%= text_field_tag 'organization[name]', 
                            organization.name,
                            class: "form-field-input w-full" %>
                      </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <!-- RUT -->
                      <div class="space-y-1">
                        <%= form.label 'organization[rut]', 'RUT', class: 'block text-sm font-medium text-gray-700' %>
                        <div class="relative">
                          <%= text_field_tag 'organization[rut]', 
                              organization.rut,  # Mostrar sin formato
                              class: "form-field-input w-full",
                              placeholder: 'Ej: 123456789012',
                              maxlength: 12,
                              data: { 
                                controller: 'rut',
                                rut_target: 'input',
                                action: 'input->rut#formatRut keypress->rut#handleKeypress change->rut#syncHiddenField',
                                rut_format_value: 'false'  # Desactiva el formateo automático
                              } %>
                          <div class="mt-1 text-xs text-gray-500">
                            Formato: 12 dígitos, donde los dos primeros deben ser entre 01 y 22, seguidos de 8 dígitos y 2 ceros
                          </div>
                        </div>
                      </div>
                      
                      <!-- Teléfono -->
                      <div class="space-y-1">
                        <%= form.label 'organization[telefono]', 'Teléfono', class: 'block text-sm font-medium text-gray-700' %>
                        <div class="relative">
                          <%= text_field_tag 'organization[telefono]', 
                              organization.telefono,
                              class: "form-field-input w-full" %>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Dirección -->
                    <div class="space-y-1">
                      <%= form.label 'organization[direccion]', 'Dirección', class: 'block text-sm font-medium text-gray-700' %>
                      <div class="relative">
                        <%= text_field_tag 'organization[direccion]', 
                            organization.direccion,
                            class: "form-field-input w-full" %>
                      </div>
                    </div>
                  </div>
                <% else %>
                  <div class="text-center py-4">
                    <p class="text-gray-500">No hay organización asignada a este cliente.</p>
                  </div>
                <% end %>
              </div>

              <!-- Información personal del cliente -->
              <%= render 'shared/clients/personal_info_form', 
                         form: form, 
                         disabled_fields: [:email] %>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <%= render 'shared/form_field', form: form, field: :phone, 
                    label: 'Teléfono', 
                    input_type: 'tel',
                    placeholder: '+56 9 1234 5678',
                    wrapper_class: 'mb-4' %>

                <%= render 'shared/form_field', form: form, field: :position, 
                    label: 'Cargo', 
                    input_type: 'text',
                    placeholder: 'Ej: Gerente de Finanzas',
                    wrapper_class: 'mb-4' %>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-end">
                  <div class="flex-shrink-0">
                    <%= form.button type: 'submit', 
                                  class: 'btn btn-primary' do %>
                      <i class="ri-save-line mr-2"></i>
                      <span>Guardar Cambios</span>
                    <% end %>
                  </div>
                </div>
              </div>

              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Datos de registro</h4>
                    <p class="mt-1 text-sm text-gray-500">Registrado el: <%= @client.created_at.strftime('%d de %B de %Y') %></p>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

        <!-- Estado de Acceso Global -->
        <%= render 'shared/clients/card', title: 'Estado de Acceso', card_id: 'access-card' do %>
          <%= render 'shared/clients/access_status', 
                     client: @client, 
                     namespace: 'owner', 
                     organization: current_organization %>
        <% end %>
    <% end %>

  <% elsif @tab == 'fiscal' %>
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900">Datos Fiscales</h3>
      </div>
      
      <div class="p-6">
        <% if @client.client_organizations.any? %>
          <% client_org = @client.client_organizations.first %>
          <%= form_with(model: @client, url: owner_client_path(@client), method: :patch, class: 'space-y-6', 
                  data: { controller: 'fiscal--simplified-rut', 'fiscal--simplified-rut-target': 'fiscalForm' }) do |form| %>
            <%= hidden_field_tag :tab, 'fiscal' %>
            <%= hidden_field_tag :client_id, @client.id %>
            
            <!-- Campo RUT oculto para el controlador -->
            <%= hidden_field_tag 'organization[rut]', client_org.rut, id: 'fiscal_rut_input', data: { simplified_rut_target: 'input' } %>

            <div id="fiscal-data-section">
              <div class="grid grid-cols-1 gap-4">
                <!-- Primera fila: RUT -->
                <div>
                  <div class="flex items-center justify-between mb-1">
                    <div>
                      <label for="rut" class="block text-sm font-medium text-gray-700">RUT</label>
                      <p class="text-xs text-gray-500">
                        Para modificar el RUT, por favor diríjase a la pestaña 'Información General'
                      </p>
                    </div>
                  </div>

                  <div class="flex space-x-2 items-center">
                    <!-- Campo RUT de solo lectura -->
                    <div class="relative" style="width: 200px;">
                      <div class="form-field-input bg-gray-50 text-gray-700 w-full">
                        <%= client_org.rut.present? ? format_rut(client_org.rut) : 'No especificado' %>
                      </div>
                    </div>
                    
                    <!-- Botón de consulta DGI funcional directo con JavaScript vanilla -->
                    <button type="button" 
                            id="dgi-fetch-button"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            title="Consultar datos fiscales a DGI"
                            <%= 'disabled' unless client_org.rut.present? %>>
                      <i class="ri-download-line mr-1"></i>
                      <span>Cargar datos desde DGI</span>
                      <div id="dgi-fetch-spinner" class="hidden ml-2">
                        <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    </button>
                    
                    <script>
                    // Función para manejar la carga de datos fiscales
                    function fetchFiscalDataManually(button, rut) {
                      console.log('Iniciando consulta de datos fiscales para RUT:', rut);
                      
                      // Mostrar spinner
                      const spinner = button.querySelector('#fiscal-spinner');
                      if (spinner) spinner.classList.remove('hidden');
                      
                      // Deshabilitar botón temporalmente
                      button.disabled = true;
                      
                      // Hacer la petición al backend
                      fetch(`/owner/search/fiscal_data?rut=${encodeURIComponent(rut)}`, {
                        headers: {
                          'Accept': 'application/json',
                          'X-Requested-With': 'XMLHttpRequest',
                          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                        }
                      })
                      .then(response => {
                        console.log('Respuesta recibida, estado:', response.status);
                        if (!response.ok) {
                          throw new Error(`Error HTTP: ${response.status}`);
                        }
                        return response.json();
                      })
                      .then(data => {
                        console.log('Datos recibidos:', data);
                        if (data.error) {
                          throw new Error(data.error);
                        }
                        
                        // Aquí iría la lógica para rellenar el formulario con los datos
                        if (data.razon_social) {
                          const nombreLegalInput = document.querySelector('input[name="organization[nombre_legal]"]');
                          if (nombreLegalInput) nombreLegalInput.value = data.razon_social;
                        }
                        
                        // Mostrar mensaje de éxito
                        alert('Datos fiscales cargados correctamente');
                      })
                      .catch(error => {
                        console.error('Error al cargar datos fiscales:', error);
                        alert('Error al cargar datos fiscales: ' + (error.message || 'Error desconocido'));
                      })
                      .finally(() => {
                        // Ocultar spinner y reactivar botón
                        if (spinner) spinner.classList.add('hidden');
                        button.disabled = false;
                      });
                    }
                    </script>
                    
                    <!-- Botón de limpiar (se muestra/oculta desde el controlador) -->
                    <button type="button" 
                          class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 hidden"
                          data-action="simplified-rut#clear"
                          data-simplified-rut-target="clearButton"
                          title="Limpiar datos">
                      <i class="ri-close-line mr-1"></i>
                      <span>Limpiar</span>
                    </button>
                    
                    <!-- Spinner para carga de datos fiscales -->
                    <div class="hidden" data-simplified-rut-target="spinner">
                      <div class="flex items-center justify-center">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600"></div>
                        <span class="ml-2 text-sm text-gray-600">Consultando DGI...</span>
                      </div>
                    </div>
                  </div>
                    
                    <!-- Área para mostrar errores -->
                    <div data-simplified-rut-target="error" class="mt-1 hidden"></div>
                    
                    <!-- Área para mostrar resultados -->
                    <div data-simplified-rut-target="resultsContainer" class="mt-2 hidden"></div>
                  </div>
                </div>
                
                <!-- Primera fila: Razón Social y Nombre de Fantasía -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div class="md:col-span-1">
                    <label for="organization_nombre_legal" class="block text-sm font-medium text-gray-700 mb-1">Razón Social (Nombre Legal)</label>
                    <%= text_field_tag "organization[nombre_legal]", client_org.nombre_legal, class: "form-field-input w-full" %>
                  </div>

                  <div class="md:col-span-1">
                    <label for="organization_nombre_fantasia" class="block text-sm font-medium text-gray-700 mb-1">Nombre de Fantasía</label>
                    <%= text_field_tag "organization[nombre_fantasia]", client_org.nombre_fantasia, class: "form-field-input w-full" %>
                  </div>

                  <!-- Tipo de Entidad -->
                  <div class="md:col-span-1">
                    <label for="organization_tipo_entidad" class="block text-sm font-medium text-gray-700 mb-1">Tipo de Entidad</label>
                    <%= text_field_tag "organization[tipo_entidad]", client_org.tipo_entidad, class: "form-field-input w-full" %>
                  </div>

                  <!-- Dirección -->
                  <div class="md:col-span-1">
                    <label for="organization_direccion" class="block text-sm font-medium text-gray-700 mb-1">Dirección</label>
                    <%= text_field_tag "organization[direccion]", client_org.direccion, class: "form-field-input w-full" %>
                  </div>

                  <!-- Ciudad -->
                  <div class="md:col-span-1">
                    <label for="organization_ciudad" class="block text-sm font-medium text-gray-700 mb-1">Ciudad</label>
                    <%= text_field_tag "organization[ciudad]", client_org.ciudad, class: "form-field-input w-full" %>
                  </div>

                  <!-- Departamento -->
                  <div class="md:col-span-1">
                    <label for="organization_departamento" class="block text-sm font-medium text-gray-700 mb-1">Departamento</label>
                    <%= text_field_tag "organization[departamento]", client_org.departamento, class: "form-field-input w-full" %>
                  </div>

                  <!-- Teléfono -->
                  <div class="md:col-span-1">
                    <label for="organization_telefono" class="block text-sm font-medium text-gray-700 mb-1">Teléfono</label>
                    <%= text_field_tag "organization[telefono]", client_org.telefono, class: "form-field-input w-full" %>
                  </div>

                  <!-- Email -->
                  <div class="md:col-span-1">
                    <label for="organization_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <%= email_field_tag "organization[email]", client_org.email, class: "form-field-input w-full" %>
                  </div>

                  <!-- Estado -->
                  <div class="md:col-span-1">
                    <label for="organization_estado" class="block text-sm font-medium text-gray-700 mb-1">Estado</label>
                    <%= text_field_tag "organization[estado]", client_org.estado, class: "form-field-input w-full" %>
                  </div>

                  <!-- Fecha Inicio Actividades -->
                  <div class="md:col-span-1">
                    <label for="organization_fecha_inicio_actividades" class="block text-sm font-medium text-gray-700 mb-1">Fecha Inicio Actividades</label>
                    <%= text_field_tag "organization[fecha_inicio_actividades]", client_org.fecha_inicio_actividades, class: "form-field-input w-full" %>
                  </div>
                </div>

                <!-- Actividades (ancho completo) -->
                <div class="mt-4">
                  <label for="organization_actividades" class="block text-sm font-medium text-gray-700 mb-1">Actividades</label>
                  <% actividades_array = client_org.actividades.presence || [] %>
                  <%= text_area_tag "organization[actividades]", 
                                  actividades_array.join("\n"), 
                                  class: "form-field-input w-full h-24", 
                                  placeholder: "Ingrese una actividad por línea" %>
                  <p class="mt-1 text-xs text-gray-500">Ingrese una actividad por línea</p>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-end">
                  <div class="flex-shrink-0">
                    <%= form.button type: 'submit', 
                                class: 'btn btn-primary' do %>
                      <i class="ri-save-line mr-2"></i>
                      <span>Guardar Datos Fiscales</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">No hay información fiscal disponible.</p>
            <% if can_edit_client?(@client) %>
              <%= link_to validate_fiscal_data_owner_client_path(@client), 
                        class: 'btn btn-primary' do %>
                <i class="ri-search-line mr-2"></i> Obtener Datos Fiscales
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

  <% elsif @tab == 'billing' %>
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900">Datos de Facturación</h3>
      </div>
      
      <div class="p-6">
        <% if @client.client_organizations.any? %>
          <% client_org = @client.client_organizations.first %>
          <%= form_with model: @client, 
                      url: owner_client_path(@client, tab: 'billing'), 
                      method: :patch,
                      class: 'w-full' do |form| %>
            <div class="max-w-3xl mx-auto w-full">
              <% if client_org.errors.any? %>
                <div class="mb-6">
                  <%= render 'shared/alert', 
                          type: 'error',
                          title: "Se encontraron #{client_org.errors.count} errores",
                          message: client_org.errors.full_messages.to_sentence %>
                </div>
              <% end %>

              <div class="mb-6 bg-blue-50 p-4 rounded-md border-l-4 border-blue-400">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <i class="ri-information-line text-blue-500 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-blue-700">Los datos de facturación son opcionales y solo se utilizan si difieren de los datos fiscales. Si no ingresas datos de facturación, se utilizarán los datos fiscales para la facturación.</p>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                  <label for="organization_billing_name" class="block text-sm font-medium text-gray-700 mb-1">Nombre o Razón Social</label>
                  <%= text_field_tag "organization[billing_name]", client_org.billing_name, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>

                <div>
                  <label for="organization_billing_rut" class="block text-sm font-medium text-gray-700 mb-1">RUT</label>
                  <%= text_field_tag "organization[billing_rut]", client_org.billing_rut, 
                                    class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md",
                                    placeholder: "12345678-9", 
                                    data: { controller: "rut", action: "change->rut#format" } %>
                </div>

                <div>
                  <label for="organization_billing_email" class="block text-sm font-medium text-gray-700 mb-1">Correo para facturación</label>
                  <%= email_field_tag "organization[billing_email]", client_org.billing_email, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>

                <div class="md:col-span-2">
                  <label for="organization_billing_address" class="block text-sm font-medium text-gray-700 mb-1">Dirección</label>
                  <%= text_field_tag "organization[billing_address]", client_org.billing_address, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>

                <div>
                  <label for="organization_billing_comuna" class="block text-sm font-medium text-gray-700 mb-1">Comuna</label>
                  <%= text_field_tag "organization[billing_comuna]", client_org.billing_comuna, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>

                <div>
                  <label for="organization_billing_region" class="block text-sm font-medium text-gray-700 mb-1">Región</label>
                  <%= text_field_tag "organization[billing_region]", client_org.billing_region, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-end">
                  <div class="flex-shrink-0">
                    <%= form.button type: 'submit', 
                                class: 'inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700' do %>
                      <i class="ri-save-line mr-2"></i>
                      <span>Guardar Datos de Facturación</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">Primero debe completar los datos fiscales antes de añadir información de facturación.</p>
            <% if can_edit_client?(@client) %>
              <%= link_to owner_client_path(@client, tab: 'fiscal'), 
                        class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700" do %>
                <i class="ri-file-list-3-line mr-2"></i>
                Completar Datos Fiscales
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  <% elsif @tab == 'activity' %>
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900">Historial de Actividad</h3>
      </div>
      <div class="p-6">
        <p class="text-gray-500 text-sm italic">El historial de actividad estará disponible próximamente.</p>
      </div>
    </div>
  <% elsif @tab == 'documents' %>
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 mt-6">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900">Documentos</h3>
      </div>
      <div class="p-6">
        <p class="text-gray-500 text-sm italic">La sección de documentos estará disponible próximamente.</p>
      </div>
    </div>
  <% end %>
  
  <!-- Elemento de prueba para verificar Stimulus -->
  <div class="fixed bottom-4 right-4 p-4 bg-white shadow-lg rounded-lg border border-gray-200 z-50"
       data-controller="test">
    <h3 class="font-medium text-gray-900 mb-2 text-sm">Prueba de Stimulus</h3>
    <p class="text-xs text-gray-600 mb-3" data-test-target="message">
      Esperando conexión con Stimulus...
    </p>
    <button type="button" 
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            data-action="click->test#showAlert">
      Probar Alerta
    </button>
  </div>
</div>
