<div>
  <div class="mb-2">
    <h1 class="text-2xl font-bold text-gray-800">Editar Cliente</h1>
  </div>
  <p class="text-sm text-gray-600 mb-2">Aquí puedes editar los datos de este cliente.</p>

  <!-- Pestañas de navegación -->
  <%= render 'shared/tabs' do |tabs| %>
    <% tabs.with_tab name: 'Información General', 
                    path: edit_owner_client_path(@client, section: 'general'), 
                    active: @section == 'general',
                    icon: 'ri-information-line' %>
    
    <% tabs.with_tab name: '<PERSON>tos Fiscales', 
                    path: edit_owner_client_path(@client, section: 'fiscal'), 
                    active: @section == 'fiscal',
                    icon: 'ri-bill-line' %>
                    
    <% tabs.with_tab name: 'Datos de Facturación', 
                    path: edit_owner_client_path(@client, section: 'billing'), 
                    active: @section == 'billing',
                    icon: 'ri-bank-card-line' %>
                    
    <% tabs.with_tab name: 'Asignación de Colaboradores', 
                    path: edit_owner_client_path(@client, section: 'members'), 
                    active: @section == 'members',
                    icon: 'ri-team-line' %>
                    
    <% if current_organization&.client_access_enabled %>
      <% tabs.with_tab name: 'Acceso a Plataforma', 
                      path: edit_owner_client_path(@client, section: 'access'), 
                      active: @section == 'access',
                      icon: 'ri-lock-unlock-line' %>
    <% end %>
  <% end %>

  <div class="bg-white shadow-md rounded-lg overflow-hidden max-w-3xl mx-auto">
    <div class="p-6">
    <%= form_with model: @client, url: owner_client_path(@client, section: @section), method: :patch, local: true, class: "space-y-6" do |form| %>
      <% if @client.errors.any? || @client_organization&.errors&.any? %>
        <div class="bg-red-50 p-4 rounded-md mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Se encontraron errores:</h3>
              <div class="mt-2 text-sm text-red-700">
                <ul role="list" class="list-disc pl-5 space-y-1">
                  <% @client.errors.full_messages.each do |error| %>
                    <li><%= error %></li>
                  <% end %>
                  <% if @client_organization&.errors&.any? %>
                    <% @client_organization.errors.full_messages.each do |error| %>
                      <li><%= error %></li>
                    <% end %>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <% if @section == 'general' %>
        <!-- Sección de Información General -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <!-- Encabezado -->
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Información Personal</h3>
            <p class="mt-1 text-sm text-gray-500">Datos básicos del contacto principal</p>
          </div>
          
          <!-- Contenido -->
          <div class="p-6">
            <div class="space-y-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <!-- Nombre -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', form: form, field: :name, 
                      label: 'Nombre', 
                      input_type: 'text',
                      placeholder: 'Ingrese el nombre',
                      required: true %>
                </div>

                <!-- Apellido -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', form: form, field: :last_name, 
                      label: 'Apellido', 
                      input_type: 'text',
                      placeholder: 'Ingrese el apellido',
                      required: true %>
                </div>

                <!-- Email -->
                <div class="sm:col-span-4">
                  <%= render 'shared/form_field', form: form, field: :email, 
                      label: 'Correo electrónico', 
                      input_type: 'email',
                      placeholder: '<EMAIL>',
                      required: true %>
                </div>

                <!-- Teléfono -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', form: form, field: :phone, 
                      label: 'Teléfono', 
                      input_type: 'tel',
                      placeholder: '+56 9 1234 5678' %>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pie de página con acciones -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <%= link_to "Cancelar", owner_client_path(@client), class: "btn btn-secondary" %>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line mr-2"></i>Guardar cambios
            </button>
          </div>
        </div>
      <% elsif @section == 'fiscal' && @client_organization.present? %>
        <!-- Sección de Datos Fiscales -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <!-- Encabezado -->
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Datos Fiscales</h3>
            <p class="mt-1 text-sm text-gray-500">Información fiscal del cliente</p>
          </div>
          
          <!-- Contenido -->
          <div class="p-6">
            <div class="space-y-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <!-- RUT con búsqueda -->
                <div class="sm:col-span-3">
                  <div class="form-field">
                    <label for="organization_rut" class="form-label">RUT</label>
                    <div class="relative mt-1">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="ri-search-line text-gray-400"></i>
                      </div>
                      <input type="search" 
                             id="organization_rut" 
                             name="organization[rut]" 
                             value="<%= @client_organization.rut %>" 
                             class="form-field-input pl-10 pr-12" 
                             placeholder="12345678-9" 
                             data-controller="rut"
                             data-action="change->rut#format"
                             data-rut-target="input">
                      <button type="button" 
                              class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                              data-action="click->rut#search"
                              data-rut-target="searchButton"
                              title="Buscar RUT">
                        <i class="ri-search-line"></i>
                      </button>
                      <button type="button" 
                              class="absolute inset-y-0 right-8 px-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                              data-action="click->rut#clear"
                              title="Limpiar búsqueda">
                        <i class="ri-close-line"></i>
                      </button>
                    </div>
                    <% if @client_organization.errors[:rut].any? %>
                      <p class="mt-1 text-sm text-red-600"><%= @client_organization.errors[:rut].first %></p>
                    <% end %>
                  </div>
                </div>

                <!-- Razón Social -->
                <div class="sm:col-span-6">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :razon_social, 
                      namespace: 'organization',
                      value: @client_organization.razon_social,
                      label: 'Razón Social', 
                      input_type: 'text',
                      required: true %>
                </div>

                <!-- Giro -->
                <div class="sm:col-span-6">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :giro, 
                      namespace: 'organization',
                      value: @client_organization.giro,
                      label: 'Giro', 
                      input_type: 'text' %>
                </div>

                <!-- Dirección -->
                <div class="sm:col-span-6">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :direccion, 
                      namespace: 'organization',
                      value: @client_organization.direccion,
                      label: 'Dirección', 
                      input_type: 'text' %>
                </div>

                <!-- Comuna y Región -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :comuna, 
                      namespace: 'organization',
                      value: @client_organization.comuna,
                      label: 'Comuna', 
                      input_type: 'text' %>
                </div>

                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :region, 
                      namespace: 'organization',
                      value: @client_organization.region,
                      label: 'Región', 
                      input_type: 'text' %>
                </div>

            <!-- Mensaje de ayuda -->
            <div class="md:col-span-2 mt-4">
              <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h2a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-blue-700">
                      Puedes buscar automáticamente los datos fiscales ingresando el RUT y haciendo clic en el botón de búsqueda.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% elsif @section == 'billing' && @client_organization.present? %>
        <!-- Sección de Datos de Facturación -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <!-- Encabezado -->
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Datos de Facturación</h3>
            <p class="mt-1 text-sm text-gray-500">Configuración de facturación del cliente</p>
          </div>
          
          <!-- Contenido -->
          <div class="p-6">
            <div class="space-y-6">
              <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <!-- Método de Pago -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :metodo_pago, 
                      namespace: 'organization',
                      value: @client_organization.metodo_pago,
                      label: 'Método de Pago', 
                      input_type: 'select',
                      options: [
                        ['Transferencia Bancaria', 'transferencia'],
                        ['WebPay', 'webpay'],
                        ['Otro', 'otro']
                      ] %>
                </div>

                <!-- Días de Pago -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :dias_pago, 
                      namespace: 'organization',
                      value: @client_organization.dias_pago || 30,
                      label: 'Días de Pago', 
                      input_type: 'number',
                      min: 1,
                      max: 90,
                      help_text: 'Número de días para el pago de facturas' %>
                </div>

                <!-- Límite de Crédito -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :limite_credito, 
                      namespace: 'organization',
                      value: @client_organization.limite_credito || 0,
                      label: 'Límite de Crédito ($)', 
                      input_type: 'number',
                      min: 0,
                      step: '0.01',
                      help_text: 'Monto máximo de crédito permitido (0 para sin límite)' %>
                </div>

                <!-- Moneda -->
                <div class="sm:col-span-3">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :moneda, 
                      namespace: 'organization',
                      value: @client_organization.moneda,
                      label: 'Moneda', 
                      input_type: 'select',
                      options: [
                        ['Pesos Chilenos (CLP)', 'CLP'],
                        ['Dólares (USD)', 'USD'],
                        ['Euros (EUR)', 'EUR']
                      ] %>
                </div>

                <!-- Exento de IVA -->
                <div class="sm:col-span-6 pt-2">
                  <%= render 'shared/form_field', 
                      form: form, 
                      field: :exento_iva, 
                      namespace: 'organization',
                      value: @client_organization.exento_iva,
                      label: 'Exento de IVA', 
                      input_type: 'checkbox',
                      help_text: 'Marcar si el cliente está exento de IVA' %>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pie de página con acciones -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <%= link_to "Cancelar", owner_client_path(@client), class: "btn btn-secondary" %>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line mr-2"></i>Guardar cambios
            </button>
          </div>
        </div>
      <% elsif @section == 'members' %>
        <!-- Sección de Asignación de Colaboradores -->
        <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-4 mb-6">
          <div class="mb-4 border-b border-gray-200 pb-2">
            <h3 class="text-md font-medium text-gray-800">Asignación de Colaboradores</h3>
            <p class="text-sm text-gray-600">Selecciona los colaboradores que pueden gestionar este cliente.</p>
          </div>

          <% if @available_members.any? %>
            <div class="space-y-4">
              <% @available_members.each do |member| %>
                <div class="flex items-center">
                  <%= check_box_tag "client[assigned_member_ids][]", 
                                  member.id, 
                                  @client.assigned_members.include?(member), 
                                  id: "member_#{member.id}",
                                  class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                  <label for="member_<%= member.id %>" class="ml-2 block text-sm text-gray-900">
                    <%= member.name %> <%= member.last_name %>
                    <span class="text-gray-500 text-xs block"><%= member.email %></span>
                  </label>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-sm text-gray-500">No hay colaboradores disponibles para asignar.</p>
          <% end %>
        </div>
      <% elsif @section == 'access' && current_organization&.client_access_enabled %>
        <!-- Sección de Acceso a Plataforma -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
          <!-- Encabezado -->
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">Acceso a Plataforma</h3>
            <p class="mt-1 text-sm text-gray-500">Gestiona el acceso del cliente a la plataforma</p>
          </div>
          
          <!-- Contenido -->
          <div class="p-6">
            <% org_user = @client.organization_users.find_by(organization: current_organization) %>
            
            <!-- Estado del Acceso -->
            <% if org_user&.access_enabled? %>
              <div class="rounded-md bg-green-50 p-4 mb-6">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <i class="ri-checkbox-circle-line text-green-400 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">Acceso Habilitado</h3>
                    <div class="mt-2 text-sm text-green-700">
                      <p>Este cliente tiene acceso a la plataforma.</p>
                      <% if org_user.invitation_sent_at %>
                        <p class="mt-1 text-xs text-green-600">
                          <i class="ri-mail-line mr-1"></i> Última invitación: <%= l(org_user.invitation_sent_at, format: :long) %>
                        </p>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% else %>
              <div class="rounded-md bg-yellow-50 p-4 mb-6">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <i class="ri-alert-line text-yellow-400 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Acceso Deshabilitado</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                      <p>Este cliente no tiene acceso a la plataforma.</p>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <!-- Opción de Invitación -->
            <div class="mt-6 pt-4 border-t border-gray-200">
              <%= render 'shared/form_field', 
                  form: form,
                  field: :send_invitation,
                  label: 'Enviar invitación',
                  input_type: 'checkbox',
                  help_text: 'Al marcar esta opción, se enviará un correo electrónico al cliente con instrucciones para acceder a la plataforma.' %>
            </div>

            <!-- Información de Acceso -->
            <div class="mt-6 bg-gray-50 p-4 rounded-md border border-gray-200">
              <h4 class="text-sm font-medium text-gray-700 mb-3">Información de Acceso</h4>
              <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                <div class="sm:col-span-1">
                  <dt class="text-xs font-medium text-gray-500">Correo Electrónico</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= @client.email %></dd>
                </div>
                <div class="sm:col-span-1">
                  <dt class="text-xs font-medium text-gray-500">Estado de la Cuenta</dt>
                  <dd class="mt-1">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= org_user&.access_enabled? ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
                      <%= org_user&.access_enabled? ? 'Activa' : 'Inactiva' %>
                    </span>
                  </dd>
                </div>
                <% if org_user&.last_sign_in_at %>
                  <div class="sm:col-span-2">
                    <dt class="text-xs font-medium text-gray-500">Último Inicio de Sesión</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <%= l(org_user.last_sign_in_at, format: :long) rescue 'Nunca' %>
                      <span class="text-gray-500 text-xs ml-2">(<%= time_ago_in_words(org_user.last_sign_in_at) %> atrás)</span>
                    </dd>
                  </div>
                <% end %>
              </dl>
            </div>
        </div>
      <% end %>

      <div class="flex flex-col sm:flex-row sm:justify-between gap-3 mt-6 pt-4 border-t border-gray-200">
        <div class="flex gap-2">
          <%= link_to owner_clients_path, class: "btn btn-secondary" do %>
            <i class="ri-arrow-left-line mr-2"></i> Volver a la lista
          <% end %>
        </div>
        <div class="flex gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line mr-2"></i>
            Guardar Cambios
          </button>
        </div>
      </div>
    <% end %>
  </div>
</div>
