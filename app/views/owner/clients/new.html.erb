<div class="bg-white shadow-md rounded-lg overflow-hidden max-w-3xl mx-auto">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Nuevo Cliente</h1>
    <p class="text-gray-600 mt-1">
      Ingresa los datos básicos del cliente
    </p>
  </div>

  <div class="p-6">
    <%= form_with url: create_basic_owner_clients_path, method: :post, class: "space-y-6", data: { turbo: false } do |form| %>
      <div class="space-y-8">
        <!-- Datos básicos de la organización -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-3">Datos de la organización</h3>
          <div class="bg-gray-50 p-6 rounded border border-gray-200 max-w-2xl mx-auto">
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div class="sm:col-span-2">
                <%= form.label :organization_name, "Nombre de la Organización", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.text_field :organization_name, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>

              <div class="sm:col-span-2">
                <%= form.label :organization_address, "Dirección", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.text_field :organization_address, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Datos básicos del cliente -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-3">Datos de contacto</h3>
          <div class="bg-gray-50 p-6 rounded border border-gray-200 max-w-2xl mx-auto">
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <!-- Datos del usuario (contacto) -->
              <div>
                <%= form.label :name, "Nombre", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.text_field :name, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>

              <div>
                <%= form.label :last_name, "Apellido", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.text_field :last_name, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>

              <div class="sm:col-span-2">
                <%= form.label :email, "Correo electrónico", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.email_field :email, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>

              <div>
                <%= form.label :phone, "Teléfono", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.telephone_field :phone, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
                </div>
              </div>

              <div>
                <%= form.label :password, "Contraseña", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.password_field :password, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>
              
              <div>
                <%= form.label :password_confirmation, "Confirmar Contraseña", class: "block text-sm font-medium text-gray-700" %>
                <div class="mt-1">
                  <%= form.password_field :password_confirmation, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md", required: true %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Campos ocultos para datos fiscales (se llenarán con JS) -->
        <%= form.hidden_field :fiscal_rut, id: "fiscalRut" %>
        <%= form.hidden_field :fiscal_razon_social, id: "fiscalRazonSocial" %>
        <%= form.hidden_field :fiscal_giro, id: "fiscalGiro" %>
        <%= form.hidden_field :fiscal_tipo_entidad, id: "fiscalTipoEntidad" %>
        <%= form.hidden_field :fiscal_direccion, id: "fiscalDireccion" %>
        <%= form.hidden_field :fiscal_telefono, id: "fiscalTelefono" %>
        <%= form.hidden_field :fiscal_email, id: "fiscalEmail" %>
      </div>

      <div class="pt-5">
        <div class="flex justify-end">
          <%= link_to "Cancelar", owner_clients_path, class: "btn btn-secondary" %>
          <button type="submit" class="ml-3 btn btn-primary">
            Guardar Cliente
          </button>
        </div>
      </div>
    <% end %>
  </div>
</div>
