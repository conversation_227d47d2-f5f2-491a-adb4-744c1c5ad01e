<%- headers = [
  'ID',
  'Nombre',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'RUT',
  'Teléfono',
  'Dirección',
  'Estado',
  'Fecha de Creación',
  'Última Actualización'
] -%>
<%= CSV.generate_line(headers, col_sep: ";").html_safe -%>
<%- @clients.each do |client| -%>
<%- row = [
  client.id,
  client.first_name,
  client.last_name,
  client.email,
  client.rut,
  client.phone,
  client.address,
  client.active? ? 'Activo' : 'Inactivo',
  I18n.l(client.created_at, format: :long),
  I18n.l(client.updated_at, format: :long)
] -%>
<%= CSV.generate_line(row, col_sep: ";").html_safe -%>
<%- end -%>
