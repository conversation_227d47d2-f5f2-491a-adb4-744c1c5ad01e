<% content_for :full_width, true %>

<%= render 'shared/clients/section_header', 
           title: 'Gestionar Clientes',
           actions: link_to("Nuevo Cliente", new_owner_client_path, class: "btn btn-primary") %>

<%
  # Definir las columnas para el DataTable
  columns = [
    {
      label: 'Email',
      field: 'email',
      sortable: true,
      width: '20%'
    },
    {
      label: 'Nombre',
      field: ->(client) { [client.name, client.last_name].compact.join(' ') },
      sortable: true,
      width: '20%'
    },
    {
      label: 'RUT',
      field: ->(client) { client.client_organizations.first&.rut || "-" },
      filterable: true,
      sortable: true,
      width: '15%',
      filter_options: @clients.flat_map { |c| c.client_organizations.pluck(:rut) }.compact.uniq
    },
    {
      label: 'Dirección',
      field: ->(client) { client.client_organizations.first&.direccion || "-" },
      sortable: true,
      width: '15%'
    },
    {
      label: 'Teléfono',
      field: ->(client) { client.client_organizations&.first&.telefono.presence || "-" },
      sortable: true,
      width: '10%',
      filterable: true
    },
    {
      label: 'Estado',
      field: ->(client) {
        org_user = client.organization_users.find_by(organization: @organization)
        if org_user&.access_enabled?
          '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Activo</span>'.html_safe
        else
          '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactivo</span>'.html_safe
        end
      },
      sortable: true,
      width: '10%',
      filterable: true,
      filter_options: [
        { value: 'active', label: 'Activo' },
        { value: 'inactive', label: 'Inactivo' }
      ]
    },
    {
      label: 'Acciones',
      field: ->(client) {
        render partial: 'shared/actions_dropdown', locals: {
          edit_path: edit_owner_client_path(client),
          show_path: owner_client_path(client),
          delete_path: confirm_destroy_owner_client_path(client),
          toggle_access_path: toggle_access_owner_client_path(client),
          toggle_access_text: client.organization_users.find_by(organization: @organization)&.access_enabled? ? 'Desactivar' : 'Activar',
          show_actions: true,
          show_edit: true,
          show_delete: true,
          show_toggle_access: true
        }
      },
      sortable: false,
      width: '10%',
      class: 'sticky right-0 bg-white'
    }
  ]
%>

<%= render partial: 'shared/datatable', locals: {
  columns: columns,
  collection: @clients,
  search_enabled: true,
  filters_enabled: true,
  sticky_actions: true,
  show_actions: true,
  show_edit: true,
  show_delete: true,
  show_toggle_access: true,
  new_path: new_owner_client_path,
  export_path: export_owner_clients_path(format: :csv)
} %>
