<%# Partial para mostrar la lista de clientes en resultados de búsqueda %>
<%# Se espera una variable local 'clients' con la colección de usuarios %>

<% if clients.any? %>
  <div class="overflow-x-auto bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Nombre
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            RUT
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Email
          </th>
          <th scope="col" class="relative px-6 py-3">
            <span class="sr-only">Acciones</span>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% clients.each do |client| %>
          <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 font-medium">
                  <%= client.first_name[0].upcase %><%= client.last_name[0].upcase if client.last_name.present? %>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">
                    <%= link_to "#{client.first_name} #{client.last_name}", 
                                owner_client_path(client), 
                                class: 'text-indigo-600 hover:text-indigo-900 hover:underline' %>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= format_rut(client.rut) if client.rut.present? %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= client.email %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <%= link_to 'Ver', owner_client_path(client), 
                          class: 'text-indigo-600 hover:text-indigo-900 mr-4' %>
              <%= link_to 'Editar', edit_owner_client_path(client), 
                          class: 'text-indigo-600 hover:text-indigo-900' %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <%# Paginación %>
  <% if clients.respond_to?(:total_pages) %>
    <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
      <div class="flex-1 flex justify-between sm:hidden">
        <%= link_to_prev_page clients, 'Anterior', 
            class: 'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50' %>
        <%= link_to_next_page clients, 'Siguiente', 
            class: 'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50' %>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Mostrando <span class="font-medium"><%= clients.offset_value + 1 %></span>
            a <span class="font-medium"><%= [clients.offset_value + clients.count, clients.total_count].min %></span>
            de <span class="font-medium"><%= clients.total_count %></span> resultados
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <%= link_to_prev_page clients, class: 'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50' do %>
              <span class="sr-only">Anterior</span>
              <!-- Heroicon name: solid/chevron-left -->
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
            
            <%# Mostrar números de página %>
            <% window = 2 %>
            <% if clients.total_pages > 1 %>
              <% if clients.current_page > window + 1 %>
                <% (1..1).each do |page| %>
                  <%= link_to page, url_for(page: page), class: 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50' %>
                <% end %>
                <% if clients.current_page > window + 2 %>
                  <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                <% end %>
              <% end %>
              
              <% ([1, clients.current_page - window].max..[clients.current_page + window, clients.total_pages].min).each do |page| %>
                <% if page == clients.current_page %>
                  <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                    <%= page %>
                  </span>
                <% else %>
                  <%= link_to page, url_for(page: page), class: 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50' %>
                <% end %>
              <% end %>
              
              <% if clients.current_page < clients.total_pages - window - 1 %>
                <% if clients.current_page < clients.total_pages - window - 1 %>
                  <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                <% end %>
                <%= link_to clients.total_pages, url_for(page: clients.total_pages), class: 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50' %>
              <% end %>
            <% end %n            
            <%= link_to_next_page clients, class: 'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50' do %>
              <span class="sr-only">Siguiente</span>
              <!-- Heroicon name: solid/chevron-right -->
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
          </nav>
        </div>
      </div>
    </div>
  <% end %>
<% else %>
  <div class="text-center py-12 bg-white rounded-lg shadow">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No se encontraron resultados</h3>
    <p class="mt-1 text-sm text-gray-500">
      No hay clientes que coincidan con tu búsqueda.
    </p>
    <div class="mt-6">
      <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <!-- Heroicon name: solid/plus -->
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Nuevo Cliente
      </button>
    </div>
  </div>
<% end %>
