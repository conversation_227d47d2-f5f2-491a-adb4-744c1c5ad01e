<div class="bg-white shadow-md rounded-lg overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
    <h1 class="text-2xl font-bold text-gray-800">Usuarios</h1>
    <%= link_to new_owner_user_path, class: "btn btn-primary inline-flex items-center" do %>
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Nuevo Usuario
    <% end %>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>

          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rol</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organización</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @users.each do |user| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= user.email %></td>

            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= user.name %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize"><%= user.role %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <% if user.organizations.any? %>
                Organización
              <% else %>
                Usuario Individual
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <% if user.admin %>
                <%= user.admin.email %>
              <% elsif user.assigned_to %>
                <%= user.assigned_to.email %> (Asignado)
              <% else %>
                -
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex flex-wrap gap-2 justify-end">
                <%= link_to "Ver", user_path(user), class: "btn btn-sm btn-secondary" %>
                <%= link_to "Editar", edit_user_path(user), class: "btn btn-sm btn-secondary" %>
                <% if user.member? %>
                  <%= link_to "Permisos", user_permissions_path(user), class: "btn btn-sm btn-secondary" %>
                <% end %>
                <%= button_to "Eliminar", user_path(user), 
                    method: :delete, 
                    class: "btn btn-sm btn-danger", 
                    form: { 
                      class: "inline-block",
                      data: { turbo_confirm: "¿Estás seguro de que deseas eliminar este usuario?" } 
                    } %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <% if @users.empty? %>
    <div class="px-6 py-4 text-center text-gray-500">
      No hay usuarios disponibles.
    </div>
  <% end %>

  <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
    <%= link_to "Volver al inicio", home_path, class: "btn btn-secondary" %>
  </div>
</div>
