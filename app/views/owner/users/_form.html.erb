<%= form_with(model: user, class: "space-y-6") do |form| %>
  <% if user.errors.any? %>
    <div class="bg-red-50 p-4 rounded-md mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Se encontraron <%= pluralize(user.errors.count, "error") %></h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
    <div class="sm:col-span-3">
      <label for="user_name" class="form-field-label">Nombre</label>
      <%= form.text_field :name, class: "form-field-input w-full" %>
    </div>

    <div class="sm:col-span-3">
      <label for="user_last_name" class="form-field-label">Apellido</label>
      <%= form.text_field :last_name, class: "form-field-input w-full" %>
    </div>

    <div class="sm:col-span-3">
      <label for="user_email" class="form-field-label">Email</label>
      <%= form.email_field :email, class: "form-field-input w-full" %>
    </div>

    <% if current_user.superadmin? || current_user.support? %>
      <div class="sm:col-span-3">
        <label for="user_role" class="form-field-label">Rol</label>
        <%= form.select :role, User.roles.keys.map { |role| [role.capitalize, role] }, {}, class: "form-field-input w-full" %>
      </div>
    <% end %>

    <% if current_user.superadmin? || current_user.support? %>
      <div class="sm:col-span-3">
        <label for="user_admin_id" class="form-field-label">Propietario (para colaboradores)</label>
        <%= form.collection_select :admin_id, User.where(role: :owner), :id, :email, { include_blank: "Ninguno" }, class: "form-field-input w-full" %>
      </div>
    <% end %>

    <% if current_user.superadmin? || current_user.support? || current_user.owner? || (current_user.collaborator? && current_user.has_permission?('assign_members')) %>
      <div class="sm:col-span-3">
        <label for="user_assigned_to_id" class="form-field-label">Asignado a (para clientes)</label>
        <% if current_user.owner? %>
          <%= form.collection_select :assigned_to_id, User.where(admin_id: current_user.id), :id, :email, { include_blank: "Ninguno" }, class: "form-field-input w-full" %>
        <% elsif current_user.collaborator? && current_user.has_permission?('assign_members') %>
          <%= form.hidden_field :assigned_to_id, value: current_user.id %>
          <div class="shadow-sm block w-full sm:text-sm border-gray-300 rounded-md bg-gray-100 px-3 py-2">
            <%= current_user.email %> (Tú)
          </div>
        <% else %>
          <%= form.collection_select :assigned_to_id, User.where(role: :collaborator), :id, :email, { include_blank: "Ninguno" }, class: "form-field-input w-full" %>
          <% end %>
        </div>
      </div>
    <% end %>

    <div class="sm:col-span-3">
      <%= form.label :password, "Contraseña", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= form.password_field :password, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
        <% if user.persisted? %>
          <p class="mt-2 text-sm text-gray-500">Dejar en blanco si no deseas cambiarla</p>
        <% end %>
      </div>
    </div>

    <div class="sm:col-span-3">
      <%= form.label :password_confirmation, "Confirmar Contraseña", class: "block text-sm font-medium text-gray-700" %>
      <div class="mt-1">
        <%= form.password_field :password_confirmation, class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
      </div>
    </div>
  </div>

  <div class="pt-5">
    <div class="flex justify-end">
      <% if user.persisted? %>
        <%= link_to "Cancelar", user_path(user), class: "btn btn-secondary" %>
      <% else %>
        <%= link_to "Cancelar", users_path, class: "btn btn-secondary" %>
      <% end %>
      <%= form.submit user.persisted? ? "Guardar" : "Crear", class: "ml-3 btn btn-primary" %>
    </div>
  </div>
<% end %>
