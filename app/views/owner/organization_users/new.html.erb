<div class="bg-white shadow-md rounded-lg overflow-hidden">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Agregar Colaborador a <%= @organization.name %></h1>
  </div>

  <div class="p-6">
    <div class="space-y-6">
      <%= form_with(model: [@organization, @organization_user], url: owner_organization_collaborators_path(@organization), local: true) do |form| %>
        <%= hidden_field_tag :create_new_user, "true" %>

        <% if @user.errors.any? %>
          <div class="bg-red-50 p-4 rounded mb-6 border border-red-200">
            <h3 class="text-red-800 font-medium">Se encontraron <%= @user.errors.count %> errores:</h3>
            <ul class="list-disc pl-5 text-red-700">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="bg-gray-50 p-6 rounded border border-gray-200 space-y-6">
          <h3 class="text-lg font-medium text-gray-900">Datos del nuevo usuario</h3>

          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
            <div>
              <%= label_tag "user[name]", "Nombre", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= text_field_tag "user[name]", @user.name, class: "form-field-input" %>
            </div>

            <div>
              <%= label_tag "user[last_name]", "Apellido", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= text_field_tag "user[last_name]", @user.last_name, class: "form-field-input" %>
            </div>

            <div>
              <%= label_tag "user[email]", "Email", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= text_field_tag "user[email]", @user.email, class: "form-field-input", required: true %>
            </div>

            <div>
              <%= label_tag "user[password]", "Contraseña", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= password_field_tag "user[password]", nil, class: "form-field-input", required: true %>
            </div>

            <div>
              <%= label_tag "user[password_confirmation]", "Confirmar Contraseña", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= password_field_tag "user[password_confirmation]", nil, class: "form-field-input", required: true %>
            </div>
          </div>
        </div>

        <div class="flex justify-between items-center mt-8 pt-4 border-t border-gray-200">
          <%= link_to edit_owner_organization_path(@organization, section: 'collaborators'), 
              class: "btn btn-secondary" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Cancelar
          <% end %>
          
          <button type="submit" class="btn btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Guardar Colaborador
          </button>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Eliminado botón redundante al pie de página, ya que existe un botón de Cancelar en el formulario -->
</div>


