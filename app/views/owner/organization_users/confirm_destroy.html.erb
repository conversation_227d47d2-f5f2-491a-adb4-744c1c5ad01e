<div class="bg-white shadow-md rounded-lg overflow-hidden w-full">
  <div class="px-6 py-4 border-b border-gray-200">
    <h1 class="text-2xl font-bold text-gray-800">Confirmar Eliminación de Colaborador</h1>
  </div>

  <div class="p-6">
    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-yellow-700">
            <strong>Atención:</strong> Estás a punto de eliminar un colaborador de la organización. Esta acción no se puede deshacer.
          </p>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <h2 class="text-lg font-medium text-gray-900">Detalles del colaborador</h2>
      <div class="mt-4 border border-gray-200 rounded-md p-4 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">Nombre</p>
            <p class="mt-1 text-sm text-gray-900"><%= @organization_user.user.name %></p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Apellido</p>
            <p class="mt-1 text-sm text-gray-900"><%= @organization_user.user.last_name %></p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Correo electrónico</p>
            <p class="mt-1 text-sm text-gray-900"><%= @organization_user.user.email %></p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Rol en la organización</p>
            <p class="mt-1 text-sm text-gray-900"><%= t("organization_user.roles.#{@organization_user.role}") %></p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">Organización</p>
            <p class="mt-1 text-sm text-gray-900"><%= @organization.name %></p>
          </div>
        </div>
      </div>
    </div>
    
  </div>

  <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-wrap gap-2">
    <%= link_to edit_owner_organization_path(@organization, section: 'collaborators'), class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
      <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
      </svg>
      Volver
    <% end %>

    <%= button_to owner_organization_collaborator_path(@organization, @organization_user), 
                method: :delete,
                class: "btn btn-danger",
                data: { turbo_confirm: '¿Estás completamente seguro?' } do %>
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>
      Confirmar Eliminación
    <% end %>
  </div>
</div>
