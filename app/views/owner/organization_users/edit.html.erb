<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-3xl mx-auto">
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
      <div class="relative">
        <div class="px-6 py-4 bg-gradient-to-r from-indigo-600 to-indigo-400">
          <div class="flex items-center justify-between">
            <h1 class="text-xl font-semibold text-white">Editar Colaborador</h1>
          </div>
        </div>
      </div>

      <%= form_with(model: [@organization, @organization_user], url: owner_organization_collaborator_path(@organization, @organization_user), method: :patch, local: true, class: "w-full") do |form| %>
        <div class="p-6">
          <% if @organization_user.errors.any? || @user.errors.any? %>
            <div class="bg-red-50 p-4 rounded mb-6 border border-red-200">
              <h3 class="text-red-800 font-medium">Se encontraron errores:</h3>
              <ul class="list-disc pl-5 text-red-700">
                <% @organization_user.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
                <% @user.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          <% end %>

          <div class="space-y-6">
            <h2 class="text-lg font-medium text-gray-900">Datos del Colaborador</h2>
            
            <%= fields_for :user, @user do |user_form| %>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <%= user_form.label :name, "Nombre", class: "block text-sm font-medium text-gray-700 mb-1" %>
                  <%= user_form.text_field :name, class: "form-input block w-full" %>
                </div>

                <div>
                  <%= user_form.label :last_name, "Apellido", class: "block text-sm font-medium text-gray-700 mb-1" %>
                  <%= user_form.text_field :last_name, class: "form-input block w-full" %>
                </div>

                <div class="md:col-span-2">
                  <%= user_form.label :email, "Correo Electrónico", class: "block text-sm font-medium text-gray-700 mb-1" %>
                  <%= user_form.email_field :email, class: "form-input block w-full", required: true %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center gap-4">
          <%= link_to edit_owner_organization_path(@organization, section: 'collaborators'), class: "btn btn-secondary" do %>
            <i class="ri-arrow-left-line mr-2"></i>
            Volver
          <% end %>
          
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line mr-2"></i>
            Guardar Cambios
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>


