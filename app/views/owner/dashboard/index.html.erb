<div class="py-8 px-4 sm:px-6 lg:px-8">
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado del Dashboard -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-1 text-sm text-gray-500">
          Bienvenido a <%= @organization.name %>. Aquí tienes un resumen de tu organización.
        </p>
      </div>
      <div class="mt-4 md:mt-0 flex space-x-3">
        <a href="<%= edit_owner_organization_path(@organization, section: 'general') %>" class="btn btn-primary">
          <i class="ri-settings-line mr-1"></i> Configuración
        </a>
      </div>
    </div>

    <!-- Tarjetas de resumen -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <!-- Tarjeta de clientes -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-indigo-100 rounded-md p-3">
              <i class="ri-user-line text-indigo-600 text-xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Clientes</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900"><%= @clients_count %></div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="<%= owner_clients_path %>" class="font-medium text-indigo-600 hover:text-indigo-900">Ver todos</a>
          </div>
        </div>
      </div>

      <!-- Tarjeta de colaboradores -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
              <i class="ri-team-line text-green-600 text-xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Colaboradores</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900"><%= @collaborators_count %></div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="<%= owner_organization_collaborators_path(@organization) %>" class="font-medium text-green-600 hover:text-green-900">Ver todos</a>
          </div>
        </div>
      </div>

      <!-- Tarjeta de estado -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
              <i class="ri-building-line text-blue-600 text-xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Estado</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900"><%= @organization.status.to_s.capitalize %></div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="<%= edit_owner_organization_path(@organization, section: 'general') %>" class="font-medium text-blue-600 hover:text-blue-900">Modificar</a>
          </div>
        </div>
      </div>

      <!-- Tarjeta de acceso a clientes -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
              <i class="ri-lock-line text-purple-600 text-xl"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Acceso a clientes</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900"><%= @organization.client_access_enabled? ? 'Habilitado' : 'Deshabilitado' %></div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <a href="<%= edit_owner_organization_path(@organization, section: 'access') %>" class="font-medium text-purple-600 hover:text-purple-900">Gestionar</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Clientes recientes y colaboradores recientes -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Clientes recientes -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-5 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            Clientes recientes
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <% if @recent_clients.any? %>
            <% @recent_clients.each do |client| %>
              <div class="px-5 py-4 flex items-center">
                <div class="min-w-0 flex-1">
                  <div class="text-sm font-medium text-gray-900 truncate"><%= client.name %> <%= client.last_name %></div>
                  <div class="text-sm text-gray-500">
                    <span class="mr-2"><%= client.email %></span>
                    <span>Creado: <%= client.created_at.strftime('%d/%m/%Y') %></span>
                  </div>
                </div>
                <a href="<%= owner_client_path(client) %>" class="ml-4 flex-shrink-0">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <i class="ri-eye-line mr-1"></i> Ver
                  </span>
                </a>
              </div>
            <% end %>
          <% else %>
            <div class="px-5 py-8 text-center">
              <p class="text-sm text-gray-500">No hay clientes registrados aún.</p>
              <div class="mt-4">
                <a href="<%= new_owner_client_path %>" class="btn btn-primary">
                  <i class="ri-add-line mr-1"></i> Añadir cliente
                </a>
              </div>
            </div>
          <% end %>
        </div>
        <% if @recent_clients.any? %>
          <div class="bg-gray-50 px-5 py-3 text-right">
            <a href="<%= owner_clients_path %>" class="text-sm font-medium text-indigo-600 hover:text-indigo-900">
              Ver todos <i class="ri-arrow-right-line ml-1"></i>
            </a>
          </div>
        <% end %>
      </div>

      <!-- Colaboradores recientes -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-5 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            Colaboradores recientes
          </h3>
        </div>
        <div class="divide-y divide-gray-200">
          <% if @recent_collaborators.any? %>
            <% @recent_collaborators.each do |org_user| %>
              <div class="px-5 py-4 flex items-center">
                <div class="min-w-0 flex-1">
                  <div class="text-sm font-medium text-gray-900 truncate"><%= org_user.user.name %></div>
                  <div class="text-sm text-gray-500">
                    <span><%= org_user.user.email %></span>
                    <span class="ml-2">Rol: <%= org_user.role.capitalize %></span>
                  </div>
                </div>
                <a href="<%= edit_owner_organization_collaborator_path(@organization, org_user) %>" class="ml-4 flex-shrink-0">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <i class="ri-user-settings-line mr-1"></i> Editar
                  </span>
                </a>
              </div>
            <% end %>
          <% else %>
            <div class="px-5 py-8 text-center">
              <p class="text-sm text-gray-500">No hay colaboradores registrados aún.</p>
              <div class="mt-4">
                <a href="<%= new_owner_organization_collaborator_path(@organization) %>" class="btn btn-primary">
                  <i class="ri-team-line mr-1"></i> Añadir colaborador
                </a>
              </div>
            </div>
          <% end %>
        </div>
        <% if @recent_collaborators.any? %>
          <div class="bg-gray-50 px-5 py-3 text-right">
            <a href="<%= owner_organization_collaborators_path(@organization) %>" class="text-sm font-medium text-indigo-600 hover:text-indigo-900">
              Ver todos <i class="ri-arrow-right-line ml-1"></i>
            </a>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Accesos rápidos -->
    <div class="mt-8 bg-white shadow rounded-lg overflow-hidden">
      <div class="px-5 py-4 border-b border-gray-200 bg-gray-50">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Acciones rápidas
        </h3>
      </div>
      <div class="p-5">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <a href="<%= new_owner_client_path %>" class="btn btn-secondary flex flex-col items-center py-4 px-2">
            <i class="ri-user-add-line text-2xl mb-2"></i>
            <span>Nuevo cliente</span>
          </a>
          
          <a href="<%= new_owner_organization_collaborator_path(@organization) %>" class="btn btn-secondary flex flex-col items-center py-4 px-2">
            <i class="ri-user-add-line text-2xl mb-2"></i>
            <span>Nuevo colaborador</span>
          </a>
          
          <a href="<%= edit_owner_organization_path(@organization) %>" class="btn btn-secondary flex flex-col items-center py-4 px-2">
            <i class="ri-settings-4-line text-2xl mb-2"></i>
            <span>Configuración</span>
          </a>
          
          <a href="<%= owner_profile_path %>" class="btn btn-secondary flex flex-col items-center py-4 px-2">
            <i class="ri-user-settings-line text-2xl mb-2"></i>
            <span>Mi perfil</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
