# Modelo que representa la asignación de clientes a colaboradores
# Permite que un cliente pueda ser asignado a múltiples colaboradores
class ClientAssignment < ApplicationRecord
  # Relaciones
  belongs_to :client, class_name: "User"
  belongs_to :collaborator, class_name: "User"

  # Validaciones
  validates :client_id, presence: true
  validates :collaborator_id, presence: true
  validates :client_id, uniqueness: { scope: :collaborator_id, message: "ya está asignado a este colaborador" }

  # Validar que el cliente tenga rol de cliente
  validate :validate_client_role

  # Validar que el colaborador tenga rol de colaborador
  validate :validate_collaborator_role

  private

  # Validar que el usuario asignado como cliente tenga el rol de cliente
  def validate_client_role
    if client && !client.client?
      errors.add(:client, "debe tener el rol de cliente")
    end
  end

  # Validar que el usuario asignado como colaborador tenga el rol de colaborador
  def validate_collaborator_role
    if collaborator && !collaborator.collaborator?
      errors.add(:collaborator, "debe tener el rol de colaborador")
    end
  end
end
