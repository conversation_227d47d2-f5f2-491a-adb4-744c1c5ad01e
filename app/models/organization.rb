# frozen_string_literal: true

# Modelo que representa una organización en el sistema
# Contiene información sobre la organización, sus miembros y datos de facturación
class Organization < ApplicationRecord
  include SoftDeletable

  # Constantes para el estado de la organización
  # Evitamos usar enum para prevenir conflictos con SoftDeletable
  STATUS_ACTIVE = 0
  STATUS_INACTIVE = 1
  STATUS_DELETED = 2

  # Establecer valor por defecto para status
  attribute :status, :integer, default: STATUS_ACTIVE
  
  # Campos para datos fiscales
  attribute :nombre_legal, :string
  # Alias para compatibilidad con el código existente
  alias_attribute :razon_social, :nombre_legal
  
  # Setter personalizado para razon_social
  def razon_social=(value)
    self.nombre_legal = value
  end
  
  attribute :nombre_fantasia, :string
  attribute :tipo_entidad, :string
  attribute :direccion, :string
  attribute :telefono, :string
  attribute :actividades, :json

  # Scopes adicionales para filtrar por estado
  scope :active_only, -> { where(status: STATUS_ACTIVE) }
  scope :inactive_only, -> { where(status: STATUS_INACTIVE) }
  scope :deleted_only, -> { where(status: STATUS_DELETED) }
  scope :not_deleted, -> { where.not(status: STATUS_DELETED) }

  # Relaciones
  has_many :organization_users, dependent: :destroy
  has_many :users, through: :organization_users
  belongs_to :deleted_by, class_name: 'User', optional: true

  # Serialización para Rails 8 (ya definido arriba)

  # Validaciones
  validates :rut, uniqueness: true, if: -> { rut.present? }
  validates :name, presence: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP, message: "debe ser un email válido" },
                    allow_blank: true
  validates :email_facturacion, format: { with: URI::MailTo::EMAIL_REGEXP, message: "debe ser un email válido" },
                                allow_blank: true
  # El campo actividades es JSON y se maneja en la serialización

  # Callbacks
  validate :validate_uruguayan_rut, if: -> { rut.present? }

  # Scopes para acceso de clientes
  scope :with_client_access_enabled, -> { where(client_access_enabled: true) }
  scope :with_client_access_disabled, -> { where(client_access_enabled: false) }

  # Verifica si la organización es de tipo cliente
  def client_organization?
    # Verificar si alguno de los usuarios asociados es cliente
    organization_users.exists?(role: 2)
  end

  # Valida el formato del RUT uruguayo
  def validate_uruguayan_rut
    return true unless rut.present?
    
    # Normalizar el RUT primero (eliminar caracteres no numéricos)
    normalized_rut = RutValidatorService.normalize(rut)
    
    # Asignar el RUT normalizado
    self.rut = normalized_rut
    
    # Validar con el servicio
    valid = RutValidatorService.valid?(normalized_rut, strict: true)
    
    unless valid
      # Si no es válido, obtener y añadir el error específico
      if normalized_rut.length != 12
        errors.add(:rut, RutValidatorService::VALIDATION_ERRORS[:length])
      elsif !(1..22).include?(normalized_rut[0..1].to_i)
        errors.add(:rut, RutValidatorService::VALIDATION_ERRORS[:first_digits])
      elsif normalized_rut[2..7] == "000000"
        errors.add(:rut, RutValidatorService::VALIDATION_ERRORS[:zero_sequence])
      elsif normalized_rut[8..9] != "00"
        errors.add(:rut, RutValidatorService::VALIDATION_ERRORS[:middle_zeros])
      else
        # Si no podemos determinar el error específico, usar un mensaje genérico
        errors.add(:rut, "no es válido")
      end
      return false
    end
    
    true
  end

  # Métodos para obtener usuarios por rol en la organización
  def owners
    users.joins(:organization_users).where(organization_users: { role: 0 })
  end

  def collaborators
    users.joins(:organization_users).where(organization_users: { role: 1 })
  end

  # Métodos legacy para compatibilidad (evitar usar estos métodos en código nuevo)
  alias_method :admins, :owners
  alias_method :members, :collaborators # Deprecated: Usar collaborators en su lugar

  # Obtener todos los clientes de la organización sin duplicados
  # @return [ActiveRecord::Relation] Colección de usuarios con rol cliente
  def clients
    users.joins(:organization_users)
         .where(organization_users: { role: 2, organization_id: id })
         .distinct
  end

  # Método para agregar un usuario a la organización con un rol específico
  # @param user [User] El usuario a agregar
  # @param role [Integer, String] El rol que tendrá el usuario (0=admin, 1=collaborator, 2=client)
  # @return [OrganizationUser] La relación creada o actualizada
  def add_user(user, role = 1)
    # Si es un cliente y la organización tiene acceso de clientes habilitado, también habilitar su acceso
    access_enabled = role.to_s == 'client' && client_access_enabled

    # Buscar si ya existe una relación
    org_user = organization_users.find_by(user: user)

    if org_user
      # Si ya existe, actualizar el rol y el acceso
      org_user.update(role: role, access_enabled: access_enabled)
      org_user
    else
      # Si no existe, crear una nueva relación
      organization_users.create(user: user, role: role, access_enabled: access_enabled)
    end
  end

  # Método para eliminar un usuario de la organización
  def remove_user(user)
    organization_users.find_by(user: user)&.destroy
  end

  # Obtener clientes con acceso habilitado
  def clients_with_access
    users.joins(:organization_users)
         .where(organization_users: { role: 2, access_enabled: true })
  end

  # Obtener clientes sin acceso habilitado
  def clients_without_access
    users.joins(:organization_users)
         .where(organization_users: { role: 2, access_enabled: false })
  end

  # Habilitar acceso de clientes a nivel de organización
  def enable_client_access!
    update(client_access_enabled: true)
  end

  # Deshabilitar acceso de clientes a nivel de organización
  def disable_client_access!
    update(client_access_enabled: false)
  end

  # Métodos para gestionar el estado de la organización

  # Activa la organización (la hace visible para todos los usuarios)
  # @return [Boolean] true si se activó correctamente
  def activate!
    update(status: STATUS_ACTIVE)
  end

  # Desactiva la organización (la hace visible solo para administradores)
  # @return [Boolean] true si se desactivó correctamente
  def deactivate!
    update(status: STATUS_INACTIVE)
  end

  # Métodos para verificar el estado de la organización
  def active?
    status == STATUS_ACTIVE
  end

  def inactive?
    status == STATUS_INACTIVE
  end

  def deleted?
    status == STATUS_DELETED
  end
end
