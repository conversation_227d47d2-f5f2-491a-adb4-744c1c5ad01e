# frozen_string_literal: true

# Modelo que representa un usuario en el sistema
# Define roles, permisos, relaciones con organizaciones y métodos
# para la gestión de usuarios y sus capacidades en el sistema
class User < ApplicationRecord
  # Constantes para configuración de seguridad
  PASSWORD_MIN_LENGTH = 6
  PASSWORD_REGEX = /\A(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}\z/

  # Sobrescribimos has_secure_password para permitir clientes sin contraseña
  # cuando no tienen acceso a la plataforma
  has_secure_password validations: false

  # Normalizar email para evitar duplicados con diferentes formatos
  normalizes :email, with: ->(email) { email&.strip&.downcase }

  # Validación personalizada de contraseña
  validate :password_validation

  # No longer needed after organization migration
  # attr_accessor :is_organization

  # Enum para el rol de usuario
  # superadmin: 0 - Administrador del sistema
  # support: 1 - Soporte técnico
  # owner: 2 - Propietario/Administrador de organización
  # collaborator: 3 - <PERSON><PERSON><PERSON>/Miembro de la organización
  # client: 4 - C<PERSON>e
  enum :role, { superadmin: 0, support: 1, owner: 2, collaborator: 3, client: 4 }

  # Relaciones con organizaciones
  has_many :organization_users, dependent: :destroy
  has_many :organizations, through: :organization_users

  # Relaciones entre usuarios (legacy, se mantienen para compatibilidad)
  # Un owner puede tener muchos colaboradores en su organización
  has_many :team_members, class_name: "User", foreign_key: "admin_id", dependent: :nullify

  # Un collaborator pertenece a un owner (su organización)
  belongs_to :admin, class_name: "User", optional: true

  # Un collaborator puede tener muchos clientes asignados (relación legacy 1:N)
  has_many :assigned_clients, class_name: "User", foreign_key: "assigned_to_id", dependent: :nullify

  # Un client pertenece a un collaborator que lo gestiona (relación legacy 1:N)
  belongs_to :assigned_to, class_name: "User", optional: true

  # Relaciones para asignación múltiple de clientes a colaboradores
  # Para clientes: obtener todas las asignaciones y los colaboradores asignados
  has_many :client_assignments, foreign_key: "client_id", dependent: :destroy
  has_many :assigned_collaborators, through: :client_assignments, source: :collaborator

  # Para colaboradores: obtener todas las asignaciones y los clientes asignados
  has_many :collaborator_assignments, class_name: "ClientAssignment", foreign_key: "collaborator_id", dependent: :destroy
  has_many :assigned_clients_multiple, through: :collaborator_assignments, source: :client

  # Relación con permisos
  has_many :permissions, dependent: :destroy

  # Relación con excepciones de permisos
  has_many :permission_overrides, class_name: 'UserPermissionOverride', dependent: :destroy

  # Validaciones
  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :role, presence: true

  # Validaciones condicionales según el rol (legacy, se mantienen para compatibilidad)
  validates :admin_id, presence: true, if: :collaborator?
  validates :assigned_to_id, presence: true, if: :client?

  # Métodos para trabajar con organizaciones

  # Obtener todas las organizaciones donde el usuario es administrador
  def administered_organizations
    organizations.joins(:organization_users).where(organization_users: { role: 0 })
  end

  # Obtener todas las organizaciones donde el usuario es colaborador
  def collaborator_organizations
    organizations.joins(:organization_users).where(organization_users: { role: 1 })
  end
  
  # Alias para compatibilidad con código existente (deprecated)
  alias_method :member_organizations, :collaborator_organizations

  # Obtener todas las organizaciones donde el usuario es cliente
  def client_organizations
    organizations.joins(:organization_users)
               .where(organization_users: { role: 2 })
               .where.not(rut: '217090160018')
  end

  # Obtener todas las organizaciones donde el usuario es cliente (incluyendo todas)
  def all_client_organizations
    organizations.joins(:organization_users).where(organization_users: { role: 2 })
  end

  # Verificar si el usuario es administrador de una organización específica
  def admin_of?(organization)
    organization_users.exists?(organization: organization, role: 0)
  end

  # Verificar si el usuario es colaborador de una organización específica
  def collaborator_of?(organization)
    organization_users.exists?(organization: organization, role: 1)
  end
  
  # Alias para compatibilidad con código existente (deprecated)
  alias_method :member_of?, :collaborator_of?

  # Verificar si el usuario es cliente de una organización específica
  def client_of?(organization)
    organization_users.exists?(organization: organization, role: 2)
  end

  # Verificar si el usuario tiene acceso a la plataforma como cliente de una organización
  # @param organization [Organization] La organización a verificar
  # @return [Boolean] true si tiene acceso, false en caso contrario
  def client_access?(organization)
    return false unless client_of?(organization)
    organization_users.exists?(organization: organization, role: 2, access_enabled: true)
  end

  # Verificar si el usuario tiene acceso a la plataforma en general
  # @return [Boolean] true si tiene acceso, false en caso contrario
  def platform_access?
    return true unless client? # Solo los clientes pueden tener restricciones de acceso
    return true if superadmin? || support? || owner? || collaborator? # Estos roles siempre tienen acceso

    # Para clientes, verificar si tienen acceso en alguna organización
    organization_users.exists?(role: 2, access_enabled: true)
  end

  # Métodos de conveniencia (legacy)

  # Obtener todos los colaboradores de una organización (para owners)
  def organization_members
    return team_members if owner?
    return User.none unless owner? || superadmin? || support?

    if superadmin? || support?
      User.where(role: :collaborator)
    else
      User.none
    end
  end

  # Obtener todos los clientes según el rol del usuario sin duplicados
  # @return [ActiveRecord::Relation] Colección de usuarios con rol cliente
  def all_clients
    if superadmin? || support?
      # Superadmin y support ven todos los clientes del sistema
      return User.where(role: :client).distinct
    elsif owner?
      # Owner ve todos los clientes del sistema, pero podríamos limitarlo a sus organizaciones
      # para evitar mostrar clientes que no le corresponden
      orgs = administered_organizations.pluck(:id)
      return User.joins(:organization_users)
                .where(organization_users: { organization_id: orgs, role: 2 })
                .distinct
    elsif collaborator?
      # Colaboradores ven sus clientes asignados o todos los de su organización si tienen permiso
      return clients_for_member
    end

    User.none
  end

  # Obtiene los clientes para un usuario con rol colaborador sin duplicados
  # @return [ActiveRecord::Relation] Colección de clientes
  def clients_for_member
    if permission?('view_all_clients') && admin_id.present?
      # Si tiene permiso para ver todos los clientes, mostrar los de su organización
      admin_user = User.find_by(id: admin_id)
      return User.none unless admin_user

      orgs = admin_user.administered_organizations.pluck(:id)
      return User.joins(:organization_users)
                .where(organization_users: { organization_id: orgs, role: 2 })
                .distinct
    end

    # Si no tiene permiso, mostrar sus clientes asignados (tanto por assigned_to_id como por ClientAssignment)
    # Usamos una consulta SQL más eficiente para obtener todos los clientes asignados sin duplicados
    client_ids = User.where(assigned_to_id: id).pluck(:id) +
                 ClientAssignment.where(member_id: id).pluck(:client_id)

    # Eliminamos duplicados y obtenemos los clientes
    clients = User.where(id: client_ids.uniq)

    clients.any? ? clients : User.none
  end

  # Verifica si un colaborador tiene asignado a un cliente específico
  # @param client [User] El cliente a verificar
  # @return [Boolean] true si el cliente está asignado al colaborador
  def client_assigned?(client)
    return false unless collaborator? && client&.client?

    # Verificar asignación legacy (assigned_to_id)
    return true if client.assigned_to_id == id

    # Verificar asignación múltiple (ClientAssignment)
    member_assignments.exists?(client_id: client.id)
  end

  # Verifica si un cliente está asignado a un colaborador específico
  # @param member [User] El colaborador a verificar
  # @return [Boolean] true si el cliente está asignado al colaborador
  def assigned_to_member?(member)
    return false unless client? && member&.collaborator?

    # Verificar asignación legacy (assigned_to_id)
    return true if assigned_to_id == member.id

    # Verificar asignación múltiple (ClientAssignment)
    client_assignments.exists?(member_id: member.id)
  end

  # Métodos para gestión de permisos

  # Verifica si el usuario tiene un permiso específico
  def permission?(permission_name)
    return true if superadmin? # Los superadmins tienen todos los permisos
    # Los support tienen algunos permisos por defecto
    return true if support? && ['view_all_clients'].include?(permission_name)
    # Los owners tienen todos estos permisos
    return true if owner? && %w[assign_members view_all_clients edit_client_data].include?(permission_name)

    # Para colaboradores, verificamos los permisos específicos
    permissions.exists?(name: permission_name)
  end

  # Asigna un permiso al usuario
  def grant_permission(permission_name, description = nil)
    return false unless Permission::AVAILABLE_PERMISSIONS.include?(permission_name)
    # Verificar si el permiso está disponible para este rol
    return false unless available_permissions.include?(permission_name)
    return true if permission?(permission_name) # Ya tiene el permiso

    permissions.create(name: permission_name, description: description)
    true
  rescue ActiveRecord::RecordInvalid
    false
  end

  # Revoca un permiso al usuario
  def revoke_permission(permission_name)
    permission = permissions.find_by(name: permission_name)
    return false unless permission

    permission.destroy
    true
  end

  # Obtiene todos los permisos disponibles para este usuario según su rol
  def available_permissions
    case role
    when 'superadmin'
      [] # No necesitan permisos adicionales
    when 'support', 'owner'
      ['impersonate_users'] # Pueden recibir el permiso de impersonación
    when 'collaborator'
      # Los colaboradores pueden tener cualquier permiso excepto impersonar
      Permission::AVAILABLE_PERMISSIONS - ['impersonate_users']
    when 'client'
      # Los clientes no tienen permisos disponibles
      []
    end
  end

  # Método para verificar si un usuario administra alguna organización
  def manages_organization?
    # Un usuario administra organizaciones si es owner de alguna
    administered_organizations.any?
  end

  # Métodos para verificar si un usuario puede impersonar a otros
  def can_impersonate?
    superadmin? || permission?('impersonate_users')
  end

  # Verifica si un usuario puede ser impersonado por otro usuario
  # @param user [User] El usuario que intenta impersonar
  # @return [Boolean] true si puede ser impersonado, false en caso contrario
  def can_be_impersonated_by?(user)
    return false unless user.can_impersonate?

    return impersonable_by_superadmin?(user) if user.superadmin?
    return impersonable_by_support?(user) if user.support? && user.permission?('impersonate_users')
    return impersonable_by_admin?(user) if user.owner? && user.permission?('impersonate_users')

    false
  end

  # Verifica si el usuario tiene una excepción para un permiso específico
  # @param permission_key [String] La clave del permiso
  # @return [Boolean, nil] true si tiene permiso, false si está explícitamente denegado, nil si no hay excepción
  def permission_override_status(permission_key)
    UserPermissionOverride.user_permission_status(self, permission_key)
  end

  # Verifica si el usuario tiene permiso para una configuración específica
  # considerando tanto la configuración global como las excepciones a nivel de usuario
  # @param config_key [String] La clave de la configuración
  # @return [Boolean] true si tiene permiso, false en caso contrario
  def has_system_permission?(config_key)
    # Los superadmins siempre tienen todos los permisos
    return true if superadmin?

    # Verificar si hay una excepción para este usuario
    override_status = permission_override_status(config_key)

    # Si hay una excepción, usar ese valor
    return override_status unless override_status.nil?

    # Si no hay excepción, usar la configuración global
    SystemConfiguration.get_bool(config_key)
  end

  # Verifica si puede ser impersonado por un superadmin
  # @param user [User] El superadmin que intenta impersonar
  # @return [Boolean] true si puede ser impersonado, false en caso contrario
  def impersonable_by_superadmin?(user)
    user.superadmin? && !superadmin?
  end

  # Verifica si puede ser impersonado por un usuario de soporte
  # @param user [User] El usuario de soporte que intenta impersonar
  # @return [Boolean] true si puede ser impersonado, false en caso contrario
  def impersonable_by_support?(user)
    !superadmin? && !support?
  end

  # Verifica si puede ser impersonado por un propietario
  # @param user [User] El propietario que intenta impersonar
  # @return [Boolean] true si puede ser impersonado, false en caso contrario
  def impersonable_by_admin?(user)
    (collaborator? && admin_id == user.id) || (client? && assigned_to&.admin_id == user.id)
  end
  # Validación personalizada de contraseña
  # - Requerida para todos los usuarios excepto clientes sin acceso
  # - Si se proporciona, debe tener al menos 6 caracteres, una mayúscula, una minúscula y un número
  # @return [Boolean] true si la contraseña es válida, false en caso contrario
  def password_validation
    # Si es un cliente, verificar si tiene acceso a la plataforma
    if client? && !platform_access?
      # Los clientes sin acceso no necesitan contraseña
      return true
    end

    # Para todos los demás usuarios, la contraseña es obligatoria
    if password_digest.blank?
      errors.add(:password, "no puede estar en blanco")
      return false
    end

    # Si se está estableciendo una nueva contraseña, validar su complejidad
    if password.present?
      if password.length < PASSWORD_MIN_LENGTH
        errors.add(:password, "debe tener al menos #{PASSWORD_MIN_LENGTH} caracteres")
        return false
      end

      # Validar complejidad de la contraseña solo para nuevos usuarios o cambios de contraseña
      # No aplicamos esta validación a contraseñas existentes para evitar problemas de compatibilidad
      if new_record? || password_digest_changed?
        unless password =~ PASSWORD_REGEX
          errors.add(:password, "debe contener al menos una letra mayúscula, una minúscula y un número")
          return false
        end
      end
    end

    true
  end
end

#has_secure_password
# Almacena la contraseña de manera segura en forma de hash en la base de datos.
# Genera métodos para establecer y verificar contraseñas.
# Valida automáticamente que el campo de contraseña no esté vacío.
# Agrega un atributo virtual 'password' y un atributo 'password_confirmation'
# para manejar la confirmación de la contraseña en los formularios.
# Incorpora métodos para autenticar usuarios, como 'authenticate', que verifica
# si una contraseña proporcionada coincide con la contraseña almacenada en la base de datos.
