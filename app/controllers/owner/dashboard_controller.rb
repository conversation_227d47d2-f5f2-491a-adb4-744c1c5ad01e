# frozen_string_literal: true

module Owner
  # Controlador para el dashboard principal del propietario
  # Muestra información resumida y enlaces rápidos a las principales funciones
  class DashboardController < ApplicationController
    before_action :require_login
    before_action :set_active_organization
    
    # GET /owner
    # Muestra el dashboard principal del propietario con información
    # resumida de su organización activa y accesos rápidos
    def index
      @organization = Organization.find_by(id: session[:active_organization_id])
      if @organization
        @clients_count = @organization.clients.count
        @collaborators_count = @organization.organization_users.where.not(user_id: current_user.id).count
        @recent_clients = @organization.clients.order(created_at: :desc).limit(5)
        @recent_collaborators = @organization.organization_users
                                 .includes(:user)
                                 .where.not(user_id: current_user.id)
                                 .order(created_at: :desc)
                                 .limit(5)
      end
    end
    
    private
    
    # Establece la organización activa para el usuario
    # Si no tiene una organización activa, intenta establecer la primera
    # Si no tiene organizaciones, redirige a la creación de organizaciones
    def set_active_organization
      # Verifica si ya hay una organización activa en la sesión
      if session[:active_organization_id].present? && 
         current_user.organizations.exists?(session[:active_organization_id])
        return
      end
      
      # Intenta establecer la primera organización como activa
      org = current_user.organizations.first
      if org
        session[:active_organization_id] = org.id
      else
        # Si no tiene organizaciones, redirige a crear una
        redirect_to new_owner_organization_path, 
                    notice: "Primero debes crear una organización para comenzar."
      end
    end
    
    # Verifica que el usuario haya iniciado sesión
    # Redirige si no ha iniciado sesión
    def require_login
      unless current_user
        redirect_to main_root_path, alert: "Debes iniciar sesión para acceder a esta página."
      end
    end
  end
end
