# frozen_string_literal: true

module Owner
  # Controlador para gestionar la relación entre usuarios y organizaciones
  # Permite añadir, editar y eliminar miembros de una organización, así como
  # gestionar sus roles dentro de la misma
  class OrganizationUsersController < ApplicationController
  before_action :require_login
  before_action :set_organization
  before_action :check_admin_permission
  before_action :set_organization_user, only: [:edit, :update, :destroy]

  def index
    @organization_users = @organization.organization_users.includes(:user)
  end

  def new
    @organization_user = @organization.organization_users.new
    # Para la creación de un nuevo usuario
    @user = User.new
  end

  def create
    # Crear un nuevo usuario
    @user = User.new(user_params)

    # Asignar el rol de colaborador por defecto
    @user.role = :collaborator
    
    # Asignar el admin_id ya que siempre será un colaborador
    @user.admin = current_user if current_user.owner?

    # Intentar guardar el usuario
    if @user.save
      # Crear la relación con la organización
      @organization_user = @organization.organization_users.new(role: role_param, user: @user)

      if @organization_user.save
        redirect_to edit_owner_organization_path(@organization, section: 'collaborators'),
                    notice: "Usuario creado y agregado correctamente como colaborador"
        return
      else
        # Si falla la creación de la relación, eliminar el usuario creado
        @user.destroy
      end
    end

    # Si llegamos aquí, hubo un error
    @organization_user = @organization.organization_users.new(role: role_param)
    render :new, status: :unprocessable_entity
  end

  def edit
    @available_roles = OrganizationUser.roles.keys
    @user = @organization_user.user
  end

  def update
    @user = @organization_user.user
    
    if params[:user].present?
      if @user.update(collaborator_user_params)
        redirect_to edit_owner_organization_path(@organization, section: 'collaborators'),
                  notice: "Datos del colaborador actualizados correctamente"
      else
        render :edit, status: :unprocessable_entity
      end
    else
      redirect_to edit_owner_organization_path(@organization, section: 'collaborators')
    end
  end

  def confirm_destroy
    @organization_user = @organization.organization_users.find(params[:id])
  end

  def destroy
    @organization_user.destroy
    redirect_to edit_owner_organization_path(@organization, section: 'collaborators'),
                notice: "Colaborador eliminado de la organización"
  end

  private

  def set_organization
    @organization = Organization.find(params[:organization_id])
  end

  def set_organization_user
    @organization_user = @organization.organization_users.find(params[:id])
  end

  def check_admin_permission
    return if current_user.admin_of?(@organization)

    redirect_to owner_organization_path(@organization.id),
              alert: "No tienes permisos para gestionar los colaboradores de esta organización"
  end

  def organization_user_params
    params.require(:organization_user).permit(:user_id, :role)
  end

  def user_params
    # Permitir solo los campos necesarios para crear un usuario
    params.require(:user).permit(:email, :name, :last_name, :password, :password_confirmation)
  end
  
  def collaborator_user_params
    # Permitir campos para actualizar un colaborador existente
    params.require(:user).permit(:name, :last_name, :email)
  end
  
  def role_param
    params.dig(:organization_user, :role) || 'collaborator'
  end

  def require_login
    return if logged_in?

    redirect_to root_path, alert: "Necesitas iniciar sesión primero."
  end
  end
end
