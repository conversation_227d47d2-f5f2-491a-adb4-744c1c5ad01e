# frozen_string_literal: true

module Owner
  # Controlador para búsquedas en el espacio del propietario
  class SearchController < ApplicationController
    
    before_action :require_authentication
    before_action :require_owner
    before_action :set_organization
    
    # Verifica que el usuario sea propietario
    def require_owner
      return if current_user.owner?
      
      respond_to do |format|
        format.json { render json: { error: 'No autorizado' }, status: :forbidden }
        format.html { redirect_to root_path, alert: 'No tiene permisos para acceder a esta sección' }
      end
    end

    # GET /owner/search/clients
    # Búsqueda de clientes por RUT o nombre
    #
    # Parámetros:
    #   q: Término de búsqueda (RUT o nombre)
    #   page: Número de página para paginación (opcional)
    #
    # Retorna:
    #   JSON con los clientes encontrados
    def clients
      @search_term = params[:q].to_s.strip
      
      # Buscar clientes de la organización actual
      @clients = @organization.users
                             .client
                             .includes(:organization_users)
                             .where(organization_users: { organization: @organization })
                             
      # Aplicar búsqueda si hay término
      if @search_term.present?
        # Limpiar y formatear RUT para búsqueda
        rut_clean = @search_term.gsub(/[^0-9kK]/, '').upcase
        
        # Buscar por RUT (con y sin formato) o por nombre/email
        @clients = @clients.where(
          "unaccent(users.rut) ILIKE unaccent(:q) OR 
           unaccent(users.rut) ILIKE unaccent(:rut_clean) OR
           unaccent(users.first_name) ILIKE unaccent(:name) OR
           unaccent(users.last_name) ILIKE unaccent(:name) OR
           unaccent(users.email) ILIKE unaccent(:email)",
          q: "%#{@search_term}%",
          rut_clean: "%#{rut_clean}%",
          name: "%#{@search_term}%",
          email: "%#{@search_term}%"
        )
      end
      
      # Ordenar y paginar resultados
      @clients = @clients.order(:first_name, :last_name)
                         .page(params[:page])
                         .per(10)
      
      respond_to do |format|
        format.json do
          render json: {
            clients: @clients.as_json(only: [:id, :rut, :first_name, :last_name, :email]),
            meta: {
              total_pages: @clients.total_pages,
              current_page: @clients.current_page,
              total_count: @clients.total_count
            }
          }
        end
        
        format.html do
          render partial: 'owner/clients/client_list', 
                 locals: { clients: @clients },
                 status: :ok
        end
      end
    end

    # GET /owner/search/fiscal_data
    # Consulta datos fiscales a partir de un RUT
    #
    # Parámetros:
    #   rut: RUT a consultar
    #
    # Retorna:
    #   JSON con los datos fiscales obtenidos de la API externa
    def fiscal_data
      begin
        # Crear archivo log específico para fiscal
        fiscal_logger = Logger.new(Rails.root.join('log/fiscal_debug.log'))
        fiscal_logger.info("Iniciando depuración: #{Time.now}")
        
        rut = params[:rut].to_s.strip
        fiscal_logger.info("RUT recibido: #{rut}")
        
        # Verificar que se proporcionó un RUT
        if rut.blank?
          fiscal_logger.error("RUT vacío")
          return render json: { success: false, error: "Debe proporcionar un RUT" }, status: :bad_request
        end

        # Validación de RUT directamente en el controlador (versión simplificada)
        begin
          # Limpiar el RUT para que solo tenga números
          clean_rut = rut.to_s.gsub(/[^0-9]/, '')
          fiscal_logger.info("RUT limpio: #{clean_rut}")
          
          # Verificar longitud básica (para RUT uruguayo)
          if clean_rut.length != 12
            fiscal_logger.warn("RUT inválido: longitud incorrecta #{clean_rut.length}")
            return render json: { 
              success: false, 
              error: "El RUT debe tener 12 dígitos numéricos (actual: #{clean_rut.length})" 
            }, status: :unprocessable_entity
          end
          
          # Log de depuración del RUT recibido
          fiscal_logger.info("RUT validado correctamente: #{clean_rut}")
          
          # Comentamos la validación estricta de las posiciones 9-10
          # ya que no todos los RUTs uruguayos siguen este patrón
          # if clean_rut[8..9] != "00"
          #   fiscal_logger.warn("RUT inválido: posiciones 9-10 no son 00")
          #   return render json: { 
          #     success: false, 
          #     error: "El RUT tiene formato inválido" 
          #   }, status: :unprocessable_entity
          # end
          
          fiscal_logger.info("Validación de RUT: correcta")
        rescue => e
          fiscal_logger.error("Error al validar RUT: #{e.message}")
          fiscal_logger.error(e.backtrace.join("\n"))
          return render json: { success: false, error: "Error al validar RUT: #{e.message}" }, status: :internal_server_error
        end
        
        # Registrar la consulta para fines de auditoría
        Rails.logger.info("Consulta fiscal para RUT: #{rut} por usuario: #{current_user.id}")
        fiscal_logger.info("Llamando a FiscalDataService.fetch_fiscal_data")
        
        # Usar el nuevo servicio para obtener los datos fiscales
        # Esta llamada es síncrona pero podría hacerse async en el futuro
        begin
          result = FiscalDataService.fetch_fiscal_data(rut)
          fiscal_logger.info("Resultado: #{result.inspect}")
        
          # Verificar si la consulta fue exitosa
          if result && result[:success]
            # Asegurar que los datos se envían directamente, no anidados
            # Formatear la dirección para asegurar que sea un string
            direccion = if result[:address].is_a?(Hash)
              # Si es un hash, construir la dirección
              dir_parts = [
                result.dig(:address, :calle),
                result.dig(:address, :numero),
                result.dig(:address, :apartamento).presence,
                result.dig(:address, :localidad)
              ].compact
              
              # Unir las partes con espacios y limpiar espacios múltiples
              dir_parts.join(' ').gsub(/\s+/, ' ').strip
            else
              # Si ya es un string, usarlo directamente
              result[:address].to_s
            end
            
            # Obtener el ID del cliente si se proporcionó
            client_id = params[:client_id].presence
            fiscal_logger.info("=== INICIO GUARDADO AUTOMÁTICO ===")
            fiscal_logger.info("Client ID recibido: #{client_id}")
            fiscal_logger.info("Parámetros recibidos: #{params.inspect}")
            
            # Si hay un client_id, intentar guardar los datos automáticamente
            if client_id
              begin
                client = User.find_by(id: client_id)
                fiscal_logger.info("Cliente encontrado: #{client&.id} - #{client&.email}")
                
                if client
                  # Buscar la organización del cliente
                  organization = client.organizations.first || client.owned_organizations.first
                  fiscal_logger.info("Organización encontrada: #{organization&.id} - #{organization&.name}")
                  
                  if organization
                    # Preparar los atributos a actualizar
                    update_attrs = {
                      nombre_legal: result[:nombre_legal].presence,
                      nombre_fantasia: result[:nombre_fantasia].presence,
                      tipo_entidad: result[:tipo_entidad].presence,
                      direccion: direccion.presence,
                      telefono: result[:phone].presence,
                      email: result[:email].presence,
                      actividades: result[:actividades].presence
                    }.compact
                    
                    fiscal_logger.info("Atributos a actualizar: #{update_attrs.inspect}")
                    
                    # Actualizar los datos fiscales de la organización
                    organization.assign_attributes(update_attrs)
                    
                    # Guardar sin validaciones para evitar problemas con campos requeridos
                    if organization.save(validate: false)
                      fiscal_logger.info("✅ Datos fiscales guardados correctamente para la organización: #{organization.id}")
                      fiscal_logger.info("Datos actualizados: #{organization.reload.attributes.slice('nombre_legal', 'nombre_fantasia', 'tipo_entidad', 'direccion', 'telefono', 'email', 'actividades')}")
                      
                      # Forzar la recarga de la organización para asegurar que los cambios persistan
                      organization.reload
                    else
                      fiscal_logger.error("❌ Error al guardar: #{organization.errors.full_messages.join(', ')}")
                      fiscal_logger.error("Atributos con error: #{organization.attributes.slice('nombre_legal', 'nombre_fantasia', 'tipo_entidad', 'direccion', 'telefono', 'email', 'actividades')}")
                    end
                  else
                    fiscal_logger.warn("⚠️ No se pudo encontrar la organización para el cliente: #{client_id}")
                  end
                else
                  fiscal_logger.error("❌ Cliente no encontrado con ID: #{client_id}")
                end
              rescue => e
                fiscal_logger.error("❌ Error al guardar automáticamente los datos fiscales: #{e.message}")
                fiscal_logger.error("Backtrace: #{e.backtrace.first(10).join("\n")}")
                # No fallamos la petición, solo registramos el error
              end
              
              fiscal_logger.info("=== FIN GUARDADO AUTOMÁTICO ===")
            end
            
            # Formatear la respuesta para el frontend
            render json: { 
              success: true,
              nombre_legal: result[:nombre_legal],
              nombre_fantasia: result[:nombre_fantasia],
              tipo_entidad: result[:tipo_entidad],
              direccion: direccion,
              telefono: result[:phone],
              email: result[:email],
              actividades: result[:actividades],
              raw_data: result[:raw_data],
              saved: client_id.present? && organization.present?
            }, status: :ok
          else
            # Enviar error si no se pudieron obtener los datos
            fiscal_logger.warn("Error en consulta fiscal: #{result[:error]}")
            render json: { 
              success: false, 
              error: result[:error] || "No se pudieron obtener los datos fiscales" 
            }, status: :unprocessable_entity
          end
        rescue => e
          fiscal_logger.error("Error al llamar al servicio fiscal: #{e.message}")
          fiscal_logger.error(e.backtrace.join("\n"))
          render json: { 
            success: false, 
            error: "Error al consultar datos fiscales: #{e.message}" 
          }, status: :unprocessable_entity
        end
      rescue => e
        # Registrar el error
        Rails.logger.error("Error en fiscal_data: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        
        # Responder con un mensaje de error amigable
        render json: { success: false, error: "Error al consultar datos fiscales: #{e.message}" }, status: :internal_server_error
      end
    end
    
    private
    
    # Establece la organización actual del usuario
    # Si el usuario es propietario, usa su organización administrada
    # Si el usuario es colaborador, usa la organización del propietario
    def set_organization
      begin
        @organization = if current_user.owner?
          # Para propietarios, obtener la primera organización que administran
          current_user.administered_organizations.first
        elsif current_user.collaborator?
          # Para colaboradores, obtener su organización asignada
          current_user.organization
        else
          # Si el usuario no tiene un rol válido
          raise 'Usuario sin rol válido para realizar esta acción'
        end
        
        # Si no se pudo obtener la organización, lanzar una excepción
        if @organization.nil?
          error_msg = if current_user.owner?
            'No se encontró ninguna organización asociada a su cuenta de propietario.'
          else
            'No tiene una organización asignada para realizar esta acción.'
          end
          raise error_msg
        end
        
        @organization # Retornar la organización encontrada
        
      rescue => e
        # Registrar el error para depuración
        Rails.logger.error("Error en set_organization: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        
        # Manejar la respuesta según el formato solicitado
        respond_to do |format|
          format.json do 
            render json: { 
              success: false, 
              error: e.message || 'No se pudo determinar la organización' 
            }, status: :unprocessable_entity
          end
          format.html { redirect_to root_path, alert: e.message || 'No se pudo determinar la organización' }
        end
        
        # Retornar false para detener el procesamiento de la acción
        false
      end
    end
  end
end
