# frozen_string_literal: true

module Owner
  # Controlador para gestionar organizaciones
  # Permite crear, editar, ver y eliminar organizaciones, así como
  # gestionar sus miembros y datos de facturación
  class OrganizationsController < ApplicationController
  before_action :require_login
  before_action :set_organization, only: [:show, :edit, :update, :destroy, :toggle_client_access]
  before_action :check_ownership, only: [:edit, :update, :destroy, :toggle_client_access]

  # GET /organizations
  # Lista todas las organizaciones del usuario actual
  # Si el usuario solo tiene una organización y no tiene permiso para múltiples,
  # lo redirige directamente a la página de edición de esa organización
  #
  # @return [void]
  def index
    # Cargar las organizaciones del usuario
    @organizations = current_user.organizations.includes(:organization_users).to_a
    
    # Verificar permisos
    @multiple_orgs_enabled = current_user.has_system_permission?('multiple_organizations_enabled')
    @override_status = current_user.permission_override_status('multiple_organizations_enabled')
    @should_redirect = @organizations.one? && !@multiple_orgs_enabled && !current_user.superadmin?

    # Debug: Mostrar información relevante
    Rails.logger.info "\n=== DEBUG ORGANIZACIONES ==="
    Rails.logger.info "Usuario: #{current_user.id} - #{current_user.email}"
    Rails.logger.info "Organizaciones: #{@organizations.map(&:id).join(', ')}"
    Rails.logger.info "Cantidad: #{@organizations.count}"
    Rails.logger.info "Múltiples orgs habilitadas: #{@multiple_orgs_enabled}"
    Rails.logger.info "Es superadmin: #{current_user.superadmin?}"
    Rails.logger.info "Debería redirigir: #{@should_redirect}"
    
    # Si solo tiene una organización, redirigir a la edición
    if @should_redirect
      org = @organizations.first
      Rails.logger.info "Redirigiendo a edición de organización: #{org.id} - #{org.name}"
      
      # Asegurarse de que la organización esté establecida como activa
      session[:active_organization_id] = org.id
      
      # Usar una redirección directa con la ruta completa
      redirect_to "/owner/organizations/#{org.id}/edit?section=general"
      return
    end
    
    # Si llegamos aquí, mostramos la lista de organizaciones
    # (solo para superadmins o usuarios con múltiples organizaciones)
    Rails.logger.info "Mostrando lista de organizaciones"
    Rails.logger.info "===========================\n"
  end

  def new
    # Verificar si está habilitada la creación de múltiples organizaciones
    # Los superadmins siempre pueden crear organizaciones
    # Los usuarios sin organizaciones siempre pueden crear una
    # Los usuarios con una organización solo pueden crear más si está habilitado o tienen una excepción
    # Los usuarios con múltiples organizaciones pueden seguir gestionándolas pero no crear nuevas si está deshabilitado
    if !current_user.has_system_permission?('multiple_organizations_enabled') &&
       !current_user.organizations.empty?
      redirect_to organizations_path, alert: "No está permitido crear múltiples organizaciones. Contacta al administrador."
      return
    end

    @organization = Organization.new
  end

  def create
    # Verificar si está habilitada la creación de múltiples organizaciones
    # Aplicamos la misma lógica que en el método new
    if !current_user.has_system_permission?('multiple_organizations_enabled') &&
       !current_user.organizations.empty?
      redirect_to organizations_path, alert: "No está permitido crear múltiples organizaciones. Contacta al administrador."
      return
    end

    @organization = Organization.new(organization_params)

    # Establecer el acceso de clientes según la configuración del sistema
    @organization.client_access_enabled = SystemConfiguration.get_bool('client_access_enabled_by_default')

    if @organization.save
      # Crear la relación entre el usuario y la organización como propietario
      OrganizationUser.create(organization: @organization, user: current_user, role: :owner)

      redirect_to organizations_path, notice: "Organización creada correctamente"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def show
    # Establecer esta organización como la activa al visitarla
    session[:active_organization_id] = @organization.id
    
    # Redirigir a la página de edición con la pestaña de información general
    # Usamos la ruta completa para evitar problemas de enrutamiento
    redirect_to "/owner/organizations/#{@organization.id}/edit?section=general"
  end

  def edit
    # Por defecto, mostrar solo la información general
    @section = params[:section] || 'general'

    # Cargar los colaboradores de la organización si estamos en la sección de colaboradores
    return unless @section == 'collaborators'

    # Filtrar solo los usuarios con rol 'collaborator'
    @organization_users = @organization.organization_users.where(role: 'collaborator').includes(:user)
  end

  def update
    section = params[:section] || 'general'
    
    if @organization.update(organization_params)
      redirect_to edit_owner_organization_path(@organization, section: section),
                  notice: "Organización actualizada correctamente"
    else
      @section = section
      render :edit, status: :unprocessable_entity
    end
  end

  # Muestra la página de confirmación de eliminación
  def confirm_destroy
    @organization = current_user.organizations.find(params[:id])
    @section = params[:section] || 'general'
  rescue ActiveRecord::RecordNotFound
    redirect_to owner_organizations_path, alert: "Organización no encontrada"
  end

  # DELETE /organizations/:id
  # Elimina lógicamente (soft delete) una organización
  # Actualiza el estado a 'deleted' y establece deleted_at y deleted_by_id
  #
  # @return [void]
  def destroy
    @organization = current_user.organizations.find(params[:id])
    section = params[:section] || 'general'
    confirmation = params[:confirmation]

    # Si no se ha confirmado, redirigir a la página de confirmación
    if confirmation.blank? || confirmation != 'ELIMINAR'
      redirect_to confirm_destroy_owner_organization_path(@organization, section: section),
                  alert: "Debes escribir exactamente 'ELIMINAR' para confirmar la eliminación"
      return
    end

    # Si todo está correcto, procedemos con la eliminación
    if @organization.soft_delete(current_user)
      # Si la organización eliminada era la activa, seleccionar otra
      if current_organization&.id == @organization.id
        new_org = current_user.organizations.where.not(id: @organization.id).first
        set_active_organization(new_org) if new_org
      end

      redirect_to owner_organizations_path, notice: "Organización eliminada correctamente"
    else
      redirect_to edit_owner_organization_path(@organization, section: section),
                  alert: "No se pudo eliminar la organización: #{@organization.errors.full_messages.join(', ')}"
    end
  rescue ActiveRecord::RecordNotFound
    redirect_to owner_organizations_path, alert: "Organización no encontrada"
  end

  # Cambia la organización activa del usuario
  def switch
    organization = current_user.organizations.find(params[:id])

    if set_active_organization(organization)
      redirect_back fallback_location: root_path, notice: "Has cambiado a la organización: #{organization.name}"
    else
      redirect_back fallback_location: root_path, alert: "No se pudo cambiar de organización"
    end
  end

  # POST /organizations/:id/toggle_client_access
  # Activa o desactiva el acceso de clientes a la plataforma
  #
  # @return [void]
  def toggle_client_access
    # Cambiar el estado de acceso de clientes
    new_state = !@organization.client_access_enabled

    if @organization.update(client_access_enabled: new_state)
      # Si se está activando el acceso, redirigir a la sección de acceso de clientes
      if new_state
        flash[:notice] = "Acceso de clientes activado correctamente."
        redirect_to edit_organization_path(@organization, section: 'client_access')
      else
        # Si se está desactivando, redirigir a la sección general
        flash[:notice] = "Acceso de clientes desactivado correctamente."
        redirect_to edit_organization_path(@organization, section: 'general')
      end
    else
      flash[:alert] = "No se pudo cambiar la configuración de acceso de clientes: #{@organization.errors.full_messages.join(', ')}"
      redirect_to edit_organization_path(@organization, section: 'general')
    end
  end

  private

  # Establece la organización actual basada en el parámetro id
  # Respeta el scope de soft delete por defecto
  # @return [Organization] la organización encontrada
  def set_organization
    # El scope default_scope { where(deleted_at: nil) } ya se aplica automáticamente
    @organization = Organization.includes(:organization_users).find(params[:id])
  end

  # Verifica que el usuario actual sea administrador de la organización
  # Redirige si no tiene permisos
  # @return [Boolean] true si el usuario es administrador, false en caso contrario
  def check_ownership
    return true if current_user.admin_of?(@organization)

    redirect_to profile_path, alert: "No tienes permisos para editar esta organización"
    false
  end

  # Parámetros permitidos para la organización
  # @return [ActionController::Parameters] parámetros filtrados
  def organization_params
    params.require(:organization).permit(:name, :rut, :direccion, :telefono, :email_facturacion)
  end

  # Verifica que el usuario haya iniciado sesión
  # Redirige si no ha iniciado sesión
  # @return [Boolean] true si el usuario ha iniciado sesión, false en caso contrario
  def require_login
    return true if logged_in?

    redirect_to root_path, alert: "Necesitas iniciar sesión primero."
    false
  end
  end
end
