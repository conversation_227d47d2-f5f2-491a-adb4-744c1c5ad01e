# frozen_string_literal: true

module Owner
  # Controlador para gestionar perfiles de propietarios
  # Permite a los propietarios ver y editar su información personal
  class ProfilesController < ApplicationController
    before_action :require_authentication
    before_action :require_owner

    def show
      @user = current_user
      @tab = params[:tab] || 'personal'
      set_organizations
    end

    def update
      @user = current_user

      if @user.update(user_params)
        redirect_to owner_profile_path(tab: 'personal'), notice: "Perfil actualizado correctamente"
      else
        set_organizations
        render :show, status: :unprocessable_entity
      end
    end

    private

    def user_params
      params.require(:user).permit(:email, :name, :last_name)
    end

    def require_owner
      return if current_user&.owner?
      
      flash[:alert] = 'No tienes permisos para acceder a esta sección.'
      redirect_to dashboard_path
    end

    def set_organizations
      @admin_organizations = @user.administered_organizations
      @member_organizations = @user.member_organizations
      @client_organizations = @user.client_organizations
    end
  end
end
