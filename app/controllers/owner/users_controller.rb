# frozen_string_literal: true

module Owner
  # Controlador para gestionar usuarios
  # Permite crear, editar, ver y eliminar usuarios del sistema
  class UsersController < ApplicationController
  include PermissionVerification

  before_action :require_admin_or_higher
  before_action :set_user, only: [:show, :edit, :update, :destroy]
  before_action :check_management_permission, only: [:edit, :update, :destroy]

  # GET /users
  def index
    @users = filtered_users
  end

  # GET /users/1
  def show; end

  # GET /users/new
  def new
    @user = User.new
  end

  # GET /users/1/edit
  def edit; end

  # POST /users
  def create
    @user = User.new(user_params)

    # Asignar relaciones según el rol
    @user.admin = current_user if @user.collaborator? && current_user.admin?

    if @user.save
      redirect_to @user, notice: 'Usuario creado correctamente.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /users/1
  def update
    if @user.update(user_params)
      redirect_to @user, notice: 'Usuario actualizado correctamente.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /users/1
  def destroy
    @user.destroy
    redirect_to users_url, notice: 'Usuario eliminado correctamente.'
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    permitted_params = [:email, :name, :last_name]

    # Solo permitir cambiar el rol si el usuario actual es superadmin o support
    permitted_params << :role if current_user.superadmin? || current_user.support?

    # Solo permitir asignar admin_id si el usuario actual es superadmin, support o admin
    permitted_params << :admin_id if current_user.superadmin? || current_user.support? || current_user.admin?

    # Solo permitir asignar assigned_to_id si el usuario actual es superadmin, support, admin
    # o si es colaborador con permiso assign_members
    if current_user.superadmin? || current_user.support? || current_user.admin? ||
       (current_user.collaborator? && current_user.permission?('assign_members'))
      permitted_params << :assigned_to_id
    end

    # Incluir password solo en creación o si se proporciona explícitamente
    if action_name == 'create' || params[:user][:password].present?
      permitted_params += [:password,
                           :password_confirmation]
    end

    params.require(:user).permit(permitted_params)
  end

  def require_admin_or_higher
    return if current_user&.superadmin? || current_user&.support?

    redirect_to organizations_path, alert: "No tienes permisos para acceder a la gestión global de usuarios."
  end

  # Verifica si el usuario actual puede gestionar al usuario objetivo
  def check_management_permission
    return if can_manage_user?(@user)

    redirect_to users_path, alert: "No tienes permisos para gestionar este usuario."
  end

  # Filtra los usuarios según el rol del usuario actual y la organización activa
  def filtered_users
    if current_user.superadmin?
      User.all
    elsif current_user.support?
      User.where.not(role: :superadmin)
    else
      # Para admin y colaborador, filtrar por la organización activa
      return User.none unless current_organization

      # Obtener los usuarios de la organización activa
      org_users = current_organization.users

      if current_user.admin_of?(current_organization)
        # Admin ve todos los usuarios de su organización
        org_users
      elsif current_user.collaborator_of?(current_organization)
        if current_user.permission?('view_all_clients')
          # Si tiene permiso para ver todos los clientes de la organización
          org_users.where(role: :client)
        else
          # Solo ve sus clientes asignados
          User.where(assigned_to_id: current_user.id)
        end
      else
        User.none
      end
    end
  end
  end
end
