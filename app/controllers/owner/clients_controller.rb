# frozen_string_literal: true

module Owner
  # Controlador para gestionar clientes en el espacio de nombres del propietario
  # Permite a los propietarios ver y gestionar todos los clientes de su organización
  class ClientsController < ApplicationController
    before_action :require_authentication
    before_action :require_owner
    before_action :set_organization
    before_action :set_client, only: [:show, :edit, :update, :destroy, :toggle_access, :validate_fiscal_data]
    before_action :process_activities_param, only: [:update]
    
    # Make permission methods available in views
    helper_method :can_edit_client?, :can_view_client?, :can_manage_clients?

    # GET /owner/clients
    def index
      @clients = @organization.clients.includes(:organization_users)
    end

    # GET /owner/clients/:id
    def show
      @tab = params[:tab] || 'info'
      @org_user = @client.organization_users.find_by(organization: @organization)
    end

    # GET /owner/clients/new
    def new
      @client = @organization.clients.new
    end

    # POST /owner/clients
    def create
      @client = @organization.clients.new(client_params)
      
      if @client.save
        redirect_to owner_client_path(@client), notice: 'Cliente creado exitosamente.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    # GET /owner/clients/:id/edit
    def edit
    end

    # PATCH/PUT /owner/clients/:id
    def update
      Rails.logger.info("=== INICIO DE ACTUALIZACIÓN ===")
      Rails.logger.info("Parámetros recibidos: #{params.inspect}")
      
      tab = params[:tab] || 'info'
      success = false
      
      # Inicializar variables para el manejo de errores
      client_updated = org_updated = true
      organization = @client.organizations.first
      
      Rails.logger.info("Actualizando pestaña: #{tab}")
      
      # Actualizaciones basadas en la pestaña
      case tab
      when 'info'
        # Mostrar parámetros recibidos
        Rails.logger.info("Parámetros de cliente: #{params[:client]}")
        Rails.logger.info("Parámetros de organización: #{params[:organization]}")
        
        # Actualizar datos del cliente
        client_params = client_info_params
        Rails.logger.info("Parámetros procesados para cliente: #{client_params.inspect}")
        
        if client_params.present?
          client_updated = @client.update(client_params)
          Rails.logger.info("Cliente actualizado: #{client_updated}")
          Rails.logger.info("Errores: #{@client.errors.full_messages}")
        end
        
        # Actualizar organización si se proporcionaron datos
        if params[:organization].present? && organization.present?
          org_params = {}
          
          # Limpiar y validar RUT si está presente
          if params[:organization][:rut].present?
            rut = params[:organization][:rut].to_s.gsub(/[^0-9kK]/, '').upcase
            
            # Solo validar el formato del RUT, no la lógica de negocio
            if rut.length < 8 || rut.length > 12
              organization.errors.add(:rut, "debe tener entre 8 y 12 dígitos")
              org_updated = false
              Rails.logger.error("Error en formato de RUT: #{rut}")
            else
              org_params[:rut] = rut
            end
          end
          
          # Agregar nombre si está presente
          if params[:organization][:name].present?
            org_params[:name] = params[:organization][:name]
          end
          
          # Agregar teléfono si está presente
          if params[:organization][:telefono].present?
            org_params[:telefono] = params[:organization][:telefono]
          end
          
          Rails.logger.info("Parámetros para actualizar organización: #{org_params.inspect}")
          
          # Actualizar solo si hay parámetros y no hay errores de validación
          if org_params.any? && org_updated
            org_updated = organization.update(org_params)
            Rails.logger.info("Organización actualizada: #{org_updated}")
            Rails.logger.info("Errores: #{organization.errors.full_messages}")
          end
        end
        
        success = client_updated && org_updated
      when 'fiscal'
        if params[:organization].present?
          # Usar la misma organización que se muestra en la vista (la principal)
          organization = find_or_create_client_organization(@client)
          success = organization.update(organization_params)
          
          # Logging para depuración
          Rails.logger.debug("Actualización fiscal para organización: #{organization.id}, nombre_legal: #{organization.nombre_legal}")
        else
          success = true
        end
      when 'billing'
        if params[:organization].present? && @client.client_organizations.any?
          client_org = @client.client_organizations.first
          success = client_org.update(billing_params)
        else
          success = true
        end
      end
      
      if success
        redirect_to owner_client_path(@client, tab: tab), notice: 'Cliente actualizado exitosamente.'
      else
        # Si no tuvo éxito, mostrar errores específicos
        # Construir un mensaje de error detallado
        error_message = "No se pudo actualizar la información del cliente."
        
        # Agregar errores de RUT si existen
        if organization.present? && organization.errors[:rut].any?
          error_message = "Error en el RUT: #{organization.errors[:rut].join(', ')}"
        end
        
        flash.now[:alert] = error_message
        @tab = tab
        @org_user = @client.organization_users.find_by(organization: @organization)
        render :show, status: :unprocessable_entity
      end
    end

    # DELETE /owner/clients/:id
    def destroy
      @client.destroy
      redirect_to owner_clients_path, notice: 'Cliente eliminado exitosamente.'
    end

    # PATCH /owner/clients/:id/toggle_access
    def toggle_access
      org_user = @client.organization_users.find_by(organization: @organization)
      if org_user
        org_user.toggle!(:access_enabled)
        status = org_user.access_enabled? ? 'habilitado' : 'deshabilitado'
        redirect_back fallback_location: owner_clients_path, notice: "Acceso del cliente #{status} correctamente."
      else
        redirect_back fallback_location: owner_clients_path, alert: "No se pudo actualizar el estado de acceso."
      end
    end

    # GET /owner/clients/:id/validate_fiscal_data
    def validate_fiscal_data
      @client_organization = find_or_create_client_organization(@client) if @client
      
      respond_to do |format|
        format.html do
          # This will render the validate_fiscal_data template
        end
        format.json do
          # For AJAX requests, return JSON response
          render json: { success: true }
        end
      end
    end

    # POST /owner/clients/:id/update_fiscal_data
    def update_fiscal_data
      # Obtener el cliente
      @client = User.find(params[:id])

      # Verificar que el usuario sea un cliente
      unless @client.client?
        redirect_to owner_clients_path, alert: "El usuario seleccionado no es un cliente."
        return
      end

      # Verificar que el usuario tenga permisos para editar este cliente
      unless can_edit_client?(@client)
        redirect_to owner_clients_path, alert: "No tienes permisos para editar este cliente."
        return
      end

      # Obtener la organización del cliente
      @client_organization = find_or_create_client_organization(@client)

      # Obtener el RUT del formulario
      raw_rut = params[:rut].to_s
      Rails.logger.info("RUT recibido del formulario: #{raw_rut}")

      # Normalizar el RUT (eliminar caracteres no numéricos)
      @rut = RutValidatorService.normalize(raw_rut)
      Rails.logger.info("RUT normalizado: #{@rut}")

      # Primero, validar el formato del RUT
      unless RutValidatorService.valid?(@rut)
        Rails.logger.warn("RUT inválido: #{@rut} - No cumple con el formato requerido")
        flash[:alert] = "El formato del RUT no es válido. Debe tener 12 dígitos numéricos y seguir el formato uruguayo."
        redirect_to validate_fiscal_data_owner_clients_path(@client)
        return
      end

      Rails.logger.info("RUT válido: #{@rut} - Consultando API externa")

      begin
        # Si el RUT es válido, consultar la API externa para obtener datos fiscales
        @client_data = RutApiService.fetch_fiscal_data(@rut)

        # Registrar la respuesta para depuración
        Rails.logger.debug("Respuesta de la API: #{@client_data.inspect}")

        if @client_data[:success]
          Rails.logger.info("Datos fiscales obtenidos correctamente para RUT: #{@rut}")
          
          # Preparar los datos para mostrar en el formulario
          @fiscal_data = {
            rut: @rut,
            nombre_legal: @client_data[:nombre_legal].to_s.presence || "",
            nombre_fantasia: @client_data[:nombre_fantasia].to_s.presence || "",
            tipo_entidad: @client_data[:tipo_entidad].to_s.presence || "",
            direccion: @client_data[:address].to_s.presence || "",
            telefono: @client_data[:phone].to_s.presence || "",
            email: @client_data[:email].to_s.presence || ""
          }
          
          # Actualizar automáticamente la organización con los datos obtenidos
          organization = find_or_create_client_organization(@client)
          update_attrs = {
            rut: @rut,
            nombre_legal: @client_data[:nombre_legal].to_s.presence,
            nombre_fantasia: @client_data[:nombre_fantasia].to_s.presence,
            tipo_entidad: @client_data[:tipo_entidad].to_s.presence,
            direccion: @client_data[:address].to_s.presence,
            telefono: @client_data[:phone].to_s.presence,
            email: @client_data[:email].to_s.presence,
            actividades: @client_data[:actividades]
          }.compact
          
          if organization.update(update_attrs)
            Rails.logger.info("✅ Datos fiscales guardados automáticamente para la organización: #{organization.id}")
            redirect_to owner_client_path(@client, tab: 'fiscal'), 
                        notice: 'Datos fiscales actualizados automáticamente.'
          else
            Rails.logger.error("❌ Error al guardar automáticamente: #{organization.errors.full_messages.join(', ')}")
            # Si falla el guardado automático, mostrar el formulario con los datos para corrección manual
            @fiscal_data = update_attrs.merge(rut: @rut)
            render :validate_fiscal_data
          end
        else
          Rails.logger.error("Error al obtener datos fiscales para RUT: #{@rut} - #{@client_data[:error]}")
          flash[:alert] = "No se pudieron obtener los datos fiscales para el RUT #{@rut}. Error: #{@client_data[:error]}"
          redirect_to validate_fiscal_data_owner_clients_path(@client)
        end
      rescue => e
        # Capturar cualquier excepción no manejada
        Rails.logger.error("Excepción no manejada en update_fiscal_data: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        flash[:alert] = "Ocurrió un error inesperado al validar el RUT. Por favor, inténtelo de nuevo."
        redirect_to validate_fiscal_data_owner_clients_path(@client)
      end
    end

    # PATCH /owner/clients/:id/update_organization_fiscal_data
    def update_organization_fiscal_data
      @client = Client.find(params[:id])
      @client_organization = find_or_create_client_organization(@client)
      
      # Registrar los parámetros recibidos
      Rails.logger.info("=== PARÁMETROS RECIBIDOS ===")
      Rails.logger.info("Params: #{params.inspect}")
      
      # Obtener los parámetros permitidos
      org_params = organization_params
      Rails.logger.info("=== PARÁMETROS PERMITIDOS ===")
      Rails.logger.info("Org params: #{org_params.inspect}")
      
      # Registrar el estado actual de la organización
      Rails.logger.info("=== ESTADO ACTUAL DE LA ORGANIZACIÓN ===")
      Rails.logger.info("ID: #{@client_organization.id}")
      Rails.logger.info("Atributos actuales: #{@client_organization.attributes}")
      
      # Actualizar los campos de la organización con los datos del formulario
      if @client_organization.update(org_params)
        Rails.logger.info("=== DATOS ACTUALIZADOS CON ÉXITO ===")
        Rails.logger.info("Nuevos atributos: #{@client_organization.reload.attributes}")
        
        respond_to do |format|
          format.html { 
            redirect_to owner_client_path(@client), 
                        notice: "Datos fiscales actualizados correctamente." 
          }
          format.json { render json: { success: true, message: "Datos fiscales actualizados correctamente." } }
        end
      else
        Rails.logger.error("=== ERROR AL ACTUALIZAR ===")
        Rails.logger.error("Errores: #{@client_organization.errors.full_messages}")
        
        @fiscal_data = {
          rut: @client_organization.rut,
          nombre_legal: @client_organization.nombre_legal,
          nombre_fantasia: @client_organization.nombre_fantasia,
          tipo_entidad: @client_organization.tipo_entidad,
          direccion: @client_organization.direccion,
          telefono: @client_organization.telefono,
          email: @client_organization.email_facturacion
        }
        
        respond_to do |format|
          format.html {
            flash.now[:alert] = "Error al guardar los datos fiscales: #{@client_organization.errors.full_messages.join(', ')}"
            render :validate_fiscal_data
          }
          format.json { 
            render json: { 
              success: false, 
              errors: @client_organization.errors.full_messages,
              message: "Error al guardar los datos fiscales: #{@client_organization.errors.full_messages.join(', ')}" 
            }, 
            status: :unprocessable_entity 
          }
        end
      end
    end

    # GET /owner/clients/export.csv
    def export
      @clients = @organization.clients
      
      respond_to do |format|
        format.csv do
          response.headers['Content-Type'] = 'text/csv'
          response.headers['Content-Disposition'] = "attachment; filename=clientes-#{Date.today}.csv"
          render template: 'owner/clients/export'
        end
      end
    end

    private

    # Helper para encontrar o crear la organización de un cliente
    def find_or_create_client_organization(client)
      # Intentar encontrar una organización existente para el cliente
      # El método client.organizations devuelve directamente las organizaciones asociadas
      client_org = client.organizations.first
      
      # Si no existe, crear una nueva sin valores por defecto
      unless client_org
        # Crear una nueva organización sin nombre por defecto
        client_org = Organization.create(
          name: nil,  # No establecer nombre por defecto
          created_by: current_user,
          actividades: []  # Asegurar que no haya actividades por defecto
        )
        
        # Asociar el cliente con la nueva organización
        ClientOrganization.create(
          client: client,
          organization: client_org,
          role: 'owner'
        )
      end
      
      # Devolver el objeto Organization
      client_org
    end

    def set_organization
      @organization = current_organization
      redirect_to owner_organizations_path, alert: 'Debes seleccionar una organización primero.' unless @organization
    end

    def set_client
      @client = @organization.clients.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to owner_clients_path, alert: 'Cliente no encontrado.'
    end

    def client_info_params
      # Handle both :client and :user parameter structures
      if params[:client].present?
        params.require(:client).permit(
          :name, :last_name, :phone
        )
      elsif params[:user].present?
        params.require(:user).permit(
          :name, :last_name, :phone
        )
      else
        {}
      end
    end
    
    def organization_params
      org_params = params.require(:organization).permit(
        :nombre_legal, :nombre_fantasia, :tipo_entidad, :rut, :email, :telefono, :direccion, :comuna, :region, :actividades => []
      )
      
      # Si viene el parámetro razon_social, usarlo como nombre_legal para mantener compatibilidad
      if params[:organization][:razon_social].present? && org_params[:nombre_legal].blank?
        org_params[:nombre_legal] = params[:organization][:razon_social]
      end
      
      # Asegurarse de que el RUT esté limpio (sin puntos ni guiones)
      if org_params[:rut].present?
        org_params[:rut] = org_params[:rut].gsub(/[^0-9kK]/, '').upcase
      end
      
      # Asegurarse de que los campos text/string sean nil en vez de strings vacíos si vienen vacíos
      # Esto asegura que si un campo se limpió en el formulario, se guarde como nil en la base de datos
      # En Rails, los campos de texto vacíos se guardan como strings vacíos pero esto puede causar problemas
      [:nombre_legal, :nombre_fantasia, :tipo_entidad, :direccion, :telefono, :email, :comuna, :region].each do |field|
        if params[:organization].has_key?(field.to_s) && params[:organization][field.to_s].blank?
          org_params[field] = nil
        end
      end
      
      # Asegurarse de que el campo actividades sea un array vacío si se limpió en el formulario
      if params[:organization].has_key?('actividades') && params[:organization]['actividades'].blank?
        org_params[:actividades] = []
      end
      
      # Logging para depuración
      Rails.logger.debug("Params procesados: #{org_params.to_h}")
      
      org_params
    end
    
    def billing_params
      params.require(:organization).permit(
        :billing_name, :billing_rut, :billing_email, :billing_address, 
        :billing_comuna, :billing_region
      )
    end
    
    def client_params
      params.require(:client).permit(
        :email, :name, :last_name, :phone, :active,
        :password, :password_confirmation
      )
    end
    
    # Procesa el parámetro de actividades antes de la acción update
    # Convierte el string de actividades separado por saltos de línea en un array
    def process_activities_param
      return unless params[:organization]
      
      # Asegurarse de que actividades siempre tenga un valor definido, incluso si es vacío
      # Esto garantiza que el campo se actualice correctamente cuando se limpia
      params[:organization][:actividades] ||= []
      
      # Si actividades es un string (viene del textarea), convertirlo a array
      if params[:organization][:actividades].is_a?(String)
        if params[:organization][:actividades].blank?
          params[:organization][:actividades] = []
        else
          # Dividir por saltos de línea, eliminar líneas vacías y espacios en blanco
          actividades = params[:organization][:actividades].split("\n")
                                    .map(&:strip)
                                    .reject(&:blank?)
          
          # Asignar el array de actividades
          params[:organization][:actividades] = actividades
        end
      end
    end

    def require_owner
      return if current_user&.owner?
      
      flash[:alert] = 'No tienes permisos para acceder a esta sección.'
      redirect_to dashboard_path
    end
  end
end
