# frozen_string_literal: true

module Main
  # Controlador para gestionar las sesiones de usuario
  # Maneja el inicio de sesión, cierre de sesión y autenticación de usuarios
  class SessionsController < ApplicationController
  def new
    # Si el usuario ya está autenticado, redirígelo a la página principal
    redirect_to home_path and return if current_user

    @user = User.new
  end

  def create
    user = User.find_by(email: params[:session][:email].downcase)

    if user && user.authenticate(params[:session][:password])
      # Verificar si el usuario tiene acceso a la plataforma
      if user.client? && !user.has_platform_access?
        flash[:alert] = "Tu acceso a la plataforma está deshabilitado. Contacta con la organización para más información."
        @user = User.new
        render :new, status: :unprocessable_entity
        return
      end

      login user
      
      # Redirigir según el rol del usuario a su dashboard correspondiente
      case user.role
      when 'superadmin'
        redirect_to superadmin_root_path
      when 'owner'
        redirect_to owner_root_path
      when 'collaborator'
        redirect_to collaborator_root_path
      when 'client'
        redirect_to client_root_path
      else
        redirect_to dashboard_path
      end
    else
      flash[:alert] = "Credenciales de acceso inválidas"
      @user = User.new # Necesario para que la vista no rompa si se vuelve a renderizar
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    logout current_user
    redirect_to root_path
  end
  end
end