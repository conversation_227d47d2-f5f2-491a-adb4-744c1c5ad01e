# Módulo para verificar permisos de usuarios en controladores
# Proporciona métodos para verificar si un usuario tiene permisos específicos
# y para gestionar el acceso a recursos basado en roles y permisos
module PermissionVerification
  extend ActiveSupport::Concern

  # Verifica si el usuario actual tiene un permiso específico
  def verify_permission(permission_name)
    return if current_user&.permission?(permission_name)

    flash[:alert] = "No tienes permisos para realizar esta acción."
    redirect_to root_path
  end

  # Verifica si el usuario actual puede asignar colaboradores a clientes
  def verify_can_assign_members
    verify_permission('assign_members')
  end

  # Verifica si el usuario actual puede ver todos los clientes de la organización
  def verify_can_view_all_clients
    verify_permission('view_all_clients')
  end

  # Verifica si el usuario actual puede editar datos de clientes
  def verify_can_edit_client_data
    verify_permission('edit_client_data')
  end

  # Verifica si el usuario actual puede gestionar al usuario objetivo
  # @param target_user [User] El usuario que se intenta gestionar
  # @return [Boolean] true si puede gestionar al usuario, false en caso contrario
  def can_manage_user?(target_user)
    return true if current_user.superadmin?
    return true if current_user.support? && !target_user.superadmin? && !target_user.support?

    # Un admin solo puede gestionar sus colaboradores y los clientes asignados a sus colaboradores
    if current_user.admin?
      return (target_user.collaborator? && target_user.admin_id == current_user.id) ||
             (target_user.client? && target_user.assigned_to&.admin_id == current_user.id)
    end

    # Un colaborador con permiso edit_client_data puede editar sus clientes asignados
    if current_user.collaborator? && current_user.permission?('edit_client_data')
      # Verificar asignación legacy (assigned_to_id)
      return true if target_user.client? && target_user.assigned_to_id == current_user.id

      # Verificar asignación múltiple (ClientAssignment)
      return true if target_user.client? && current_user.client_assigned?(target_user)
    end

    false
  end

  # Verifica si el usuario actual puede ver un cliente específico
  # @param client [User] El cliente que se intenta ver
  # @return [Boolean] true si puede ver al cliente, false en caso contrario
  def can_view_client?(client)
    # Superadmin y support pueden ver todos los clientes
    return true if current_user.superadmin? || current_user.support?

    # Owner puede ver los clientes de su organización
    return true if current_user.owner?

    # Colaborador con permiso view_all_clients puede ver todos los clientes de su organización
    return true if current_user.collaborator? && current_user.permission?('view_all_clients')

    # Colaborador puede ver sus clientes asignados (tanto por assigned_to_id como por ClientAssignment)
    if current_user.collaborator?
      # Verificar asignación legacy (assigned_to_id)
      return true if client.assigned_to_id == current_user.id

      # Verificar asignación múltiple (ClientAssignment)
      return true if current_user.client_assigned?(client)
    end

    # Cualquier usuario puede ver los clientes que ha creado
    # Verificamos si el cliente pertenece a alguna de las organizaciones del usuario
    client_orgs = client.organizations.pluck(:id)
    user_orgs = current_user.organizations.pluck(:id)
    return true if client_orgs.intersect?(user_orgs)

    false
  end

  # Verifica si el usuario actual puede editar un cliente específico
  # @param client [User] El cliente que se intenta editar
  # @return [Boolean] true si puede editar al cliente, false en caso contrario
  def can_edit_client?(client)
    # Superadmin y support pueden editar todos los clientes
    return true if current_user.superadmin? || current_user.support?

    # Owner puede editar los clientes de su organización
    return true if current_user.owner?

    # Colaborador con permiso edit_client_data puede editar sus clientes asignados
    # o los clientes que pertenecen a alguna de sus organizaciones
    if current_user.collaborator? && current_user.permission?('edit_client_data')
      # Verificar asignación legacy (assigned_to_id)
      return true if client.assigned_to_id == current_user.id

      # Verificar asignación múltiple (ClientAssignment)
      return true if current_user.client_assigned?(client)

      # Verificar si el cliente pertenece a alguna de las organizaciones del usuario
      client_orgs = client.organizations.pluck(:id)
      user_orgs = current_user.organizations.pluck(:id)
      return true if client_orgs.intersect?(user_orgs)
    end

    false
  end

  # Verifica si el usuario actual puede gestionar clientes
  # @return [Boolean] true si puede gestionar clientes, false en caso contrario
  def can_manage_clients?
    # Superadmin, support y owner pueden gestionar clientes
    return true if current_user.superadmin? || current_user.support? || current_user.owner?

    # Colaboradores con permiso edit_client_data pueden gestionar clientes
    return true if current_user.collaborator? && current_user.permission?('edit_client_data')

    false
  end

  # Verifica si el usuario actual puede gestionar los permisos del usuario objetivo
  # @param target_user [User] El usuario cuyos permisos se intentan gestionar
  # @return [Boolean] true si puede gestionar los permisos, false en caso contrario
  def can_manage_permissions?(target_user)
    return true if current_user.superadmin?
    return true if current_user.support? && !target_user.superadmin? && !target_user.support?

    # Un owner solo puede gestionar los permisos de sus colaboradores
    return target_user.collaborator? && target_user.admin_id == current_user.id if current_user.owner?

    false
  end
end
