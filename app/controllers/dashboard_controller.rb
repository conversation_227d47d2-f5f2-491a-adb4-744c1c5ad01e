class DashboardController < ApplicationController
  # Asegurarse de que el usuario esté autenticado
  before_action :require_authentication
  
  def show
    case current_user.role
    when 'owner'
      @organizations = current_user.organizations
      redirect_to owner_organizations_path
    when 'collaborator'
      redirect_to collaborator_root_path
    when 'superadmin'
      redirect_to superadmin_root_path
    when 'client'
      redirect_to client_profile_path
    else
      # Redirigir a la raíz si el rol no está definido
      redirect_to root_path, alert: 'Rol de usuario no válido.'
    end
  end

  private

  def require_authentication
    unless current_user
      redirect_to new_main_session_path, alert: 'Por favor inicia sesión para continuar.'
    end
  end
end
