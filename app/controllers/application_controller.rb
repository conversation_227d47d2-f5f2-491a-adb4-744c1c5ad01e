# frozen_string_literal: true

# Controlador base de la aplicación
# Contiene métodos comunes utilizados por todos los controladores
# Gestiona la autenticación, control de acceso, impersonación y gestión de organizaciones
#
# <AUTHOR>
# @since 1.0.0
class ApplicationController < ActionController::Base
  include PermissionVerification

  # Manejo de excepciones global
  rescue_from ActiveRecord::RecordNotFound, with: :handle_record_not_found
  rescue_from ActionController::InvalidAuthenticityToken, with: :handle_invalid_authenticity_token

  # Filtros de control de acceso por rol

  # Verifica que el usuario actual tenga rol de superadmin
  # Redirige a la página principal si no tiene los permisos necesarios
  # @return [nil]
  def require_superadmin
    return if current_user&.superadmin?

    flash[:alert] = "No tienes permisos para acceder a esta sección."
    redirect_to root_path
  end

  # Verifica que el usuario actual tenga rol de superadmin o soporte
  # Redirige a la página principal si no tiene los permisos necesarios
  # @return [nil]
  def require_soporte
    return if current_user&.superadmin? || current_user&.soporte?

    flash[:alert] = "No tienes permisos para acceder a esta sección."
    redirect_to root_path
  end

  # Verifica que el usuario actual tenga rol de superadmin, soporte o contador
  # Redirige a la página principal si no tiene los permisos necesarios
  # @return [nil]
  def require_contador
    return if current_user&.superadmin? || current_user&.soporte? || current_user&.contador?

    flash[:alert] = "No tienes permisos para acceder a esta sección."
    redirect_to root_path
  end

  # Verifica que el usuario haya iniciado sesión
  # Redirige a la página de inicio de sesión si no ha iniciado sesión
  # @return [nil]
  def require_authentication
    return if current_user

    flash[:alert] = "Necesitas iniciar sesión para acceder a esta sección."
    redirect_to new_main_session_path
  end

  # Verifica que el usuario sea un colaborador
  # Redirige al dashboard si no tiene el rol adecuado
  # @return [nil]
  def require_collaborator
    return if current_user&.collaborator?

    flash[:alert] = "No tienes permisos para acceder a esta sección."
    redirect_to dashboard_path
  end
  
  # @deprecated Use {#require_authentication} instead
  def require_cliente
    require_authentication
  end

  private

  def current_user
    Current.user ||= authenticate_user_from_session
  end
  helper_method :current_user

  # Obtiene la organización activa del usuario actual
  # Respeta el soft delete de organizaciones
  def current_organization
    return nil unless current_user

    # Si hay una organización activa en la sesión, la usamos
    if session[:active_organization_id].present?
      # Usamos current_user.organizations que ya respeta el scope de soft delete
      org = current_user.organizations.find_by(id: session[:active_organization_id])
      return org if org.present?
    end

    # Si no hay una organización activa o no es válida, usamos la primera
    # que no esté eliminada (soft deleted)
    org = current_user.organizations.first

    # Si encontramos una organización, la establecemos como activa
    set_active_organization(org) if org.present?

    org
  end
  helper_method :current_organization

  # Establece la organización activa
  def active_organization=(organization)
    return unless current_user&.organizations&.include?(organization)

    session[:active_organization_id] = organization.id
  end

  # Método auxiliar para establecer la organización activa
  def set_active_organization(organization)
    self.active_organization = organization
  end

  def authenticate_user_from_session
    User.find_by(id: session[:user_id])
  end

  # Obtiene el usuario original cuando se está haciendo impersonación
  def original_user
    @original_user ||= User.find_by(id: session[:original_user_id]) if session[:original_user_id]
  end
  helper_method :original_user

  # Verifica si se está haciendo impersonación
  def impersonating?
    session[:original_user_id].present?
  end
  helper_method :impersonating?

  def user_signed_in?
    current_user.present?
  end
  helper_method :user_signed_in?

  def logged_in?
    user_signed_in? # Puedes usar el método `user_signed_in?` aquí
  end
  helper_method :logged_in?

  def login(user)
    Current.user = user
    reset_session
    session[:user_id] = user.id

    # Establecer la primera organización como activa si el usuario tiene organizaciones
    return unless user.organizations.any?

    set_active_organization(user.organizations.first)
  end

  # Cierra la sesión del usuario actual
  def logout(_user = nil)
    Current.user = nil
    reset_session
  end

  # Inicia la impersonación de un usuario
  def impersonate(user)
    # Guarda el ID del usuario original
    session[:original_user_id] = current_user.id

    # Cambia el usuario actual por el impersonado
    session[:user_id] = user.id
    Current.user = user
  end

  # Detiene la impersonación y vuelve al usuario original
  def stop_impersonating
    return unless impersonating?

    original = original_user

    # Guarda el ID del usuario original
    original_id = original.id

    # Limpia la sesión
    Current.user = nil
    reset_session

    # Restaura el usuario original
    session[:user_id] = original_id
    Current.user = original
  end

  protected

  # Maneja el error cuando no se encuentra un registro
  # @param exception [ActiveRecord::RecordNotFound] la excepción capturada
  # @return [void]
  def handle_record_not_found(exception)
    Rails.logger.error "Record not found: #{exception.message}"
    respond_to do |format|
      format.html do
        flash[:alert] = "No se encontró el recurso solicitado."
        redirect_to root_path
      end
      format.json { render json: { error: "Recurso no encontrado" }, status: :not_found }
    end
  end

  # Maneja el error cuando el token CSRF no es válido
  # @param exception [ActionController::InvalidAuthenticityToken] la excepción capturada
  # @return [void]
  def handle_invalid_authenticity_token(exception)
    Rails.logger.error "Invalid authenticity token: #{exception.message}"
    respond_to do |format|
      format.html do
        flash[:alert] = "Tu sesión ha expirado. Por favor, vuelve a iniciar sesión."
        logout
        redirect_to root_path
      end
      format.json { render json: { error: "Sesión expirada" }, status: :unprocessable_entity }
    end
  end
end
