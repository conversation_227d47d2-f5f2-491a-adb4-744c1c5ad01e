# frozen_string_literal: true

module Collaborator
  # Controlador para gestionar clientes
  # Permite crear, editar, ver y eliminar clientes, así como
  # validar RUTs a través de una API externa
  class ClientsController < ApplicationController
    include PermissionVerification

    # Verificar autenticación y rol de colaborador
    before_action :require_authentication
    before_action :require_collaborator
    before_action :set_client, only: [:show, :edit, :update, :destroy, :toggle_access, :send_invitation]
    before_action :check_permissions, except: [:index, :show]

  # GET /clients
  def index
    # Obtener la organización activa
    @organization = current_organization

    # Verificar que exista una organización activa
    unless @organization
      redirect_to organizations_path, alert: "Debes seleccionar una organización primero."
      return
    end

    # Obtener los clientes según el rol y permisos del usuario
    @clients = if current_user.admin_of?(@organization) || current_user.permission?('view_all_clients')
                 # Administradores y usuarios con permiso ven todos los clientes
                 @organization.clients
               elsif current_user.collaborator_of?(@organization)
                 # Colaboradores ven solo sus clientes asignados
                 User.where(assigned_to_id: current_user.id)
               else
                 # Otros usuarios no ven clientes
                 User.none
               end
  end

  # GET /clients/new
  def new
    # Formulario para crear cliente con datos básicos y opción de datos fiscales
  end

  # PATCH /clients/:id/set_active
  def set_active
    @client = User.find(params[:id])
    @organization = current_organization
    org_user = OrganizationUser.find_by(user: @client, organization: @organization, role: :client)
    
    if org_user
      # Cambiar el estado de acceso
      if org_user.access_enabled?
        org_user.disable_access!
        message = "Cliente desmarcado como activo"
        active = false
      else
        org_user.enable_access!
        message = "Cliente marcado como activo"
        active = true
      end
      
      respond_to do |format|
        format.json { render json: { status: 'success', message: message, active: active } }
      end
    else
      respond_to do |format|
        format.json { render json: { status: 'error', message: 'No se encontró la relación del cliente con la organización' }, status: :not_found }
      end
    end
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { status: 'error', message: 'Cliente no encontrado' }, status: :not_found }
    end
  end

  # POST /clients/validate_rut
  def validate_rut
    # Obtener el RUT del formulario
    raw_rut = params[:rut].to_s
    Rails.logger.info("RUT recibido del formulario: #{raw_rut}")

    # Normalizar el RUT (eliminar caracteres no numéricos)
    @rut = RutValidatorService.normalize(raw_rut)
    Rails.logger.info("RUT normalizado: #{@rut}")

    # Mostrar el RUT formateado para visualización (solo para logs)
    formatted_rut = RutValidatorService.format(@rut, true)
    Rails.logger.info("RUT formateado para visualización: #{formatted_rut}")

    # Primero, validar el formato del RUT
    unless RutValidatorService.valid?(@rut)
      Rails.logger.warn("RUT inválido: #{@rut} - No cumple con el formato requerido")
      flash[:alert] = "El formato del RUT no es válido. Debe tener 12 dígitos numéricos y seguir el formato uruguayo."
      redirect_to new_client_path
      return
    end

    Rails.logger.info("RUT válido: #{@rut} - Consultando API externa")

    begin
      # Si el RUT es válido, consultar la API externa para obtener datos fiscales
      # Usamos el RUT normalizado (sin puntos ni barras) para la consulta
      @client_data = RutApiService.fetch_fiscal_data(@rut)

      # Registrar la respuesta para depuración
      Rails.logger.debug("Respuesta de la API: #{@client_data.inspect}")

      if @client_data[:success]
        Rails.logger.info("Datos fiscales obtenidos correctamente para RUT: #{@rut}")
        Rails.logger.info("Nombre: #{@client_data[:name]}, Dirección: #{@client_data[:address]}, Teléfono: #{@client_data[:phone]}, Email: #{@client_data[:email]}")

        # Guardar los datos en la sesión para usarlos en la acción new_with_data
        # Asegurarse de que todos los valores sean strings para evitar problemas de serialización
        session_data = {
          rut: @rut.to_s, # Guardamos el RUT normalizado (sin puntos ni barras)
          name: @client_data[:nombre_legal].to_s.presence || @client_data[:name].to_s,
          nombre_fantasia: @client_data[:nombre_fantasia].to_s,
          nombre_legal: @client_data[:nombre_legal].to_s,
          tipo_entidad: @client_data[:tipo_entidad].to_s,
          address: @client_data[:address].to_s,
          phone: @client_data[:phone].to_s,
          email: @client_data[:email].to_s
        }

        # Guardar también los datos raw y otros datos complejos si están disponibles
        session_data[:raw_data] = @client_data[:raw_data] if @client_data[:raw_data].present?
        session_data[:domicilio] = @client_data[:domicilio] if @client_data[:domicilio].present?
        session_data[:actividades] = @client_data[:actividades] if @client_data[:actividades].present?

        # Guardar en la sesión
        session[:client_data] = session_data

        # Verificar que los datos se hayan guardado correctamente
        Rails.logger.info("Verificando datos guardados en la sesión:")
        Rails.logger.info("RUT: #{session[:client_data][:rut].inspect}")
        Rails.logger.info("Nombre: #{session[:client_data][:name].inspect}")
        Rails.logger.info("Dirección: #{session[:client_data][:address].inspect}")
        Rails.logger.info("Teléfono: #{session[:client_data][:phone].inspect}")
        Rails.logger.info("Email: #{session[:client_data][:email].inspect}")

        # Registrar los datos guardados en la sesión para depuración
        Rails.logger.debug("Datos guardados en la sesión: #{session[:client_data].inspect}")

        # Redirigir al formulario de creación de cliente
        redirect_to new_collaborator_client_path, notice: "Datos fiscales obtenidos correctamente. Puedes crear un cliente con estos datos."
      else
        Rails.logger.error("Error al obtener datos fiscales para RUT: #{@rut} - #{@client_data[:error]}")
        flash[:alert] = "No se pudieron obtener los datos fiscales para el RUT #{formatted_rut}. Error: #{@client_data[:error]}"
        redirect_to new_client_path
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en validate_rut: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      flash[:alert] = "Ocurrió un error inesperado al validar el RUT. Por favor, inténtelo de nuevo."
      redirect_to new_client_path
    end
  end

  # POST /clients/validate_rut_ajax
  def validate_rut_ajax
    # Obtener el RUT del formulario
    raw_rut = params[:rut].to_s
    Rails.logger.info("RUT recibido del formulario AJAX: #{raw_rut}")

    # Normalizar el RUT (eliminar caracteres no numéricos)
    @rut = RutValidatorService.normalize(raw_rut)
    Rails.logger.info("RUT normalizado: #{@rut}")

    # Primero, validar el formato del RUT
    unless RutValidatorService.valid?(@rut)
      Rails.logger.warn("RUT inválido: #{@rut} - No cumple con el formato requerido")
      render json: { success: false, error: "El formato del RUT no es válido. Debe tener 12 dígitos numéricos y seguir el formato uruguayo." }
      return
    end

    Rails.logger.info("RUT válido: #{@rut} - Consultando API externa")

    begin
      # Si el RUT es válido, consultar la API externa para obtener datos fiscales
      @client_data = RutApiService.fetch_fiscal_data(@rut)

      # Registrar la respuesta para depuración
      Rails.logger.debug("Respuesta de la API: #{@client_data.inspect}")

      if @client_data[:success]
        Rails.logger.info("Datos fiscales obtenidos correctamente para RUT: #{@rut}")

        # Devolver los datos en formato JSON
        render json: {
          success: true,
          rut: @rut,
          name: @client_data[:nombre_legal].presence || @client_data[:name],
          nombre_fantasia: @client_data[:nombre_fantasia],
          nombre_legal: @client_data[:nombre_legal],
          tipo_entidad: @client_data[:tipo_entidad],
          address: @client_data[:address],
          phone: @client_data[:phone],
          email: @client_data[:email]
        }
      else
        Rails.logger.error("Error al obtener datos fiscales para RUT: #{@rut} - #{@client_data[:error]}")
        render json: { success: false, error: @client_data[:error] }
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en validate_rut_ajax: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      render json: { success: false, error: "Ocurrió un error inesperado al validar el RUT." }
    end
  end



  # POST /clients/create_basic
  def create_basic
    begin
      # Obtener la organización activa
      @current_organization = current_organization

      unless @current_organization
        redirect_to organizations_path, alert: "Debes seleccionar una organización primero."
        return
      end

      # Verificar si ya existe un usuario con ese email usando nuestro método helper
      existing_client = find_existing_client(params[:email])
      if existing_client
        # Si el cliente ya existe, verificar si ya pertenece a la organización actual
        if existing_client.client_of?(@current_organization)
          redirect_to client_path(existing_client), notice: "Este cliente ya pertenece a tu organización."
        else
          # Si el cliente existe pero no pertenece a la organización actual, agregarlo
          access_enabled = @current_organization.client_access_enabled
          create_or_update_client_organization_relation(existing_client, @current_organization, access_enabled)
          redirect_to client_path(existing_client), notice: "Cliente existente agregado a tu organización."
        end
        return
      end

      # Verificar si existe un usuario con ese email que no es cliente
      if params[:email].present? && User.find_by(email: params[:email]).present?
        redirect_to new_collaborator_client_path, alert: "Ya existe un usuario con este email que no es un cliente."
        return
      end

      # Verificar si se han obtenido datos fiscales
      has_fiscal_data = params[:has_fiscal_data] == "true"
      Rails.logger.info("Creando cliente #{has_fiscal_data ? 'con' : 'sin'} datos fiscales")

      # Verificar si hay un RUT proporcionado
      has_rut = params[:fiscal_rut].present?
      Rails.logger.info("RUT proporcionado: #{has_rut ? params[:fiscal_rut] : 'No'}")

      # Crear la organización cliente con datos básicos
      org_params = {
        name: params[:organization_name],
        direccion: params[:organization_address]
      }

      # Asegurarse de que el RUT sea nil por defecto
      org_params[:rut] = nil

      # Agregar datos fiscales solo si has_fiscal_data es true y hay un RUT proporcionado
      if has_fiscal_data && has_rut
        Rails.logger.info("Agregando datos fiscales con RUT: #{params[:fiscal_rut]}")
        org_params[:rut] = params[:fiscal_rut]
        org_params[:nombre_fantasia] = params[:fiscal_nombre_fantasia]
        org_params[:tipo_entidad] = params[:fiscal_tipo_entidad]
        org_params[:direccion] = params[:fiscal_direccion] if params[:fiscal_direccion].present?
        org_params[:telefono] = params[:fiscal_telefono]
        org_params[:email_facturacion] = params[:fiscal_email]
      else
        # Asegurarse de que el RUT sea nil cuando no se proporcionan datos fiscales
        Rails.logger.info("Creando cliente sin datos fiscales, RUT será nil")
      end

      ActiveRecord::Base.transaction do
        Rails.logger.info("Iniciando transacción para crear cliente")

        # Crear la organización cliente
        @organization = Organization.new(org_params)
        Rails.logger.info("Organización a crear: #{@organization.inspect}")

        # Crear el usuario cliente
        @user = User.new(
          email: params[:email],
          name: params[:name],
          last_name: params[:last_name],
          role: :client,
          password: @current_organization.client_access_enabled ? SecureRandom.hex(8) : nil # Generar contraseña solo si el acceso está habilitado
        )
        Rails.logger.info("Usuario a crear: #{@user.inspect}")

        # Asignar el usuario al miembro actual si es un miembro o al miembro seleccionado
        if params[:assigned_to_id].present?
          @user.assigned_to_id = params[:assigned_to_id]
          Rails.logger.info("Usuario asignado a miembro: #{@user.assigned_to_id}")
        elsif current_user.collaborator?
          @user.assigned_to = current_user
          Rails.logger.info("Usuario asignado a miembro actual: #{current_user.id}")
        else
          # Si no hay asignación específica y el usuario actual es admin o superadmin,
          # asignar al usuario actual como responsable
          @user.assigned_to = current_user
          Rails.logger.info("Usuario asignado al administrador actual: #{current_user.id}")
        end

        # Validar manualmente antes de guardar
        org_valid = @organization.valid?
        user_valid = @user.valid?

        unless org_valid && user_valid
          Rails.logger.warn("Errores de validación antes de guardar:")
          Rails.logger.warn("Organización: #{@organization.errors.full_messages}") unless org_valid
          Rails.logger.warn("Usuario: #{@user.errors.full_messages}") unless user_valid
          flash[:alert] = "Error al crear el cliente. Por favor, revise los datos e inténtelo de nuevo."
          redirect_to new_collaborator_client_path
          return
        end

        if @organization.save && @user.save
          Rails.logger.info("Organización y usuario guardados correctamente")

          # Crear la relación entre el usuario y la organización como cliente
          # Usamos nuestro método helper para evitar duplicados
          org_user1 = create_or_update_client_organization_relation(@user, @organization, false)
          Rails.logger.info("Relación usuario-organización creada: #{org_user1.inspect}")

          # Crear la relación entre el cliente y la organización actual
          # Determinar si el cliente tendrá acceso habilitado según la configuración de la organización
          access_enabled = @current_organization.client_access_enabled

          # Usamos nuestro método helper para evitar duplicados
          org_user2 = create_or_update_client_organization_relation(@user, @current_organization, access_enabled)
          Rails.logger.info("Relación cliente-organización actual creada: #{org_user2.inspect}")

          # Enviar invitación por email si el acceso está habilitado
          if access_enabled && @user.password.present?
            ClientInvitationMailer.initial_invitation(@user, @current_organization, @user.password).deliver_later
            Rails.logger.info("Invitación enviada al cliente: #{@user.email}")
          end

          if has_fiscal_data
            redirect_to collaborator_client_path(@user), notice: "Cliente creado correctamente con datos fiscales validados."
          else
            redirect_to collaborator_client_path(@user), notice: "Cliente básico creado correctamente. Puedes agregar los datos fiscales más tarde."
          end
        else
          Rails.logger.error("Error al guardar organización o usuario:")
          Rails.logger.error("Errores de organización: #{@organization.errors.full_messages}") if @organization.errors.any?
          Rails.logger.error("Errores de usuario: #{@user.errors.full_messages}") if @user.errors.any?
          flash[:alert] = "Error al crear el cliente. Por favor, revise los datos e inténtelo de nuevo."
          redirect_to new_collaborator_client_path
        end
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en create_basic: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      flash[:alert] = "Ocurrió un error inesperado al crear el cliente. Por favor, revise los datos e inténtelo de nuevo."
      redirect_to new_collaborator_client_path
    end
  end

  # POST /clients
  def create
    begin
      # Obtener la organización activa
      @current_organization = current_organization

      unless @current_organization
        redirect_to organizations_path, alert: "Debes seleccionar una organización primero."
        return
      end

      # Verificar si ya existe un usuario con ese email usando nuestro método helper
      if params[:user] && params[:user][:email].present?
        existing_client = find_existing_client(params[:user][:email])
        if existing_client
          # Si el cliente ya existe, verificar si ya pertenece a la organización actual
          if existing_client.client_of?(@current_organization)
            redirect_to client_path(existing_client), notice: "Este cliente ya pertenece a tu organización."
          else
            # Si el cliente existe pero no pertenece a la organización actual, agregarlo
            access_enabled = @current_organization.client_access_enabled
            create_or_update_client_organization_relation(existing_client, @current_organization, access_enabled)
            redirect_to client_path(existing_client), notice: "Cliente existente agregado a tu organización."
          end
          return
        end

        # Verificar si existe un usuario con ese email que no es cliente
        if User.find_by(email: params[:user][:email]).present?
          flash.now[:alert] = "Ya existe un usuario con este email que no es un cliente."
          render :create, status: :unprocessable_entity
          return
        end
      end

      # Normalizar el RUT en los parámetros solo si está presente
      if params[:organization] && params[:organization][:rut].present?
        params[:organization][:rut] = RutValidatorService.normalize(params[:organization][:rut])
        Rails.logger.info("RUT normalizado en parámetros: #{params[:organization][:rut]}")
      end

      # Obtener los parámetros
      org_params = organization_params
      usr_params = user_params

      # Asegurarse de que el RUT sea nil por defecto si no está presente
      org_params[:rut] = nil unless org_params[:rut].present?

      ActiveRecord::Base.transaction do
        Rails.logger.info("Iniciando transacción para crear cliente")

        # Crear la organización cliente
        @organization = Organization.new(org_params)
        Rails.logger.info("Organización a crear: #{@organization.inspect}")

        # Crear el usuario cliente
        @user = User.new(usr_params)
        @user.role = :client
        @user.password = @current_organization.client_access_enabled ? SecureRandom.hex(8) : nil # Generar contraseña solo si el acceso está habilitado
        Rails.logger.info("Usuario a crear: #{@user.inspect}")

        # Asignar el usuario al miembro actual si es un miembro o al miembro seleccionado
        if usr_params[:assigned_to_id].present?
          # Ya está asignado por los parámetros
          Rails.logger.info("Usuario asignado a miembro: #{@user.assigned_to_id}")
        elsif current_user.collaborator?
          @user.assigned_to = current_user
          Rails.logger.info("Usuario asignado a miembro actual: #{current_user.id}")
        else
          # Si no hay asignación específica y el usuario actual es admin o superadmin,
          # asignar al usuario actual como responsable
          @user.assigned_to = current_user
          Rails.logger.info("Usuario asignado al administrador actual: #{current_user.id}")
        end

        # Validar manualmente antes de guardar
        org_valid = @organization.valid?
        user_valid = @user.valid?

        unless org_valid && user_valid
          Rails.logger.warn("Errores de validación antes de guardar:")
          Rails.logger.warn("Organización: #{@organization.errors.full_messages}") unless org_valid
          Rails.logger.warn("Usuario: #{@user.errors.full_messages}") unless user_valid
          render :create, status: :unprocessable_entity
          return
        end

        if @organization.save && @user.save
          Rails.logger.info("Organización y usuario guardados correctamente")

          # Crear la relación entre el usuario y la organización como cliente
          # Usamos nuestro método helper para evitar duplicados
          org_user1 = create_or_update_client_organization_relation(@user, @organization, false)
          Rails.logger.info("Relación usuario-organización creada: #{org_user1.inspect}")

          # Crear la relación entre el cliente y la organización actual
          # Determinar si el cliente tendrá acceso habilitado según la configuración de la organización
          access_enabled = @current_organization.client_access_enabled

          # Usamos nuestro método helper para evitar duplicados
          org_user2 = create_or_update_client_organization_relation(@user, @current_organization, access_enabled)
          Rails.logger.info("Relación cliente-organización actual creada: #{org_user2.inspect}")

          # Enviar invitación por email si el acceso está habilitado
          if access_enabled && @user.password.present?
            ClientInvitationMailer.initial_invitation(@user, @current_organization, @user.password).deliver_later
            Rails.logger.info("Invitación enviada al cliente: #{@user.email}")
          end

          redirect_to collaborator_clients_path, notice: "Cliente creado correctamente."
        else
          Rails.logger.error("Error al guardar organización o usuario:")
          Rails.logger.error("Errores de organización: #{@organization.errors.full_messages}") if @organization.errors.any?
          Rails.logger.error("Errores de usuario: #{@user.errors.full_messages}") if @user.errors.any?
          render :create, status: :unprocessable_entity
        end
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en create: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      flash.now[:alert] = "Ocurrió un error inesperado al crear el cliente. Por favor, revise los datos e inténtelo de nuevo."
      render :create, status: :unprocessable_entity
    end
  end

  # GET /clients/1
  def show
    # Verificar que el usuario tenga permisos para ver este cliente
    unless can_view_client?(@client)
      redirect_to collaborator_clients_path, alert: "No tienes permisos para ver este cliente."
      return
    end

    # Redirigir a la página de edición con la pestaña de información general
    redirect_to edit_collaborator_client_path(@client, section: 'general')
  end

  # GET /clients/1/edit
  def edit
    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para editar este cliente."
      return
    end

    # Obtener la organización del cliente usando el método helper
    @client_organization = find_or_create_client_organization(@client)

    # Por defecto, mostrar la sección general
    @section = params[:section] || 'general'

    # Validar que la sección sea válida
    valid_sections = ['general', 'fiscal', 'billing', 'members', 'access']
    @section = 'general' unless valid_sections.include?(@section)
  end

  # PATCH/PUT /clients/1
  def update
    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para editar este cliente."
      return
    end

    # Obtener la organización del cliente usando el método helper
    @client_organization = find_or_create_client_organization(@client)

    # Obtener la sección actual
    section = params[:section] || 'general'

    # Obtener los miembros seleccionados para asignación múltiple
    member_ids = params[:user][:member_ids].reject(&:blank?) if params[:user] && params[:user][:member_ids]

    # Registrar los parámetros recibidos para depuración
    Rails.logger.debug("Parámetros recibidos: #{params.inspect}")
    Rails.logger.debug("Parámetros de usuario: #{user_params.inspect}")
    Rails.logger.debug("Parámetros de organización: #{organization_params.inspect}") if params[:organization]
    Rails.logger.debug("Organización del cliente: #{@client_organization.inspect}")

    ActiveRecord::Base.transaction do
      # Actualizar datos básicos del cliente
      client_updated = @client.update(user_params)

      # Actualizar datos de la organización si se proporcionaron
      organization_updated = true
      if params[:organization]
        # Asegurarnos de que estamos actualizando la organización correcta
        organization_updated = @client_organization.update(organization_params)

        # Guardar la organización en la sesión para futuras referencias
        session[:client_organization_id] = @client_organization.id

        # Registrar la actualización para depuración
        Rails.logger.debug("Organización actualizada: #{@client_organization.inspect}")
      end

      # Procesar asignaciones múltiples si se proporcionaron member_ids
      if member_ids.present?
        # Obtener la organización activa
        organization = current_organization

        # Verificar que exista una organización activa
        if organization
          # Obtener los miembros válidos (que pertenecen a la organización actual)
          valid_member_ids = organization.members.where(id: member_ids).pluck(:id)

          # Si hay miembros válidos seleccionados
          if valid_member_ids.any?
            # Eliminar asignaciones existentes para este cliente, pero solo de miembros de la organización actual
            # Esto preserva asignaciones de miembros de otras organizaciones si existen
            current_org_member_ids = organization.members.pluck(:id)
            @client.client_assignments.where(member_id: current_org_member_ids).destroy_all

            # Crear nuevas asignaciones para cada miembro seleccionado
            valid_member_ids.each do |member_id|
              @client.client_assignments.create(member_id: member_id)
            end
          end
        end
      elsif params[:user] && params[:user][:member_ids].nil? && section == 'members'
        # Si estamos en la sección de miembros y no se proporcionaron member_ids, eliminar todas las asignaciones
        if current_organization
          current_org_member_ids = current_organization.members.pluck(:id)
          @client.client_assignments.where(member_id: current_org_member_ids).destroy_all
        end
      end

      if client_updated && organization_updated
        redirect_to edit_collaborator_client_path(@client, section: section), notice: "Cliente actualizado correctamente."
      else
        @section = section
        # Registrar errores para depuración
        Rails.logger.error("Errores del cliente: #{@client.errors.full_messages}") unless client_updated
        Rails.logger.error("Errores de la organización: #{@client_organization.errors.full_messages}") unless organization_updated
        render :edit, status: :unprocessable_entity
      end
    end
  end

  # DELETE /clients/1
  def destroy
    # Verificar que el usuario tenga permisos para eliminar este cliente
    unless can_edit_client?(@client)
      redirect_to collaborator_clients_path, alert: "No tienes permisos para eliminar este cliente."
      return
    end

    @client.destroy
    redirect_to collaborator_clients_path, notice: "Cliente eliminado correctamente."
  end

  # POST /clients/:id/toggle_access
  def toggle_access
    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para modificar el acceso de este cliente."
      return
    end

    # Obtener la organización activa
    @organization = current_organization

    # Verificar que exista una organización activa
    unless @organization
      redirect_to organizations_path, alert: "Debes seleccionar una organización primero."
      return
    end

    # Verificar que la organización tenga habilitado el acceso de clientes
    unless @organization.client_access_enabled
      redirect_to collaborator_clients_path, alert: "Esta organización no tiene habilitado el acceso de clientes."
      return
    end

    # Obtener la relación entre el cliente y la organización
    org_user = OrganizationUser.find_by(user: @client, organization: @organization, role: :client)

    unless org_user
      redirect_to clients_path, alert: "El cliente no pertenece a esta organización."
      return
    end

    # Cambiar el estado de acceso
    if org_user.access_enabled
      # Deshabilitar acceso
      if org_user.update(access_enabled: false)
        # Enviar notificación por email
        ClientInvitationMailer.access_disabled(@client, @organization).deliver_later
        redirect_to collaborator_client_path(@client), notice: "Acceso del cliente deshabilitado correctamente."
      else
        redirect_to collaborator_client_path(@client), alert: "No se pudo deshabilitar el acceso del cliente."
      end
    else
      # Habilitar acceso
      # Generar contraseña aleatoria
      password = SecureRandom.hex(8)

      # Actualizar la contraseña del usuario
      if @client.update(password: password) && org_user.update(access_enabled: true, invitation_sent_at: Time.current)
        # Enviar notificación por email
        ClientInvitationMailer.access_enabled(@client, @organization, password).deliver_later
        redirect_to collaborator_client_path(@client), notice: "Acceso del cliente habilitado correctamente. Se ha enviado un email con las instrucciones."
      else
        redirect_to collaborator_client_path(@client), alert: "No se pudo habilitar el acceso del cliente."
      end
    end
  end

  # POST /clients/:id/send_invitation
  def send_invitation
    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para enviar invitaciones a este cliente."
      return
    end

    # Obtener la organización activa
    @organization = current_organization

    # Verificar que exista una organización activa
    unless @organization
      redirect_to organizations_path, alert: "Debes seleccionar una organización primero."
      return
    end

    # Obtener la relación entre el cliente y la organización
    org_user = OrganizationUser.find_by(user: @client, organization: @organization, role: :client)

    unless org_user
      redirect_to clients_path, alert: "El cliente no pertenece a esta organización."
      return
    end

    # Verificar que el cliente tenga acceso habilitado
    unless org_user.access_enabled
      redirect_to collaborator_client_path(@client), alert: "El cliente no tiene acceso habilitado. Habilita su acceso primero."
      return
    end

    # Generar contraseña aleatoria
    password = SecureRandom.hex(8)

    # Actualizar la contraseña del usuario
    if @client.update(password: password) && org_user.update(invitation_sent_at: Time.current)
      # Enviar notificación por email
      ClientInvitationMailer.resend_invitation(@client, @organization, password).deliver_later
      redirect_to collaborator_client_path(@client), notice: "Invitación enviada correctamente al cliente."
    else
      redirect_to collaborator_client_path(@client), alert: "No se pudo enviar la invitación al cliente."
    end
  end

  # GET /clients/validate_fiscal_data/:id
  def validate_fiscal_data
    # Obtener el cliente
    @client = User.find(params[:id])

    # Verificar que el usuario sea un cliente
    unless @client.client?
      redirect_to clients_path, alert: "El usuario seleccionado no es un cliente."
      return
    end

    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para editar este cliente."
      return
    end

    # Obtener la organización del cliente usando el método helper
    @client_organization = find_or_create_client_organization(@client)
  end

  # POST /clients/update_fiscal_data/:id
  def update_fiscal_data
    # Obtener el cliente
    @client = User.find(params[:id])

    # Verificar que el usuario sea un cliente
    unless @client.client?
      redirect_to clients_path, alert: "El usuario seleccionado no es un cliente."
      return
    end

    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para editar este cliente."
      return
    end

    # Obtener la organización del cliente usando el método helper
    @client_organization = find_or_create_client_organization(@client)

    # Obtener el RUT del formulario
    raw_rut = params[:rut].to_s
    Rails.logger.info("RUT recibido del formulario: #{raw_rut}")

    # Normalizar el RUT (eliminar caracteres no numéricos)
    @rut = RutValidatorService.normalize(raw_rut)
    Rails.logger.info("RUT normalizado: #{@rut}")

    # Primero, validar el formato del RUT
    unless RutValidatorService.valid?(@rut)
      Rails.logger.warn("RUT inválido: #{@rut} - No cumple con el formato requerido")
      flash[:alert] = "El formato del RUT no es válido. Debe tener 12 dígitos numéricos y seguir el formato uruguayo."
      redirect_to validate_fiscal_data_clients_path(@client)
      return
    end

    Rails.logger.info("RUT válido: #{@rut} - Consultando API externa")

    begin
      # Si el RUT es válido, consultar la API externa para obtener datos fiscales
      @client_data = RutApiService.fetch_fiscal_data(@rut)

      # Registrar la respuesta para depuración
      Rails.logger.debug("Respuesta de la API: #{@client_data.inspect}")

      if @client_data[:success]
        Rails.logger.info("Datos fiscales obtenidos correctamente para RUT: #{@rut}")
        
        # Preparar los datos para mostrar en el formulario
        @fiscal_data = {
          rut: @rut,
          nombre_legal: @client_data[:nombre_legal].to_s.presence || "",
          nombre_fantasia: @client_data[:nombre_fantasia].to_s.presence || "",
          tipo_entidad: @client_data[:tipo_entidad].to_s.presence || "",
          direccion: @client_data[:address].to_s.presence || "",
          telefono: @client_data[:phone].to_s.presence || "",
          email: @client_data[:email].to_s.presence || ""
        }
        
        # Mostrar el formulario con los datos obtenidos
        render :validate_fiscal_data
      else
        Rails.logger.error("Error al obtener datos fiscales para RUT: #{@rut} - #{@client_data[:error]}")
        flash[:alert] = "No se pudieron obtener los datos fiscales para el RUT #{@rut}. Error: #{@client_data[:error]}"
        redirect_to validate_fiscal_data_clients_path(@client)
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en update_fiscal_data: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      flash[:alert] = "Ocurrió un error inesperado al validar el RUT. Por favor, inténtelo de nuevo."
      redirect_to validate_fiscal_data_clients_path(@client)
    end
  end

  # POST /clients/fetch_fiscal_data
  # Endpoint para obtener datos fiscales mediante AJAX
  def fetch_fiscal_data
    # Obtener el RUT del formulario
    raw_rut = params[:rut].to_s
    Rails.logger.info("RUT recibido para fetch_fiscal_data: #{raw_rut}")

    # Normalizar el RUT (eliminar caracteres no numéricos)
    @rut = RutValidatorService.normalize(raw_rut)
    Rails.logger.info("RUT normalizado: #{@rut}")

    # Primero, validar el formato del RUT
    unless RutValidatorService.valid?(@rut)
      Rails.logger.warn("RUT inválido: #{@rut} - No cumple con el formato requerido")
      render json: { success: false, error: "El formato del RUT no es válido. Debe tener 12 dígitos numéricos y seguir el formato uruguayo." }, status: :unprocessable_entity
      return
    end

    Rails.logger.info("RUT válido: #{@rut} - Consultando API externa")

    begin
      # Si el RUT es válido, consultar la API externa para obtener datos fiscales
      @client_data = RutApiService.fetch_fiscal_data(@rut)

      # Registrar la respuesta para depuración
      Rails.logger.debug("Respuesta de la API: #{@client_data.inspect}")

      if @client_data[:success]
        Rails.logger.info("Datos fiscales obtenidos correctamente para RUT: #{@rut}")

        # Devolver los datos fiscales en formato JSON
        render json: {
          success: true,
          data: {
            rut: @rut,
            nombre_fantasia: @client_data[:nombre_fantasia].to_s.presence || "",
            nombre_legal: @client_data[:nombre_legal].to_s.presence || "",
            tipo_entidad: @client_data[:tipo_entidad].to_s.presence || "",
            direccion: @client_data[:address].to_s.presence || "",
            telefono: @client_data[:phone].to_s.presence || "",
            email: @client_data[:email].to_s.presence || ""
          }
        }
      else
        Rails.logger.error("Error al obtener datos fiscales para RUT: #{@rut} - #{@client_data[:error]}")
        render json: { success: false, error: "No se pudieron obtener los datos fiscales para el RUT #{@rut}. Error: #{@client_data[:error]}" }, status: :unprocessable_entity
      end
    rescue => e
      # Capturar cualquier excepción no manejada
      Rails.logger.error("Excepción no manejada en fetch_fiscal_data: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      render json: { success: false, error: "Ocurrió un error inesperado al validar el RUT. Por favor, inténtelo de nuevo." }, status: :internal_server_error
    end
  end

  # Hacer que estos métodos estén disponibles en las vistas
  helper_method :can_view_client?, :can_edit_client?, :can_manage_clients?

  # POST /clients/1/toggle_access
  def toggle_access
    @client = User.find(params[:id])

    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para modificar el acceso de este cliente."
      return
    end

    organization = current_organization

    if organization&.client_access_enabled
      org_user = OrganizationUser.find_by(user: @client, organization: organization, role: :client)

      if org_user
        # Cambiar el estado de acceso
        org_user.update(access_enabled: !org_user.access_enabled)

        if org_user.access_enabled
          # Si se habilitó el acceso, enviar invitación
          @client.invite!(current_user)
          redirect_to edit_collaborator_client_path(@client, section: 'access'), notice: "Acceso habilitado y invitación enviada correctamente."
        else
          redirect_to edit_collaborator_client_path(@client, section: 'access'), notice: "Acceso deshabilitado correctamente."
        end
      else
        redirect_to edit_collaborator_client_path(@client, section: 'access'), alert: "No se pudo encontrar la relación entre el cliente y la organización."
      end
    else
      redirect_to edit_client_path(@client, section: 'access'), alert: "El acceso de clientes no está habilitado para esta organización."
    end
  end

  # POST /clients/1/send_invitation
  def send_invitation
    @client = User.find(params[:id])

    # Verificar que el usuario tenga permisos para editar este cliente
    unless can_edit_client?(@client)
      redirect_to clients_path, alert: "No tienes permisos para enviar invitaciones a este cliente."
      return
    end

    organization = current_organization

    if organization&.client_access_enabled
      org_user = OrganizationUser.find_by(user: @client, organization: organization, role: :client)

      if org_user&.access_enabled
        # Enviar invitación
        @client.invite!(current_user)
        redirect_to edit_collaborator_client_path(@client, section: 'access'), notice: "Invitación enviada correctamente."
      else
        redirect_to edit_collaborator_client_path(@client, section: 'access'), alert: "El cliente no tiene acceso habilitado."
      end
    else
      redirect_to edit_client_path(@client, section: 'access'), alert: "El acceso de clientes no está habilitado para esta organización."
    end
  end

  # PATCH /clients/:id/update_organization_fiscal_data
  def update_organization_fiscal_data
    @client = Client.find(params[:id])
    @client_organization = @client.organization
    
    # Actualizar los campos de la organización con los datos del formulario
    if @client_organization.update(organization_params)
      Rails.logger.info("Datos fiscales actualizados correctamente para la organización: #{@client_organization.id}")
      redirect_to collaborator_client_path(@client), 
                  notice: "Datos fiscales actualizados correctamente."
    else
      Rails.logger.error("Error al actualizar los datos fiscales: #{@client_organization.errors.full_messages}")
      @fiscal_data = {
        rut: @client_organization.rut,
        nombre_legal: @client_organization.nombre_legal,
        nombre_fantasia: @client_organization.nombre_fantasia,
        tipo_entidad: @client_organization.tipo_entidad,
        direccion: @client_organization.direccion,
        telefono: @client_organization.telefono,
        email: @client_organization.email_facturacion
      }
      
      flash.now[:alert] = "Error al guardar los datos fiscales: #{@client_organization.errors.full_messages.join(', ')}"
      render :validate_fiscal_data
    end
  end

  private

  # Método para encontrar o crear la organización de un cliente
  # @param client [User] El cliente para el que se busca o crea la organización
  # @return [Organization] La organización del cliente
  def find_or_create_client_organization(client)
    # Primero, intentamos encontrar la organización en la sesión
    if session[:client_organization_id].present?
      client_org = Organization.find_by(id: session[:client_organization_id])
      if client_org && client_org.organization_users.exists?(user_id: client.id, role: :client)
        Rails.logger.info("Usando organización de la sesión: #{client_org.inspect}")
        return client_org
      end
    end

    # Buscar la organización propia del cliente (la que se creó para él)
    # Primero intentamos encontrar la organización que tiene un nombre relacionado con el email del cliente
    client_org = client.organizations.joins(:organization_users)
                      .where(organization_users: { user_id: client.id, role: :client })
                      .where.not(rut: '217090160018')
                      .order(updated_at: :desc)
                      .first

    # Si no se encuentra, buscar cualquier organización donde el cliente es cliente y no tiene el RUT problemático
    client_org ||= client.client_organizations.where.not(rut: '217090160018').order(updated_at: :desc).first

    # Si aún no se encuentra, usar cualquier organización donde el cliente es cliente
    client_org ||= client.client_organizations.order(updated_at: :desc).first

    # Si no se encuentra ninguna organización, crear una nueva para el cliente
    if client_org.nil?
      Rails.logger.info("No se encontró ninguna organización válida para el cliente. Creando una nueva.")
      client_org = Organization.create(
        name: "Organización de #{client.email.split('@').first}",
        rut: nil
      )
      # Crear la relación entre el usuario y la organización como cliente
      # Usamos find_or_create_by para evitar duplicados
      OrganizationUser.find_or_create_by(organization: client_org, user: client, role: :client)
      Rails.logger.info("Nueva organización creada para el cliente: #{client_org.inspect}")
    end

    # Guardar la organización en la sesión para futuras referencias
    session[:client_organization_id] = client_org.id

    client_org
  end

  # Método para verificar si un usuario ya existe y es cliente
  # @param email [String] El email del usuario a verificar
  # @return [User, nil] El usuario si existe y es cliente, nil en caso contrario
  def find_existing_client(email)
    return nil unless email.present?

    existing_user = User.find_by(email: email)
    return nil unless existing_user

    # Si el usuario existe y es un cliente, devolverlo
    existing_user.client? ? existing_user : nil
  end

  # Método para crear o actualizar la relación entre un cliente y una organización
  # @param client [User] El cliente
  # @param organization [Organization] La organización
  # @param access_enabled [Boolean] Si el cliente tiene acceso habilitado
  # @return [OrganizationUser] La relación creada o actualizada
  def create_or_update_client_organization_relation(client, organization, access_enabled = false)
    # Buscar si ya existe una relación
    org_user = OrganizationUser.find_by(user: client, organization: organization)

    if org_user
      # Si ya existe, actualizar el rol y el acceso
      org_user.update(role: :client, access_enabled: access_enabled)
    else
      # Si no existe, crear una nueva relación
      org_user = OrganizationUser.create(
        user: client,
        organization: organization,
        role: :client,
        access_enabled: access_enabled,
        invitation_sent_at: access_enabled ? Time.current : nil
      )
    end

    org_user
  end

  def set_client
    @client = User.find(params[:id])

    # Verificar que el usuario sea un cliente
    unless @client.client?
      redirect_to clients_path, alert: "El usuario seleccionado no es un cliente."
      return
    end
  end

  def check_permissions
    # Verificar que el usuario tenga permisos para gestionar clientes
    unless can_manage_clients?
      redirect_to clients_path, alert: "No tienes permisos para gestionar clientes."
      return
    end
  end

  def can_manage_clients?
    # Superadmin, support y owner pueden gestionar clientes
    return true if current_user.superadmin? || current_user.support? || current_user.owner?

    # Colaboradores con permiso edit_client_data pueden gestionar clientes
    return true if current_user.collaborator? && current_user.permission?('edit_client_data')

    false
  end

  def can_view_client?(client)
    # Superadmin y support pueden ver todos los clientes
    return true if current_user.superadmin? || current_user.support?

    # Owner puede ver los clientes de su organización
    return true if current_user.owner?

    # Colaborador con permiso view_all_clients puede ver todos los clientes de su organización
    return true if current_user.collaborator? && current_user.permission?('view_all_clients')

    # Colaborador puede ver sus clientes asignados (tanto por assigned_to_id como por ClientAssignment)
    if current_user.collaborator?
      # Verificar asignación legacy (assigned_to_id)
      return true if client.assigned_to_id == current_user.id

      # Verificar asignación múltiple (ClientAssignment)
      return true if current_user.has_client_assigned?(client)
    end

    # Cualquier usuario puede ver los clientes que ha creado
    # Verificamos si el cliente pertenece a alguna de las organizaciones del usuario
    client_orgs = client.organizations.pluck(:id)
    user_orgs = current_user.organizations.pluck(:id)
    return true if (client_orgs & user_orgs).any?

    false
  end

  def can_edit_client?(client)
    # Superadmin y support pueden editar todos los clientes
    return true if current_user.superadmin? || current_user.support?

    # Owner puede editar los clientes de su organización
    return true if current_user.owner?

    # Colaborador con permiso edit_client_data puede editar sus clientes asignados
    # o los clientes que pertenecen a alguna de sus organizaciones
    if current_user.collaborator? && current_user.permission?('edit_client_data')
      # Verificar asignación legacy (assigned_to_id)
      return true if client.assigned_to_id == current_user.id

      # Verificar asignación múltiple (ClientAssignment)
      return true if current_user.has_client_assigned?(client)

      # Verificar si el cliente pertenece a alguna de las organizaciones del usuario
      client_orgs = client.organizations.pluck(:id)
      user_orgs = current_user.organizations.pluck(:id)
      return true if (client_orgs & user_orgs).any?
    end

    false
  end


  def organization_params
    params.require(:organization).permit(:name, :rut, :nombre_legal, :nombre_fantasia, :tipo_entidad, :direccion, :telefono, :email_facturacion)
  end

  def user_params
    # Permitir los parámetros necesarios para actualizar un usuario
    # y collaborator_ids para las asignaciones múltiples
    params.require(:user).permit(:email, :name, :last_name, :collaborator_id, collaborator_ids: [])
  end
end

# Este es el final del módulo Collaborator
end
