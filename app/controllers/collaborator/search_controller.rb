# frozen_string_literal: true

module Collaborator
  # Controlador para búsquedas en el espacio del colaborador
  class SearchController < ApplicationController
    before_action :require_authentication
    before_action :require_collaborator
    before_action :set_organization
    
    # Verifica que el usuario sea colaborador
    def require_collaborator
      return if current_user.collaborator?
      
      respond_to do |format|
        format.json { render json: { error: 'No autorizado' }, status: :forbidden }
        format.html { redirect_to root_path, alert: 'No tiene permisos para acceder a esta sección' }
      end
    end
    
    # GET /collaborator/search/fiscal_data
    # Consulta datos fiscales a partir de un RUT
    #
    # Parámetros:
    #   rut: RUT a consultar
    #
    # Retorna:
    #   JSON con los datos fiscales obtenidos de la API externa
    def fiscal_data
      begin
        rut = params[:rut].to_s.strip
        
        # Verificar que se proporcionó un RUT
        if rut.blank?
          return render json: { success: false, error: "Debe proporcionar un RUT" }, status: :bad_request
        end
        
        # Crear archivo de log para depuración
        debug_log = File.open('/home/<USER>/compartido/rais1/log/fiscal_debug_collab.log', 'w')
        debug_log.puts("Iniciando depuración: #{Time.now}")
        debug_log.puts("RUT recibido: #{rut}")
        
        begin
          # Consultar datos fiscales usando el servicio
          debug_log.puts("Llamando a RutApiService.fetch_fiscal_data")
          result = RutApiService.fetch_fiscal_data(rut)
          debug_log.puts("Resultado: #{result.inspect}")
          
          # Verificar si la consulta fue exitosa
          if result[:success]
            debug_log.puts("Consulta exitosa")
            debug_log.close
            # Asegurar que los datos se envían directamente, no anidados
            render json: { 
              success: true, 
              name: result[:name],
              nombre_legal: result[:nombre_legal],
              nombre_fantasia: result[:nombre_fantasia],
              tipo_entidad: result[:tipo_entidad],
              address: result[:address],
              direccion: result[:address], # Alias para compatibilidad
              phone: result[:phone],
              telefono: result[:phone], # Alias para compatibilidad
              email: result[:email],
              actividades: result[:actividades],
              raw_data: result[:raw_data]
            }, status: :ok
          else
            # Enviar error si no se pudieron obtener los datos
            debug_log.puts("Consulta fallida: #{result[:error]}")
            debug_log.close
            render json: { 
              success: false, 
              error: result[:error] || "No se pudieron obtener los datos fiscales" 
            }, status: :unprocessable_entity
          end
        rescue => e
          debug_log.puts("ERROR GRAVE: #{e.class}: #{e.message}")
          debug_log.puts("Backtrace: #{e.backtrace.join("\n")}")
          debug_log.close
          render json: { success: false, error: "Error interno: #{e.message}" }, status: :internal_server_error
        end
      rescue => outer_e
        # Capturar cualquier error no manejado
        File.open('/home/<USER>/compartido/rais1/log/fiscal_error_collab.log', 'w') do |f|
          f.puts("ERROR CRÍTICO: #{outer_e.class}: #{outer_e.message}")
          f.puts("Backtrace: #{outer_e.backtrace.join("\n")}")
        end
        render json: { success: false, error: "Error crítico: #{outer_e.message}" }, status: :internal_server_error
      end
    end
    
    private
    
    # Establece la organización actual del usuario colaborador
    def set_organization
      begin
        @organization = current_user.organization
        
        # Si no se pudo obtener la organización, lanzar una excepción
        if @organization.nil?
          raise 'No tiene una organización asignada para realizar esta acción.'
        end
        
        @organization # Retornar la organización encontrada
        
      rescue => e
        # Registrar el error para depuración
        Rails.logger.error("Error en set_organization: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        
        # Manejar la respuesta según el formato solicitado
        respond_to do |format|
          format.json do 
            render json: { 
              success: false, 
              error: e.message || 'No se pudo determinar la organización' 
            }, status: :unprocessable_entity
          end
          format.html { redirect_to root_path, alert: e.message || 'No se pudo determinar la organización' }
        end
        
        # Retornar false para detener el procesamiento de la acción
        false
      end
    end
  end
end
