console.log('[Application] 🚀 Iniciando aplicación JavaScript...');

// Importar Turbo para navegación SPA
import "@hotwired/turbo-rails";
console.log('[Application] ✅ Turbo importado');

// Importar controladores Stimulus (esto registrará automáticamente todos los controladores)
import "./controllers";
console.log('[Application] ✅ Controladores importados');

// Verificar inmediatamente si Stimulus está disponible
console.log('[Application] Verificando Stimulus inmediatamente...');
console.log('[Application] window.Stimulus:', window.Stimulus);

// Verificar que Stimulus esté disponible después de que se cargue todo
document.addEventListener('DOMContentLoaded', () => {
  console.log('[Application] 📄 DOM cargado, verificando Stimulus...');

  // Verificar que Stimulus esté disponible
  if (window.Stimulus) {
    console.log('[Application] ✅ Stimulus cargado correctamente');
    console.log('[Application] Versión de Stimulus:', window.Stimulus.version || 'N/A');
    console.log('[Application] Controladores registrados:',
      Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || [])
    );
  } else {
    console.error('[Application] ❌ Error: Stimulus no está disponible');
    console.log('[Application] Intentando acceder a Stimulus de otra manera...');

    // Intentar importar Stimulus directamente
    import("@hotwired/stimulus").then(({ Application }) => {
      console.log('[Application] 🔧 Stimulus importado directamente:', Application);
      if (!window.Stimulus) {
        console.log('[Application] 🔧 Creando instancia de Stimulus manualmente...');
        window.Stimulus = Application.start();
        console.log('[Application] 🔧 Stimulus creado manualmente:', window.Stimulus);
      }
    }).catch(error => {
      console.error('[Application] ❌ Error al importar Stimulus:', error);
    });
  }
});
