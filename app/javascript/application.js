console.log('[Application] Iniciando aplicación JavaScript...');

// Importar Turbo para navegación SPA
import "@hotwired/turbo-rails";
console.log('[Application] Turbo importado');

// Importar controladores Stimulus (esto registrará automáticamente todos los controladores)
import "./controllers";
console.log('[Application] Controladores importados');

// Verificar que Stimulus esté disponible después de que se cargue todo
document.addEventListener('DOMContentLoaded', () => {
  console.log('[Application] DOM cargado, verificando Stimulus...');

  // Verificar que Stimulus esté disponible
  if (window.Stimulus) {
    console.log('✅ Stimulus cargado correctamente');
    console.log('Versión de Stimulus:', window.Stimulus.version || 'N/A');
    console.log('Controladores registrados:',
      Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || [])
    );
  } else {
    console.error('❌ Error: Stimulus no está disponible');
  }
});
