console.log('[Application] Iniciando aplicación JavaScript...');

// Importar Stimulus con el enfoque estándar de Rails 7
import "@hotwired/stimulus"
import "@hotwired/stimulus-loading"

// Esperar a que se cargue el DOM para verificar que Stimulus está disponible
document.addEventListener('DOMContentLoaded', () => {
  console.log('[Application] DOM listo, Stimulus estado:', window.Stimulus ? 'disponible' : 'no disponible');
});

// Importar Turbo para navegación SPA
import "@hotwired/turbo-rails";
console.log('[Application] Turbo importado');

// Importar controladores Stimulus (esto usará la instancia que acabamos de crear)
import "./controllers";
console.log('[Application] Controladores importados');

// Mensaje de depuración
document.addEventListener('DOMContentLoaded', () => {
  console.log('[Application] DOM completamente cargado');
  console.log('Aplicación JavaScript cargada');
  
  // Verificar que Stimulus esté disponible
  if (window.Stimulus) {
    console.log('Stimulus cargado correctamente');
    console.log('Versión de Stimulus:', window.Stimulus.application?.version || 'N/A');
    console.log('Controladores registrados:', 
      Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || [])
    );
    
    // Verificar controlador simplified-rut
    if (window.Stimulus.router?.modulesByIdentifier?.has('simplified-rut')) {
      console.log('✅ El controlador simplified-rut está registrado');
    } else {
      console.warn('⚠️ El controlador simplified-rut NO está registrado');
    }
  } else {
    console.error('Error: Stimulus no está disponible');
  }
});
import "controllers"
