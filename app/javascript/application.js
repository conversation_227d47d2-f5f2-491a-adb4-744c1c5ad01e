// Test ultra simple - solo logs
console.log('🔥 [Application] INICIO DEL ARCHIVO');
console.log('🔥 [Application] Este log debería aparecer SIEMPRE');

// Configurar variables globales para verificar que se ejecuta
window.ApplicationLoaded = true;
window.ApplicationTimestamp = new Date().toISOString();

console.log('🔥 [Application] Variables globales configuradas');

// Import estático simple de Turbo
import "@hotwired/turbo-rails";
console.log('🔥 [Application] Turbo importado con import estático');

// Import estático simple de Stimulus
import { Application } from "@hotwired/stimulus";
console.log('🔥 [Application] Stimulus importado con import estático:', Application);

// Inicializar Stimulus inmediatamente
const application = Application.start();
window.Stimulus = application;

console.log('🔥 [Application] Stimulus inicializado: [Application instance]');
console.log('🔥 [Application] window.Stimulus configurado: [Application instance]');

// Importar y registrar controladores
import "./controllers";
console.log('🔥 [Application] Controladores importados');

// Export para que el módulo no esté vacío
export { application };

console.log('🔥 [Application] FIN DEL ARCHIVO - TODO COMPLETADO');
