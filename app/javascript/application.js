console.log('[Application] 🚀 Iniciando aplicación JavaScript...');

// Importar Turbo para navegación SPA
import "@hotwired/turbo-rails";
console.log('[Application] ✅ Turbo importado');

// Importar controladores Stimulus (esto cargará Stimulus de forma asíncrona)
import "./controllers";
console.log('[Application] ✅ Controladores importados');

// Verificar estado después de que se cargue todo
document.addEventListener('DOMContentLoaded', () => {
  console.log('[Application] 📄 DOM cargado, verificando estado final...');

  // Dar tiempo para que la carga asíncrona termine
  setTimeout(() => {
    if (window.Stimulus) {
      console.log('[Application] ✅ Stimulus cargado correctamente');
      console.log('[Application] Versión de Stimulus:', window.Stimulus.version || 'N/A');
      console.log('[Application] Controladores registrados:',
        Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || [])
      );
    } else {
      console.warn('[Application] ⚠️ Stimulus aún no está disponible, puede estar cargando...');
    }
  }, 1000);
});
