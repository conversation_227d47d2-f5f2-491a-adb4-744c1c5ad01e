console.log('[Application] 🚀 TEST - Iniciando aplicación JavaScript...');

// Test básico - solo Turbo primero
try {
  console.log('[Application] 📦 Importando Turbo...');
  import("@hotwired/turbo-rails").then(() => {
    console.log('[Application] ✅ Turbo importado exitosamente');
    window.TurboLoaded = true;
  }).catch(error => {
    console.error('[Application] ❌ Error al importar Turbo:', error);
  });
} catch (error) {
  console.error('[Application] ❌ Error en import de Turbo:', error);
}

// Test básico - solo Stimulus después
try {
  console.log('[Application] 📦 Importando Stimulus...');
  import("@hotwired/stimulus").then(({ Application }) => {
    console.log('[Application] ✅ Stimulus importado exitosamente:', Application);

    const app = Application.start();
    window.Stimulus = app;
    window.StimulusLoaded = true;

    console.log('[Application] ✅ Stimulus inicializado:', app);
  }).catch(error => {
    console.error('[Application] ❌ Error al importar Stimulus:', error);
  });
} catch (error) {
  console.error('[Application] ❌ Error en import de Stimulus:', error);
}

console.log('[Application] ✅ Script principal completado');
