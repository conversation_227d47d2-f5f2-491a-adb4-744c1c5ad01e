import { Controller } from "@hotwired/stimulus"

/**
 * Controlador de prueba para verificar la correcta configuración de Stimulus
 */
export default class extends Controller {
  static targets = ["output"]
  
  connect() {
    console.log("[Stimulus] Controlador Hello conectado");
    
    try {
      // No sobrescribir el contenido del elemento raíz
      // Solo actualizar el output target si existe
      if (this.hasOutputTarget) {
        this.outputTarget.textContent = "✓ Controlador conectado correctamente";
        this.outputTarget.classList.add("text-green-600", "font-medium");
      } else {
        console.warn("[Stimulus] No se encontró el target 'output' en el controlador Hello");
      }
    } catch (error) {
      console.error("[Stimulus] Error en el controlador Hello:", error);
    }
  }
  
  // Método para manejar el clic en el botón
  greet() {
    try {
      if (this.hasOutputTarget) {
        this.outputTarget.textContent = "¡Hola desde Stimulus! 🎉";
        this.outputTarget.classList.add("text-blue-600");
      }
    } catch (error) {
      console.error("[Stimulus] Error en el método greet:", error);
    }
  }
  
  disconnect() {
    console.log("[Stimulus] Controlador Hello desconectado");
  }
}
