import { Controller } from "@hotwired/stimulus"
import FiscalService from "../../services/fiscal_service"

/**
 * Controlador para el manejo de datos fiscales
 * 
 * Este controlador se encarga de:
 * - Consultar datos fiscales por RUT a la API
 * - Autocompletar el formulario fiscal con los datos recibidos
 * - Guardar los datos fiscales en el servidor
 * - Mostrar mensajes de éxito/error al usuario
 */
export default class extends Controller {
  static targets = [
    "form",       // Formulario fiscal
    "input",      // Input de RUT
    "loading",    // Elemento de carga
    "message",    // Contenedor de mensajes
    "submitBtn"   // Botón de envío
  ]
  
  static values = {
    namespace: { type: String, default: "owner" },
    autoSave: { type: Boolean, default: true },
    debounce: { type: Number, default: 300 }
  }
  
  // Variables de instancia
  #saveTimer = null
  #isSaving = false
  #isSearching = false

  connect() {
    console.log("Controlador fiscal_data conectado")
    this.initializeEventListeners()
  }
  
  disconnect() {
    console.log("Controlador fiscal_data desconectado")
    this.clearSaveTimer()
  }
  
  /**
   * Inicializa los event listeners para los campos del formulario
   */
  initializeEventListeners() {
    if (!this.hasFormTarget) return
    
    // Obtener los campos del formulario
    const fields = this.formTarget.querySelectorAll('input, select, textarea')
    
    // Para cada campo, agregar un listener de cambio
    fields.forEach(field => {
      if (field.type !== 'submit' && field.type !== 'button') {
        field.addEventListener('change', this.handleFieldChange.bind(this))
        field.addEventListener('input', this.debouncedSave.bind(this))
      }
    })
    
    // Listener para el envío del formulario
    this.formTarget.addEventListener('submit', this.handleFormSubmit.bind(this))
    
    console.log('Event listeners inicializados para campos fiscales')
  }
  
  /**
   * Maneja el cambio de un campo
   * @param {Event} event - Evento de cambio
   */
  handleFieldChange(event) {
    console.log(`Campo cambiado: ${event.target.name} = "${event.target.value}"`)
  }
  
  /**
   * Maneja el guardado con debounce para evitar muchas llamadas
   */
  debouncedSave() {
    if (!this.autoSaveValue) return
    
    // Limpiar el timer anterior
    this.clearSaveTimer()
    
    // Configurar un nuevo timer
    this.#saveTimer = setTimeout(() => {
      if (this.hasFormTarget && !this.#isSaving) {
        console.log('Ejecutando guardado automático por debounce')
        this.saveFiscalData()
      }
    }, this.debounceValue)
  }
  
  /**
   * Limpia el timer de guardado
   */
  clearSaveTimer() {
    if (this.#saveTimer) {
      clearTimeout(this.#saveTimer)
      this.#saveTimer = null
    }
  }

  /**
   * Maneja el envío manual del formulario
   * @param {Event} event - Evento submit
   */
  async handleFormSubmit(event) {
    event.preventDefault()
    await this.saveFiscalData()
  }
  
  /**
   * Consulta datos fiscales en base al RUT y los completa en el formulario
   * @param {Event} event - Evento de clic
   */
  async fetchFiscalData(event) {
    if (event) event.preventDefault()
    
    // Evitar múltiples consultas simultáneas
    if (this.#isSearching) {
      console.log('Ya hay una consulta fiscal en curso')
      return
    }
    
    console.log('Iniciando consulta fiscal - Modo diagnóstico')
    
    // Obtener el RUT desde el input
    const rutInput = this.hasInputTarget ? this.inputTarget : document.querySelector('input[name="rut"], input[name="organization[rut]"]')
    
    if (!rutInput) {
      console.error('No se encontró el input de RUT')
      this.showMessage('No se encontró el input de RUT', 'error')
      return
    }
    
    const rut = rutInput.value.trim()
    if (!rut) {
      console.error('El campo RUT está vacío')
      this.showMessage('Por favor ingrese un RUT', 'error')
      return
    }
    
    console.log('RUT a consultar:', rut)
    
    // Mostrar spinner de carga
    this.showLoading(true)
    this.#isSearching = true
    
    try {
      // Obtener el ID del cliente de la URL
      const clientId = window.location.pathname.split('/').filter(Boolean).pop()
      
      // Consultar los datos fiscales
      const result = await FiscalService.fetchFiscalData(rut, clientId, this.namespaceValue)
      console.log('Resultado consulta fiscal:', result)
      
      if (result.success) {
        console.log('Datos fiscales obtenidos correctamente')
        
        // Preparar los datos para autocompletado
        const normalizedData = FiscalService.prepareAutocompletionData(result)
        
        // Autocompletar el formulario
        this.autocompleteFiscalForm(normalizedData)
        
        // Mostrar mensaje de éxito
        this.showMessage('Datos fiscales obtenidos correctamente', 'success')
      } else {
        console.error('Error al obtener datos fiscales:', result.message)
        this.showMessage(result.message || 'No se pudieron obtener los datos fiscales', 'error')
      }
    } catch (error) {
      console.error('Error al consultar datos fiscales:', error)
      this.showMessage('Error al consultar datos fiscales: ' + (error.message || 'Error desconocido'), 'error')
    } finally {
      // Ocultar spinner de carga
      this.showLoading(false)
      this.#isSearching = false
    }
  }

  /**
   * Autocompleta el formulario fiscal con los datos proporcionados
   * @param {Object} fiscalData - Datos fiscales a completar
   */
  autocompleteFiscalForm(fiscalData) {
    console.log('Autocompletando formulario con datos fiscales:', fiscalData)
    
    const form = this.hasFormTarget ? this.formTarget : document.querySelector('form')
    if (!form) {
      console.error('No se encontró el formulario para autocompletar')
      return
    }
    
    // Mapear los datos de la API a los campos del formulario
    const fieldMapping = {
      'organization[nombre_legal]': fiscalData.nombre_legal,
      'organization[nombre_fantasia]': fiscalData.nombre_fantasia,
      'organization[tipo_entidad]': fiscalData.tipo_entidad,
      'organization[direccion]': fiscalData.direccion,
      'organization[ciudad]': fiscalData.ciudad,
      'organization[departamento]': fiscalData.departamento,
      'organization[region]': fiscalData.region,
      'organization[comuna]': fiscalData.comuna,
      'organization[telefono]': fiscalData.telefono,
      'organization[email]': fiscalData.email,
      'organization[email_facturacion]': fiscalData.email_facturacion,
      'organization[estado]': fiscalData.estado,
      'organization[fecha_inicio_actividades]': fiscalData.fecha_inicio_actividades,
    }
    
    console.log('Usando mapeo de campos fiscal:', fieldMapping)
    
    // Actualizar cada campo del formulario
    Object.entries(fieldMapping).forEach(([fieldName, value]) => {
      const field = form.querySelector(`[name="${fieldName}"]`)
      if (field) {
        field.value = value || ''
        console.log(`Campo ${fieldName} actualizado a: "${value}"`, field)
        
        // Disparar eventos para notificar a otros componentes
        field.dispatchEvent(new Event('input', { bubbles: true }))
        field.dispatchEvent(new Event('change', { bubbles: true }))
      } else {
        console.warn(`Campo ${fieldName} no encontrado en el formulario`)
      }
    })
    
    // Manejar actividades/giros si están disponibles (como array)
    if (fiscalData.actividades && fiscalData.actividades.length > 0) {
      const actividadesField = form.querySelector('[name="organization[actividades]"], [name="organization[actividades][]"]')
      if (actividadesField) {
        actividadesField.value = fiscalData.actividades[0]
        actividadesField.dispatchEvent(new Event('input', { bubbles: true }))
        actividadesField.dispatchEvent(new Event('change', { bubbles: true }))
        console.log(`Actividades actualizadas: ${fiscalData.actividades[0]}`)
      }
    }
    
    console.log('Formulario autocompletado exitosamente')
    
    // Guardar automáticamente si está habilitado
    if (this.autoSaveValue) {
      console.log('Iniciando guardado automático después de autocompletar...')
      this.saveFiscalData()
    }
  }
  
  /**
   * Guarda los datos fiscales del formulario
   * @returns {Promise<Object>} Resultado del guardado
   */
  async saveFiscalData() {
    if (this.#isSaving) {
      console.log('Ya hay un guardado en curso, ignorando solicitud')
      return
    }
    
    const form = this.hasFormTarget ? this.formTarget : document.querySelector('form')
    if (!form) {
      console.error('No se encontró el formulario para guardar')
      this.showMessage('No se encontró el formulario', 'error')
      return { success: false, message: 'No se encontró el formulario' }
    }
    
    console.log('Iniciando guardado de datos fiscales...')
    this.#isSaving = true
    this.showLoading(true)
    
    // Deshabilitar el botón de submit si existe
    if (this.hasSubmitBtnTarget) {
      this.submitBtnTarget.disabled = true
    }
    
    try {
      // Usar el servicio para guardar los datos
      const result = await FiscalService.saveFiscalData(form, {
        namespace: this.namespaceValue,
        showLogs: true
      })
      
      if (result.success) {
        console.log('Datos fiscales guardados correctamente')
        this.showMessage('Datos fiscales guardados correctamente', 'success')
      } else {
        console.error('Error al guardar datos fiscales:', result.message)
        this.showMessage(result.message || 'Error al guardar los datos fiscales', 'error')
      }
      
      return result
    } catch (error) {
      console.error('Error en saveFiscalData:', error)
      this.showMessage('Error al guardar: ' + (error.message || 'Error desconocido'), 'error')
      return { success: false, message: error.message || 'Error desconocido' }
    } finally {
      this.showLoading(false)
      this.#isSaving = false
      
      // Re-habilitar el botón de submit si existe
      if (this.hasSubmitBtnTarget) {
        this.submitBtnTarget.disabled = false
      }
    }
  }
  
  /**
   * Muestra/oculta el indicador de carga
   * @param {boolean} show - Indica si se debe mostrar
   */
  showLoading(show = true) {
    if (this.hasLoadingTarget) {
      this.loadingTarget.style.display = show ? 'block' : 'none'
    }
  }
  
  /**
   * Muestra un mensaje al usuario
   * @param {string} message - Mensaje a mostrar
   * @param {string} type - Tipo de mensaje ('success', 'error', 'info')
   */
  showMessage(message, type = 'info') {
    console.log(`Mensaje (${type}):`, message)
    
    // Si hay un target específico para mensajes, usarlo
    if (this.hasMessageTarget) {
      const messageElement = this.messageTarget
      
      // Configurar el mensaje
      messageElement.textContent = message
      messageElement.className = `alert alert-${type}`
      messageElement.style.display = 'block'
      
      // Ocultar después de un tiempo
      setTimeout(() => {
        messageElement.style.display = 'none'
      }, 5000)
      return
    }
    
    // Alternativa: crear un toast o usar notificaciones del sistema
    this.showToast(message, type)
  }
  
  /**
   * Muestra/oculta el indicador de carga
   * @param {boolean} show - Indica si mostrar u ocultar el indicador
   */
  showLoading(show) {
    if (this.hasLoadingTarget) {
      this.loadingTarget.style.display = show ? 'inline-block' : 'none'
    }
  }

  /**
   * Muestra un mensaje de éxito
   * @param {string} message - Mensaje a mostrar
   */
  showSuccess(message) {
    console.log('Mostrando mensaje de éxito:', message)
    
    // Eliminar mensajes anteriores
    this.removeMessages()
    
    // Crear elemento de mensaje de éxito
    const successElement = document.createElement('div')
    successElement.className = 'mt-2 p-2 text-sm font-medium text-green-700 bg-green-100 rounded'
    successElement.textContent = message
    
    // Insertar después del input
    if (this.hasInputTarget && this.inputTarget.parentNode) {
      this.inputTarget.parentNode.insertBefore(successElement, this.inputTarget.nextSibling)
    } else if (this.element) {
      this.element.appendChild(successElement)
    }
    
    // Eliminar el mensaje después de 5 segundos
    setTimeout(() => {
      successElement.remove()
    }, 5000)
  }

  /**
   * Muestra un mensaje de error
   * @param {string} message - Mensaje a mostrar
   */
  showError(message) {
    console.error('Error:', message)
    
    // Eliminar mensajes anteriores
    this.removeMessages()
    
    // Crear elemento de mensaje de error
    const errorElement = document.createElement('div')
    errorElement.className = 'mt-2 p-2 text-sm font-medium text-red-700 bg-red-100 rounded'
    errorElement.textContent = message
    
    // Insertar después del input
    if (this.hasInputTarget && this.inputTarget.parentNode) {
      this.inputTarget.parentNode.insertBefore(errorElement, this.inputTarget.nextSibling)
    } else if (this.element) {
      this.element.appendChild(errorElement)
    }
    
    // Eliminar el mensaje después de 5 segundos
    setTimeout(() => {
      errorElement.remove()
    }, 5000)
  }

  /**
   * Elimina todos los mensajes (éxito y error)
   */
  removeMessages() {
    // Eliminar mensajes de éxito
    const successElements = this.element.querySelectorAll('.text-green-700')
    successElements.forEach(element => element.remove())
    
    // Eliminar mensajes de error
    const errorElements = this.element.querySelectorAll('.text-red-700')
    errorElements.forEach(element => element.remove())
  }
  
  /**
   * Muestra un toast o notificación temporal
   * @param {string} message - Mensaje a mostrar
   * @param {string} type - Tipo de mensaje
   */
  showToast(message, type = 'info') {
    console.log(`Mostrando toast (${type}):`, message)
    
    // Buscar o crear el contenedor para toasts
    let toastContainer = document.getElementById('toast-container')
    if (!toastContainer) {
      toastContainer = document.createElement('div')
      toastContainer.id = 'toast-container'
      toastContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; max-width: 300px; z-index: 9999;'
      document.body.appendChild(toastContainer)
    }
    
    // Crear el toast
    const toast = document.createElement('div')
    toast.className = `alert alert-${type} mb-2`
    toast.style.cssText = 'opacity: 0; transition: opacity 0.3s ease-in-out;'
    toast.textContent = message
    
    // Agregar al contenedor
    toastContainer.appendChild(toast)
    
    // Mostrar con efecto fade
    setTimeout(() => toast.style.opacity = '1', 10)
    
    // Ocultar después de 5 segundos
    setTimeout(() => {
      toast.style.opacity = '0'
      setTimeout(() => toastContainer.removeChild(toast), 300)
    }, 5000)
  }
}
