import { Controller } from "@hotwired/stimulus"

// Controlador para validación de RUT uruguayo
export default class UruguayanRutController extends Controller {
  static targets = ["input", "submitButton", "errorMessage", "spinnerOverlay", "spinnerMessage", "buttonSpinner", "buttonText"]

  connect() {
    console.log("RUT validation controller connected")
    // Buscar el botón de envío
    this.submitButton = this.element.querySelector('input[type="submit"]')
    if (!this.submitButton && this.hasSubmitButtonTarget) {
      this.submitButton = this.submitButtonTarget
    }

    // Deshabilitar el botón por defecto
    if (this.submitButton) {
      this.submitButton.disabled = true
    }

    // Validar el RUT inicial si hay un valor
    if (this.inputTarget.value) {
      this.validateRut()
    }

    // Agregar evento para mostrar el spinner al enviar el formulario
    this.element.addEventListener("submit", this.showSpinner.bind(this))
  }

  // Método de instancia para validación de RUT con UI
  validateRut() {
    const rutInput = this.inputTarget

    // Limpiar mensajes de error
    if (this.hasErrorMessageTarget) {
      this.errorMessageTarget.textContent = ""
      this.errorMessageTarget.classList.add("hidden")
    }

    // Obtener el valor del RUT sin caracteres especiales
    const rutValue = rutInput.value.replace(/[^0-9]/g, "")
    console.log(`Validando RUT: ${rutValue}`)

    // Si está vacío, no validar aún
    if (rutValue.length === 0) {
      if (this.submitButton) this.submitButton.disabled = true
      return false
    }
    
    // Usar el método estático para validar el RUT
    const [isValid, errorMessage] = UruguayanRutController.validateUruguayanRut(rutValue)
    
    if (!isValid) {
      this.showError(errorMessage)
      if (this.submitButton) this.submitButton.disabled = true
      return false
    }

    // Si llegamos aquí, el RUT es válido
    console.log(`RUT válido: ${rutValue}`)
    if (this.submitButton) this.submitButton.disabled = false
    return true
  }
  
  // Método estático para validación de RUT uruguayo (puede ser usado por otros controladores)
  static validateUruguayanRut(rutValue) {
    // Validar longitud
    if (rutValue.length !== 12) {
      return [false, `El RUT debe tener 12 dígitos numéricos (actual: ${rutValue.length})`];
    }

    // Validar primeros dos dígitos (entre 01 y 22)
    const firstTwoDigits = parseInt(rutValue.substring(0, 2), 10)
    if (firstTwoDigits < 1 || firstTwoDigits > 22) {
      return [false, `Los dos primeros dígitos deben estar entre 01 y 22 (actual: ${firstTwoDigits})`];
    }

    // Validar que posiciones 3-8 no sean todos ceros
    if (rutValue.substring(2, 8) === "000000") {
      return [false, "Los dígitos del 3 al 8 no pueden ser todos ceros"];
    }

    // Validar que posiciones 9-10 sean "00"
    if (rutValue.substring(8, 10) !== "00") {
      return [false, `Los dígitos 9 y 10 deben ser '00' (actual: ${rutValue.substring(8, 10)})`];
    }

    // Validar dígito verificador
    const firstEleven = rutValue.substring(0, 11)
    const lastDigit = parseInt(rutValue.substring(11, 12), 10)
    const factors = [4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]

    let sum = 0
    for (let i = 0; i < 11; i++) {
      sum += parseInt(firstEleven.charAt(i), 10) * factors[i]
    }

    const remainder = sum % 11
    let checkDigit = 11 - remainder

    // Si el resultado es 11, el dígito verificador es 0
    if (checkDigit === 11) {
      checkDigit = 0
    }

    // Si el resultado es 10, el RUT no es válido
    if (checkDigit === 10) {
      return [false, "El dígito verificador no es válido (resultado 10)"];
    }

    // Verificar que el dígito verificador calculado coincida con el último dígito del RUT
    if (checkDigit !== lastDigit) {
      return [false, `El dígito verificador no es válido (esperado: ${checkDigit}, recibido: ${lastDigit})`];
    }
    
    // Si llegamos aquí, el RUT es válido
    return [true, null];
  }

  showError(message) {
    console.log(`Error de validación: ${message}`)
    if (this.hasErrorMessageTarget) {
      this.errorMessageTarget.textContent = message
      this.errorMessageTarget.classList.remove("hidden")
    }
  }

  formatRut() {
    const rutInput = this.inputTarget
    let rutValue = rutInput.value.replace(/[^0-9]/g, "")

    // Limitar a 12 dígitos
    if (rutValue.length > 12) {
      rutValue = rutValue.substring(0, 12)
    }

    rutInput.value = rutValue
    this.validateRut()
  }

  // Muestra el estado de carga cuando se envía el formulario
  showSpinner(event, message = null) {
    // Cancelar cualquier consulta anterior en curso
    this.cancelConsultaAnterior()

    // Solo mostrar el estado de carga si el formulario es válido
    if (this.element.checkValidity() && this.validateRut()) {
      console.log("Mostrando estado de carga")

      if (this.hasSubmitButtonTarget) {
        // Guardar las clases originales del botón si no se han guardado aún
        if (!this._originalButtonClasses) {
          this._originalButtonClasses = this.submitButtonTarget.className
        }

        // Cambiar el color del botón a un color de carga (verde) usando estilos inline
        this.submitButtonTarget.classList.remove("bg-indigo-600", "hover:bg-indigo-700")
        this.submitButtonTarget.classList.add("cursor-wait")

        // Aplicar estilos inline para asegurar que el cambio de color sea visible
        this.submitButtonTarget.style.backgroundColor = "#4ade80" // Verde fluorescente (green-400)
        this.submitButtonTarget.style.borderColor = "#4ade80"
        this.submitButtonTarget.style.color = "black" // Texto negro para mejor contraste con verde

        // Cambiar el texto del botón si se proporciona un mensaje
        if (this.hasButtonTextTarget) {
          // Guardar el texto original si no se ha guardado aún
          if (!this._originalButtonText) {
            this._originalButtonText = this.buttonTextTarget.textContent
          }

          // Cambiar el texto a "Consultando..." o el mensaje proporcionado
          this.buttonTextTarget.textContent = message || "Consultando..."
        }

        console.log("Estado de carga visible en el botón")

        // Marcar que hay una consulta en curso
        this.consultaEnCurso = true

        // Configurar un timeout para restaurar el botón después de 30 segundos
        // en caso de que la consulta falle y no se reciba respuesta
        this.timeout = setTimeout(() => {
          console.log("Timeout de consulta alcanzado")
          this.hideSpinner()

          // Mostrar un mensaje de error
          const errorMessage = document.createElement('div')
          errorMessage.className = 'mt-2 text-sm text-red-600'
          errorMessage.textContent = 'La consulta ha tardado demasiado tiempo. Por favor, inténtelo de nuevo.'
          this.element.appendChild(errorMessage)

          // Eliminar el mensaje después de 5 segundos
          setTimeout(() => {
            if (errorMessage.parentNode) {
              errorMessage.parentNode.removeChild(errorMessage)
            }
          }, 5000)
        }, 30000) // 30 segundos
      }
    } else {
      console.warn("Formulario no válido o RUT no válido, no se muestra el estado de carga")
    }
  }

  // Cancela cualquier consulta anterior en curso
  cancelConsultaAnterior() {
    if (this.consultaEnCurso) {
      console.log("Cancelando consulta anterior")

      // Limpiar el timeout
      if (this.timeout) {
        clearTimeout(this.timeout)
        this.timeout = null
      }

      // Restaurar el botón
      this.hideSpinner()

      // Ya no hay consulta en curso
      this.consultaEnCurso = false
    }
  }

  // Método para mostrar el spinner con un mensaje personalizado
  showSpinnerWithMessage(message) {
    this.showSpinner(null, message)
  }
  
  // Método estático para limpiar un RUT (quitar caracteres no numéricos)
  static cleanRut(rut) {
    return rut ? rut.replace(/[^0-9]/g, "").trim() : '';
  }

  // Oculta el estado de carga (método público que puede ser llamado desde fuera)
  hideSpinner() {
    console.log("Ocultando estado de carga")

    // Limpiar el timeout si existe
    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }

    // Restaurar el botón a su estado original
    if (this.hasSubmitButtonTarget) {
      // Restaurar las clases originales del botón
      if (this._originalButtonClasses) {
        // Eliminar las clases de carga
        this.submitButtonTarget.classList.remove("cursor-wait")

        // Restaurar las clases originales
        this.submitButtonTarget.classList.add("bg-indigo-600", "hover:bg-indigo-700")

        // Eliminar los estilos inline
        this.submitButtonTarget.style.backgroundColor = ""
        this.submitButtonTarget.style.borderColor = ""
        this.submitButtonTarget.style.color = ""
      }

      // Restaurar el texto original del botón
      if (this.hasButtonTextTarget && this._originalButtonText) {
        this.buttonTextTarget.textContent = this._originalButtonText
      }

      console.log("Botón restaurado a su estado original")
    }

    // Ya no hay consulta en curso
    this.consultaEnCurso = false
  }
}
