import { Controller } from "@hotwired/stimulus";
import { validateRut } from "../utils/rut_utils";
import FiscalApiService from "../services/fiscal_api_service";

/**
 * Controlador legacy para validación y formato de RUT
 * 
 * NOTA: Este controlador se mantiene solo para vistas legacy en:
 * - owner/clients/edit.html.erb
 * - collaborator/clients/edit.html.erb
 * 
 * Para nuevas implementaciones, usar simplified_rut_controller.js
 */
export default class extends Controller {
  static targets = [
    "input", "searchButton", "clearButton", "searchContainer", "searchResults", 
    "rutError", "fiscalMessage", "fiscalForm", "spinner"
  ];

  static values = {
    searchUrl: String,
    validateRut: { type: Boolean, default: true },
    namespace: { type: String, default: 'owner' }
  };

  connect() {
    console.log("%c[RUT Controller] Conectado", "background: #FFA726; color: white; padding: 2px 5px; border-radius: 3px;");
    console.log("Elemento:", this.element);
    console.log("Valor del input:", this.hasInputTarget ? this.inputTarget.value : 'No disponible');
    console.log("URL actual:", window.location.href);
    console.log("Targets disponibles:", {
      input: this.hasInputTarget ? "✓" : "✗",
      searchButton: this.hasSearchButtonTarget ? "✓" : "✗",
      fiscalForm: this.hasFiscalFormTarget ? "✓" : "✗",
      spinner: this.hasSpinnerTarget ? "✓" : "✗",
      fiscalMessage: this.hasFiscalMessageTarget ? "✓" : "✗"
    });
    
    // Si hay input y tiene valor, formatearlo
    if (this.hasInputTarget && this.inputTarget.value.trim()) {
      console.log("Formateando RUT inicial:", this.inputTarget.value);
      this.format();
    }

    // Crear instancia del servicio fiscal para consulta DGI
    this.fiscalApiService = new FiscalApiService();
    console.log("%c[RUT Controller] Servicio fiscal inicializado", "background: #43A047; color: white; padding: 2px 5px; border-radius: 3px;");
  }

  /**
   * Formatea un RUT con puntos y guion
   */
  format() {
    console.log("%c[RUT Controller] Formato", "background: #2196F3; color: white; padding: 2px 5px; border-radius: 3px;");
    if (!this.hasInputTarget) {
      console.warn("No se encontró el input target para formatear RUT");
      return;
    }

    let value = this.inputTarget.value.trim();
    if (!value) {
      console.log("Valor vacío, no se formatea");
      return;
    }

    console.log("Valor original:", value);
    // Eliminar todos los caracteres no numéricos y 'k'
    value = value.replace(/[^0-9kK]/g, "");
    console.log("Valor limpio:", value);

    // Formatear con puntos y guion
    if (value.length > 1) {
      const body = value.slice(0, -1);
      const dv = value.slice(-1).toLowerCase();
      
      // Agregar puntos para los miles
      let formatted = "";
      for (let i = body.length - 1, j = 0; i >= 0; i--, j++) {
        if (j % 3 === 0 && j > 0) formatted = "." + formatted;
        formatted = body[i] + formatted;
      }
      
      // Agregar guion y dígito verificador
      formatted = formatted + "-" + dv;
      console.log("Valor formateado:", formatted);
      
      this.inputTarget.value = formatted;
    }

    // Validar si es necesario
    if (this.validateRutValue) {
      console.log("Validando RUT...");
      this.validate();
    }
  }

  /**
   * Valida un RUT
   * @returns {boolean} Indica si el RUT es válido
   */
  validate() {
    console.log("%c[RUT Controller] Validación", "background: #673AB7; color: white; padding: 2px 5px; border-radius: 3px;");
    
    if (!this.hasInputTarget) {
      console.warn("No se encontró el input target para validar RUT");
      return false;
    }
    
    const rut = this.inputTarget.value.trim();
    if (!rut) {
      console.warn("RUT vacío, no se valida");
      return false;
    }

    // Limpiar RUT para validación
    const cleanRut = rut.replace(/\.|-/g, "");
    console.log("RUT a validar (limpio):", cleanRut);
    
    const result = validateRut(cleanRut);
    console.log("Resultado de validación:", result ? "✓ Válido" : "✗ Inválido");
    
    // Mostrar error si es inválido
    if (this.hasRutErrorTarget) {
      if (!result) {
        this.rutErrorTarget.textContent = "RUT inválido";
        this.rutErrorTarget.classList.remove("hidden");
        console.log("Mostrando mensaje de error");
      } else {
        this.rutErrorTarget.classList.add("hidden");
        console.log("Ocultando mensaje de error");
      }
    } else {
      console.warn("No se encontró el target rutError para mostrar/ocultar errores");
    }
    
    return result;
  }

  /**
   * Limpia el campo de búsqueda y los resultados
   */
  clear() {
    if (this.hasInputTarget) {
      this.inputTarget.value = "";
      this.inputTarget.focus();
    }
    
    if (this.hasSearchResultsTarget) {
      this.searchResultsTarget.innerHTML = "";
      this.searchResultsTarget.classList.add("hidden");
    }
  }

  /**
   * Búsqueda simple de RUT
   */
  search() {
    if (!this.hasInputTarget) return;

    const query = this.inputTarget.value.trim();
    if (!query) return;

    // Validar formato de RUT
    if (this.validateRutValue && !this.validate()) {
      return;
    }

    // Redirigir a página de búsqueda con RUT
    window.location.href = `/${this.namespaceValue}/search?q=${encodeURIComponent(query)}`;
  }

  /**
   * Consultar datos fiscales para el RUT actual
   */
  async fetchFiscalData(event) {
    console.log("%c[RUT Controller] Consulta Fiscal ⚡️", "background: #E91E63; color: white; padding: 2px 5px; border-radius: 3px; font-weight: bold");
    console.log("Fecha y hora:", new Date().toLocaleString());
    console.log("Evento:", event);
    console.log("Tipo de evento:", event ? event.type : "N/A");
    console.log("Elemento que disparó el evento:", event ? event.currentTarget : "N/A");
    console.log("URL actual:", window.location.href);
    console.log("Controlador:", this);
    
    if (!this.hasInputTarget) {
      console.error("❌ No se encontró el input target de RUT");
      return;
    }

    const rut = this.inputTarget.value.trim();
    if (!rut) {
      this.showFiscalMessage("Por favor ingrese un RUT para consultar", "warning");
      return;
    }

    // Validar formato de RUT si es necesario
    if (this.validateRutValue && !this.validate()) {
      this.showFiscalMessage("RUT inválido, por favor verifique el formato", "error");
      return;
    }

    // Mostrar spinner de carga
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.classList.remove("hidden");
    }

    try {
      // Obtener el ID del cliente de la URL
      const pathParts = window.location.pathname.split('/');
      const clientId = pathParts[pathParts.length - 1];

      // Usar el servicio fiscal
      const result = await this.fiscalApiService.fetchFiscalData(rut, clientId);

      if (result && result.success) {
        this._populateFiscalForm(result);
        this.showFiscalMessage("Datos fiscales cargados correctamente", "success");
      } else {
        this.showFiscalMessage(result?.message || "No se pudieron obtener datos fiscales", "error");
      }
    } catch (error) {
      console.error("Error al consultar datos fiscales:", error);
      this.showFiscalMessage(error.message || "Error al consultar datos fiscales", "error");
    } finally {
      // Ocultar spinner de carga
      if (this.hasSpinnerTarget) {
        this.spinnerTarget.classList.add("hidden");
      }
    }
  }

  /**
   * Completa el formulario fiscal con los datos de la API
   * @param {Object} data - Los datos fiscales de la API
   * @private
   */
  _populateFiscalForm(data) {
    if (!this.hasFiscalFormTarget || !data.result) return;

    const fiscalData = data.result;
    const form = this.fiscalFormTarget;

    // Mapear campos de la API a campos del formulario
    const fieldMapping = {
      nombre_legal: "organization[nombre_legal]",
      nombre_fantasia: "organization[nombre_fantasia]",
      giro: "organization[giro]",
      actividades: "organization[actividades]",
      direccion: "organization[direccion]",
      codigo_postal: "organization[codigo_postal]",
      ciudad: "organization[ciudad]",
      region: "organization[region]",
      comuna: "organization[comuna]",
      email: "organization[email]",
      telefono: "organization[telefono]"
    };

    // Rellenar los campos del formulario
    Object.entries(fieldMapping).forEach(([apiField, formField]) => {
      const input = form.querySelector(`[name="${formField}"]`);
      if (input && fiscalData[apiField] !== undefined) {
        input.value = fiscalData[apiField] || "";
      }
    });

    // Disparar eventos de cambio para selects
    form.querySelectorAll('select').forEach(select => {
      select.dispatchEvent(new Event('change', { bubbles: true }));
    });
  }

  /**
   * Muestra un mensaje en el formulario fiscal
   * @param {string} message - Mensaje a mostrar
   * @param {string} type - Tipo de mensaje (success, error, warning, info)
   */
  showFiscalMessage(message, type = "info") {
    if (!this.hasFiscalMessageTarget) return;

    // Limpiar clases de mensajes anteriores
    this.fiscalMessageTarget.classList.remove(
      "bg-green-50", "border-green-400", "text-green-700", 
      "bg-red-50", "border-red-400", "text-red-700", 
      "bg-yellow-50", "border-yellow-400", "text-yellow-700",
      "bg-blue-50", "border-blue-400", "text-blue-700"
    );

    // Añadir nuevas clases según el tipo
    let bgColor, borderColor, textColor, icon;
    switch (type) {
      case "success":
        bgColor = "bg-green-50";
        borderColor = "border-green-400";
        textColor = "text-green-700";
        icon = "ri-checkbox-circle-line";
        break;
      case "error":
        bgColor = "bg-red-50";
        borderColor = "border-red-400";
        textColor = "text-red-700";
        icon = "ri-error-warning-line";
        break;
      case "warning":
        bgColor = "bg-yellow-50";
        borderColor = "border-yellow-400";
        textColor = "text-yellow-700";
        icon = "ri-alert-line";
        break;
      default: // info
        bgColor = "bg-blue-50";
        borderColor = "border-blue-400";
        textColor = "text-blue-700";
        icon = "ri-information-line";
    }

    this.fiscalMessageTarget.classList.add(
      bgColor, borderColor, textColor, "border-l-4", "p-4", "my-4", "rounded"
    );

    // Establecer contenido del mensaje
    this.fiscalMessageTarget.innerHTML = `
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="${icon} text-xl"></i>
        </div>
        <div class="ml-3">
          <p>${message}</p>
        </div>
      </div>
    `;

    // Mostrar mensaje
    this.fiscalMessageTarget.classList.remove("hidden");

    // Ocultar después de 5 segundos para mensajes de éxito
    if (type === "success") {
      setTimeout(() => {
        this.fiscalMessageTarget.classList.add("hidden");
      }, 5000);
    }
  }
}
