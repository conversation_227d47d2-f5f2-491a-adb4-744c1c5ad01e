// Importar y registrar controladores de la carpeta fiscal
import { application } from "../application";
import FiscalSimplifiedRutController from "./simplified_rut_controller";

// Registrar controladores de forma explícita
application.register("fiscal-simplified-rut", FiscalSimplifiedRutController);

// Mensaje de depuración
console.log("Controladores fiscales registrados:", 
  Array.from(application.controllers.keys())
    .filter(key => key.startsWith("fiscal-"))
    .join(", ")
);
