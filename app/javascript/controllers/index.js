console.log("[Controllers] 🚀 Esperando Stimulus y registrando controladores...");

// Función para esperar a que Stimulus esté disponible
async function waitForStimulusAndRegister() {
  console.log("[Controllers] 🔄 Esperando a que window.Stimulus esté disponible...");

  // Esperar hasta que window.Stimulus esté disponible
  let attempts = 0;
  const maxAttempts = 50; // 5 segundos máximo

  while (!window.Stimulus && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
    if (attempts % 10 === 0) {
      console.log(`[Controllers] ⏳ Esperando Stimulus... intento ${attempts}/${maxAttempts}`);
    }
  }

  if (!window.Stimulus) {
    console.error("[Controllers] ❌ Timeout: window.Stimulus no está disponible después de esperar");
    return;
  }

  console.log("[Controllers] ✅ window.Stimulus disponible, registrando controladores...");

  try {
    // Hello Controller
    const HelloModule = await import("./hello_controller");
    const HelloController = HelloModule.default;
    window.Stimulus.register("hello", HelloController);
    console.log("[Controllers] ✅ Controlador 'hello' registrado");

    // Simplified RUT Controller
    const SimplifiedRutModule = await import("./simplified_rut_controller");
    const SimplifiedRutController = SimplifiedRutModule.default;
    window.Stimulus.register("simplified-rut", SimplifiedRutController);
    console.log("[Controllers] ✅ Controlador 'simplified-rut' registrado");

    console.log("[Controllers] ✅ Todos los controladores registrados exitosamente");

    // Verificar controladores registrados
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] 📋 Controladores finales disponibles:", controllers);

  } catch (error) {
    console.error("[Controllers] ❌ Error al registrar controladores:", error);
  }
}

// Iniciar el proceso de registro
waitForStimulusAndRegister();

console.log("[Controllers] ✅ Todos los controladores registrados correctamente");

// Verificar en DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] 📄 DOM cargado, verificación final...");

  if (window.Stimulus) {
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores disponibles:", controllers);
    console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
  } else {
    console.warn("[Controllers] ⚠️ window.Stimulus no disponible");
  }
});
