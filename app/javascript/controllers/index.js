console.log("[Controllers] Iniciando carga de controladores...");

// Importar la aplicación Stimulus
import { application } from "./application"

console.log("[Controllers] Aplicación Stimulus importada:", application);

// Importar controladores manualmente (compatible con importmaps)
import Hello<PERSON><PERSON>roller from "./hello_controller"
import SimplifiedRutController from "./simplified_rut_controller"

console.log("[Controllers] Controladores importados:", { HelloController, SimplifiedRutController });

// Registrar controladores manualmente
try {
  application.register("hello", HelloController)
  console.log("[Controllers] ✅ Controlador 'hello' registrado");

  application.register("simplified-rut", SimplifiedRutController)
  console.log("[Controllers] ✅ Controlador 'simplified-rut' registrado");

  console.log("[Controllers] ✅ Todos los controladores registrados correctamente");
} catch (error) {
  console.error("[Controllers] ❌ Error al registrar controladores:", error);
}

// Mensaje de depuración
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] DOM cargado, verificando controladores registrados...");
  const controllers = Array.from(application.router?.modulesByIdentifier?.keys() || []);
  console.log("[Controllers] Controladores disponibles:", controllers);

  if (controllers.length === 0) {
    console.warn("[Controllers] ⚠️ No se encontraron controladores registrados");
  } else {
    console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
  }
});
