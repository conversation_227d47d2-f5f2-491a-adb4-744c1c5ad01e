// Importar la aplicación Stimulus
import { application } from "./application"

// Configurar Stimulus para auto-registro de controladores
import { registerControllers } from "@hotwired/stimulus-loading"

// Registrar automáticamente todos los controladores en esta carpeta
registerControllers(application, import.meta.glob("./**/*_controller.js", { eager: true }))

// Mensaje de depuración
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] Controladores registrados automáticamente:",
    Array.from(application.router?.modulesByIdentifier?.keys() || []).join(", ")
  );
});
