console.log("[Controllers] 🚀 Registro manual de controladores (Rails 8 + Propshaft)...");

// Usar window.Stimulus directamente (ya inicializado en application.js)
if (!window.Stimulus) {
  console.error("[Controllers] ❌ window.Stimulus no está disponible");
} else {
  console.log("[Controllers] ✅ window.Stimulus disponible:", window.Stimulus);

  // Registrar controladores manualmente (compatible con Propshaft)
  // Importar directamente desde los archivos

  // Hello Controller
  import("./hello_controller").then((module) => {
    const HelloController = module.default;
    window.Stimulus.register("hello", HelloController);
    console.log("[Controllers] ✅ Controlador 'hello' registrado");
  }).catch((error) => {
    console.warn("[Controllers] ⚠️ Error cargando hello_controller:", error);
  });

  // Simplified RUT Controller
  import("./simplified_rut_controller").then((module) => {
    const SimplifiedRutController = module.default;
    window.Stimulus.register("simplified-rut", SimplifiedRutController);
    console.log("[Controllers] ✅ Controlador 'simplified-rut' registrado");
  }).catch((error) => {
    console.warn("[Controllers] ⚠️ Error cargando simplified_rut_controller:", error);
  });
}

console.log("[Controllers] ✅ Registro de controladores iniciado");

console.log("[Controllers] ✅ Todos los controladores registrados correctamente");

// Verificar en DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] 📄 DOM cargado, verificación final...");

  if (window.Stimulus) {
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores disponibles:", controllers);
    console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
  } else {
    console.warn("[Controllers] ⚠️ window.Stimulus no disponible");
  }
});
