console.log("[Controllers] 🚀 Iniciando carga de controladores...");

// Importar la aplicación Stimulus
import { application } from "./application"

console.log("[Controllers] ✅ Aplicación Stimulus importada:", application);

// Importar controladores
import HelloController from "./hello_controller"
import SimplifiedRutController from "./simplified_rut_controller"

console.log("[Controllers] ✅ Controladores importados:", { HelloController, SimplifiedRutController });

// Registrar controladores
application.register("hello", HelloController)
console.log("[Controllers] ✅ Controlador 'hello' registrado");

application.register("simplified-rut", SimplifiedRutController)
console.log("[Controllers] ✅ Controlador 'simplified-rut' registrado");

console.log("[Controllers] ✅ Todos los controladores registrados correctamente");

// Verificar en DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] 📄 DOM cargado, verificación final...");

  if (window.Stimulus) {
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores disponibles:", controllers);
    console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
  } else {
    console.warn("[Controllers] ⚠️ window.Stimulus no disponible");
  }
});
