console.log("[Controllers] 🚀 Auto-registro de controladores (Rails 8 estándar)...");

// Importar la aplicación Stimulus desde application.js
import { application } from "./application"

console.log("[Controllers] ✅ Aplicación Stimulus importada:", application);

// Función de auto-registro estándar de Rails 8
function eagerLoadControllersFrom(under, application) {
  console.log(`[Controllers] 🔍 Auto-cargando controladores desde: ${under}`);

  // En Rails 8, los controladores se auto-registran basándose en el nombre del archivo
  // Definir controladores manualmente para compatibilidad con Propshaft
  const controllerDefinitions = [
    { name: "hello", path: "controllers/hello_controller" },
    { name: "simplified-rut", path: "controllers/simplified_rut_controller" }
  ];

  controllerDefinitions.forEach(({ name, path }) => {
    import(path).then((module) => {
      const controller = module.default;
      application.register(name, controller);
      console.log(`[Controllers] ✅ Auto-registrado: ${name}`);
    }).catch((error) => {
      console.warn(`[Controllers] ⚠️ No se pudo auto-cargar ${name}:`, error);

      // Fallback: intentar con ruta relativa
      const relativePath = `./${name.replace('-', '_')}_controller`;
      import(relativePath).then((module) => {
        const controller = module.default;
        application.register(name, controller);
        console.log(`[Controllers] ✅ Registrado con fallback: ${name}`);
      }).catch((fallbackError) => {
        console.error(`[Controllers] ❌ Fallback falló para ${name}:`, fallbackError);
      });
    });
  });
}

// Ejecutar auto-registro
eagerLoadControllersFrom("controllers", application);

console.log("[Controllers] ✅ Auto-registro iniciado");

console.log("[Controllers] ✅ Todos los controladores registrados correctamente");

// Verificar en DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] 📄 DOM cargado, verificación final...");

  if (window.Stimulus) {
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores disponibles:", controllers);
    console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
  } else {
    console.warn("[Controllers] ⚠️ window.Stimulus no disponible");
  }
});
