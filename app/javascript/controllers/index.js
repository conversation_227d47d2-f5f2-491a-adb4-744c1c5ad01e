console.log("[Controllers] 🚀 Iniciando carga de controladores...");

// Función para registrar controladores cuando Stimulus esté listo
async function registerControllers() {
  console.log("[Controllers] Esperando a que Stimulus esté disponible...");

  // Esperar a que Stimulus esté disponible
  let attempts = 0;
  const maxAttempts = 50; // 5 segundos máximo

  while (!window.Stimulus && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
    console.log(`[Controllers] Intento ${attempts}/${maxAttempts} - Esperando Stimulus...`);
  }

  if (!window.Stimulus) {
    console.error("[Controllers] ❌ Stimulus no está disponible después de esperar");
    return;
  }

  console.log("[Controllers] ✅ Stimulus disponible, registrando controladores...");

  try {
    // Importar controladores
    const HelloController = (await import("./hello_controller")).default;
    const SimplifiedRutController = (await import("./simplified_rut_controller")).default;

    console.log("[Controllers] Controladores importados:", { HelloController, SimplifiedRutController });

    // Registrar controladores
    window.Stimulus.register("hello", HelloController);
    console.log("[Controllers] ✅ Controlador 'hello' registrado");

    window.Stimulus.register("simplified-rut", SimplifiedRutController);
    console.log("[Controllers] ✅ Controlador 'simplified-rut' registrado");

    console.log("[Controllers] ✅ Todos los controladores registrados correctamente");

    // Verificar controladores registrados
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores disponibles:", controllers);

  } catch (error) {
    console.error("[Controllers] ❌ Error al registrar controladores:", error);
  }
}

// Iniciar el proceso de registro
registerControllers();

// También verificar en DOMContentLoaded como fallback
document.addEventListener('DOMContentLoaded', () => {
  console.log("[Controllers] 📄 DOM cargado, verificación final...");

  if (window.Stimulus) {
    const controllers = Array.from(window.Stimulus.router?.modulesByIdentifier?.keys() || []);
    console.log("[Controllers] Controladores finales disponibles:", controllers);

    if (controllers.length === 0) {
      console.warn("[Controllers] ⚠️ No se encontraron controladores registrados, reintentando...");
      registerControllers();
    } else {
      console.log("[Controllers] ✅ Se encontraron", controllers.length, "controladores");
    }
  } else {
    console.warn("[Controllers] ⚠️ Stimulus aún no disponible en DOMContentLoaded");
  }
});
