# Estructura de Controladores Stimulus

Este directorio contiene los controladores Stimulus organizados por funcionalidad en subcarpetas para facilitar el mantenimiento y la escalabilidad.

## Estructura de Carpetas

```
controllers/
├── core/               # Controladores base y principales
│   ├── application.js  # Controlador base de la aplicación
│   └── hello_controller.js  # Controlador de ejemplo
├── fiscal/             # Controladores relacionados con datos fiscales
│   ├── rut_controller.js  # Formato, validación y búsqueda de RUT (legacy)
│   ├── simplified_rut_controller.js  # Versión simplificada y moderna
│   ├── rut_validation_controller.js  # Validación de RUT
│   └── fiscal_data_controller.js  # Gestión de datos fiscales
├── client/             # Controladores relacionados con clientes
│   ├── client_controller.js
│   ├── clients_loader_controller.js
│   ├── search_controller.js
│   └── search_results_controller.js
├── ui/                 # Controladores de interfaz de usuario
│   ├── modal_controller.js
│   ├── dropdown_controller.js
│   ├── sidebar_controller.js
│   ├── prevent_scroll_controller.js
│   ├── delete_confirmation_controller.js
│   └── form_controller.js
├── datatable/          # Controladores relacionados con tablas de datos
│   ├── datatable_controller.js
│   ├── datatable_grid_controller.js
│   ├── datatable_clients_bridge_controller.js
│   └── datatable_registry_controller.js
└── index.js            # Registro central de controladores
```

## Convención de Nombres

- Los archivos de controladores deben seguir el formato `nombre_controller.js`.
- Los identificadores en HTML deben usar kebab-case: `data-controller="nombre-controller"`.
- En el archivo index.js, los controladores se importan con PascalCase y se registran con kebab-case.

## Cómo Añadir un Nuevo Controlador

1. Crear el archivo del controlador en la subcarpeta correspondiente.
2. Importar el controlador en `index.js`.
3. Registrar el controlador en `index.js` con `application.register()`.
4. Utilizar en HTML con `data-controller="nombre-controller"`.

## Logs de Depuración

Se ha habilitado el modo de depuración para Stimulus:
```javascript
// Enable debug mode
application.debug = true
```

Además, los controladores incluyen logs detallados con formato visual para facilitar la depuración:
```javascript
console.log("%c[Controlador] Mensaje", "background: #color; color: white; padding: 2px 5px; border-radius: 3px;");
```
