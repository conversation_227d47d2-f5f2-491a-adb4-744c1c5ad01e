import { Controller } from "@hotwired/stimulus";
import UruguayanRutController from "./rut_validation_controller";
import FiscalApiService from "../../services/fiscal_api_service";

/**
 * Controlador simplificado para manejar RUT y datos fiscales
 * 
 * Este controlador maneja:
 * 1. Validación y formateo de RUT
 * 2. Consulta de datos fiscales
 * 3. Autocompletado del formulario
 * 4. Guardado tradicional (no automático)
 */
export default class extends Controller {
  static targets = [
    "input",           // Input del RUT
    "form",            // Formulario principal
    "fiscalForm",      // Formulario fiscal
    "spinner",         // Indicador de carga
    "error",           // Mensaje de error
    "errorContainer",  // Contenedor de error
    "submitBtn",       // Botón de envío
    "fetchButton",     // Botón para consultar datos fiscales
    "clearButton",     // Botón para limpiar el input
    "resultsContainer" // Contenedor de resultados
  ];
  
  static values = {
    formatRut: { type: Boolean, default: true },
    validateRut: { type: Boolean, default: true },
    namespace: { type: String, default: 'owner' }
  };
  
  connect() {
    console.log("%c[SimplifiedRut] Controlador conectado al elemento:", "background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px;");
    console.log(this.element);
    
    // Registrar los targets encontrados para depuración
    console.log('[SimplifiedRut] Targets disponibles:', {
      input: this.hasInputTarget ? 'Encontrado' : 'No encontrado',
      form: this.hasFormTarget ? 'Encontrado' : 'No encontrado',
      fiscalForm: this.hasFiscalFormTarget ? 'Encontrado' : 'No encontrado',
      fetchButton: this.hasFetchButtonTarget ? 'Encontrado' : 'No encontrado',
      spinner: this.hasSpinnerTarget ? 'Encontrado' : 'No encontrado',
      error: this.hasErrorTarget ? 'Encontrado' : 'No encontrado',
      errorContainer: this.hasErrorContainerTarget ? 'Encontrado' : 'No encontrado',
      clearButton: this.hasClearButtonTarget ? 'Encontrado' : 'No encontrado',
      resultsContainer: this.hasResultsContainerTarget ? 'Encontrado' : 'No encontrado'
    });
    
    // Verificar si el botón de fetch está presente
    if (this.hasFetchButtonTarget) {
      console.log('[SimplifiedRut] Botón de fetch encontrado:', this.fetchButtonTarget);
      this.fetchButtonTarget.addEventListener('click', (e) => {
        console.log('[SimplifiedRut] Click en el botón de fetch detectado');
        this.fetchFiscalData(e);
      });
    } else {
      console.warn('[SimplifiedRut] No se encontró el botón de fetch');
    }
    
    // Configurar el input para formateo automático
    if (this.hasInputTarget) {
      console.log('[SimplifiedRut] Input target encontrado:', this.inputTarget);
      this.inputTarget.addEventListener('input', this.formatRut.bind(this));
      this.inputTarget.addEventListener('keypress', this.handleKeypress.bind(this));
    } else {
      console.warn('[SimplifiedRut] Input target no encontrado');
    }
    
    // Si hay un formulario fiscal, agregar manejador de evento submit
    if (this.hasFiscalFormTarget) {
      console.log('[SimplifiedRut] Fiscal form target encontrado:', this.fiscalFormTarget);
      this.fiscalFormTarget.addEventListener('submit', this.handleFiscalFormSubmit.bind(this));
    } else {
      console.warn('[SimplifiedRut] Fiscal form target no encontrado');
    }
    
    // Verificar si el botón de fetch está disponible
    if (this.hasFetchButtonTarget) {
      console.log('[SimplifiedRut] Fetch button encontrado:', this.fetchButtonTarget);
    } else {
      console.warn('[SimplifiedRut] Fetch button target no encontrado');
    }
  }
  
  disconnect() {
    if (this.hasInputTarget) {
      this.inputTarget.removeEventListener('input', this.formatRut);
      this.inputTarget.removeEventListener('keypress', this.handleKeypress);
    }
    
    if (this.hasFiscalFormTarget) {
      this.fiscalFormTarget.removeEventListener('submit', this.handleFiscalFormSubmit);
    }
  }
  
  /**
   * Maneja la tecla Enter en el campo RUT
   * @param {Event} event - Evento de tecla
   */
  handleKeypress(event) {
    // Si es Enter, buscar datos fiscales
    if (event.key === 'Enter') {
      event.preventDefault();
      this.fetchFiscalData();
    }
  }
  
  /**
   * Formatear el RUT uruguayo mientras se escribe
   * @param {Event} event - Evento de entrada
   */
  formatRut(event) {
    if (!this.formatRutValue) return;
    
    const input = event.target;
    let value = input.value;
    
    // Limpiar caracteres no numéricos (excepto guiones)
    value = value.replace(/[^0-9\-]/g, '');
    
    // Formatear RUT con guión antes del dígito verificador
    if (value.length > 0) {
      // Eliminar guiones existentes
      value = value.replace(/\-/g, '');
      
      // Si tiene al menos 2 caracteres, agregar guión
      if (value.length >= 2) {
        value = value.substring(0, value.length - 1) + '-' + value.substring(value.length - 1);
      }
    }
    
    // Actualizar el valor del input
    input.value = value;
    
    // Validar el RUT
    if (this.validateRutValue && value.length >= 2) {
      this.validateRut(value);
    } else {
      this.clearError();
    }
    
    // Mostrar u ocultar el botón de limpiar
    if (this.hasClearButtonTarget) {
      this.clearButtonTarget.style.display = value.length > 0 ? 'flex' : 'none';
    }
  }
  
  /**
   * Validar formato de RUT uruguayo
   * @param {string} rut - RUT a validar
   */
  validateRut(rut) {
    const rutValidator = new UruguayanRutController();
    const isValid = rutValidator.validate(rut);
    
    if (!isValid) {
      this.showError('El RUT no tiene un formato válido');
    } else {
      this.clearError();
    }
    
    return isValid;
  }
  
  /**
   * Limpiar RUT (remover caracteres especiales)
   * @param {string} rut - RUT a limpiar
   * @returns {string} RUT limpio
   */
  cleanRut(rut) {
    return UruguayanRutController.cleanRut(rut || '');
  }
  
  /**
   * Consultar datos fiscales a partir del RUT
   */
  /**
 * Consultar datos fiscales a partir del RUT
 */
async fetchFiscalData() {
  console.log('%c[SimplifiedRut] fetchFiscalData llamado', 'background: #2196F3; color: white; padding: 2px 5px; border-radius: 3px;');
  
  if (!this.hasInputTarget) {
    console.error('[SimplifiedRut] No se encontró el target de input de RUT');
    this.showError('Error: No se pudo encontrar el campo de RUT');
    return;
  }
  
  const rut = this.inputTarget.value.trim();
  console.log('[SimplifiedRut] RUT a consultar:', rut);
  
  // Validar que haya un RUT
  if (!rut) {
    this.showError('Por favor ingrese un RUT');
    return;
  }
  
  // Validar el formato del RUT
  if (this.validateRutValue && !this.validateRut(rut)) {
    return;
  }
  
  // Mostrar spinner
  this.showLoading(true);
  
  try {
    console.log(`Consultando datos fiscales para RUT: ${rut}`);
    
    // Obtener el ID del cliente desde la URL
    const clientId = window.location.pathname.split('/').filter(Boolean).pop();
    
    // Crear instancia del servicio fiscal con el namespace correcto
    const fiscalService = new FiscalApiService({
      namespace: this.namespaceValue
    });
    
    // Consultar datos fiscales usando el servicio
    const data = await fiscalService.fetchFiscalData(rut, clientId);
    
    console.log('Datos recibidos de la API:', data);  // DEBUG
    
    if (!data || !data.success) {
      throw new Error(data?.message || 'No se pudieron obtener datos fiscales');
    }
    
    // Autocompletar el formulario
    this.autocompleteFiscalForm(data);
    
    // Mostrar mensaje de éxito
    this.showSuccess('Datos fiscales cargados correctamente');
  } catch (error) {
    console.error('Error al consultar datos fiscales:', error);
    this.showError(error.message || 'Error al consultar datos fiscales');
  } finally {
    this.showLoading(false);
  }
}
  
  /**
   * Autocompletar formulario de datos fiscales
   * @param {Object} fiscalData - Datos fiscales recibidos
   */
  autocompleteFiscalForm(fiscalData) {
    if (!this.hasFiscalFormTarget) {
      console.error('No se encontró el formulario fiscal');
      return;
    }
    
    console.log('Datos fiscales recibidos para autocompletado:', fiscalData);
    
    const form = this.fiscalFormTarget;
    
    // Mapeo simple de campos
    const fieldMapping = {
      'organization[nombre_legal]': fiscalData.nombre_legal || fiscalData.razon_social,
      'organization[nombre_fantasia]': fiscalData.nombre_fantasia,
      'organization[direccion]': fiscalData.direccion,
      'organization[ciudad]': fiscalData.ciudad,
      'organization[departamento]': fiscalData.departamento,
      'organization[telefono]': fiscalData.telefono,
      'organization[email]': fiscalData.email,
      'organization[actividades]': fiscalData.actividades,
      'organization[estado]': fiscalData.estado
    };
    
    // Recorrer todos los campos del mapeo
    Object.entries(fieldMapping).forEach(([fieldName, value]) => {
      // Buscar el campo en el formulario
      const field = form.querySelector(`[name="${fieldName}"]`);
      
      if (field) {
        // Si es un array, convertirlo a string para campos de texto
        if (Array.isArray(value)) {
          field.value = value.join(', ');
        } else if (value !== undefined && value !== null) {
          field.value = value;
        }
      }
    });
    
    console.log('Formulario autocompletado');
  }
  
  /**
   * Manejar el envío del formulario fiscal
   * @param {Event} event - Evento de envío
   */
  async handleFiscalFormSubmit(event) {
    event.preventDefault();
    
    if (!this.hasFiscalFormTarget) {
      console.error('No se encontró el formulario fiscal');
      return;
    }
    
    // Mostrar indicador de carga
    this.showLoading(true);
    
    try {
      const form = this.fiscalFormTarget;
      
      // Crear un FormData con los datos del formulario
      const formData = new FormData(form);
      
      // Obtener el token CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
      if (!csrfToken) {
        console.warn('No se encontró token CSRF');
      }
      
      // Enviar la solicitud al servidor
      const response = await fetch(form.action, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': csrfToken || '',
          'Accept': 'application/json'
        },
        body: formData,
        credentials: 'same-origin'
      });
      
      // Verificar si la respuesta es correcta
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      
      // Intentar parsear la respuesta como JSON
      let data;
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          data = { success: true };
        }
      } catch (error) {
        console.error('Error al parsear respuesta:', error);
        data = { success: true };  // Asumimos éxito si no podemos parsear la respuesta
      }
      
      // Mostrar mensaje de éxito
      this.showSuccess('¡Datos fiscales guardados correctamente!');
      
    } catch (error) {
      console.error('Error al guardar datos fiscales:', error);
      this.showError(error.message || 'Error al guardar los datos fiscales');
    } finally {
      this.showLoading(false);
    }
  }
  
  /**
   * Mostrar/ocultar indicador de carga
   * @param {boolean} show - Indica si mostrar u ocultar
   */
  showLoading(show = true) {
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.style.display = show ? 'inline-block' : 'none';
    }
    
    // También deshabilitar botón de submit si está presente
    if (this.hasSubmitBtnTarget) {
      this.submitBtnTarget.disabled = show;
    }

    // Si hay un contenedor fiscal-spinner global en la página
    const fiscalSpinner = document.getElementById('fiscal-spinner');
    if (fiscalSpinner) {
      fiscalSpinner.classList.toggle('hidden', !show);
    }
  }
  
  /**
   * Mostrar mensaje de error
   * @param {string} message - Mensaje a mostrar
   */
  showError(message) {
    console.error('Error:', message);
    
    // Limpiar mensajes anteriores
    this.clearMessages();
    
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message;
      this.errorTarget.classList.remove('hidden');
      this.errorTarget.classList.add('mt-2', 'p-2', 'text-sm', 'font-medium', 'text-red-700', 'bg-red-100', 'rounded');
    }
    
    // También usar el contenedor de error si está disponible
    if (this.hasErrorContainerTarget) {
      const errorElement = document.createElement('div');
      errorElement.className = 'p-2 text-sm font-medium text-red-700 bg-red-100 rounded';
      errorElement.textContent = message;
      
      this.errorContainerTarget.innerHTML = '';
      this.errorContainerTarget.appendChild(errorElement);
      this.errorContainerTarget.classList.remove('hidden');
      
      // Ocultar mensaje después de 5 segundos
      setTimeout(() => {
        this.errorContainerTarget.innerHTML = '';
        this.errorContainerTarget.classList.add('hidden');
      }, 5000);
    }
  }
  
  /**
   * Mostrar mensaje de éxito
   * @param {string} message - Mensaje a mostrar
   */
  showSuccess(message) {
    console.log('Éxito:', message);
    
    // Limpiar mensajes anteriores
    this.clearMessages();
    
    // Crear elemento de mensaje de éxito
    const successElement = document.createElement('div');
    successElement.className = 'mt-2 p-2 text-sm font-medium text-green-700 bg-green-100 rounded';
    successElement.textContent = message;
    
    // Insertar en el DOM
    if (this.hasInputTarget && this.inputTarget.parentNode) {
      this.inputTarget.parentNode.insertBefore(successElement, this.inputTarget.nextSibling);
    } else if (this.element) {
      this.element.appendChild(successElement);
    }
    
    // Eliminar después de 5 segundos
    setTimeout(() => {
      if (successElement.parentNode) {
        successElement.parentNode.removeChild(successElement);
      }
    }, 5000);
  }
  
  /**
   * Limpiar mensaje de error
   */
  clearError() {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = '';
      this.errorTarget.classList.add('hidden');
    }
    
    if (this.hasErrorContainerTarget) {
      this.errorContainerTarget.innerHTML = '';
      this.errorContainerTarget.classList.add('hidden');
    }
  }
  
  /**
   * Limpiar todos los mensajes
   */
  clearMessages() {
    // Limpiar mensaje de error
    this.clearError();
    
    // Eliminar mensajes de éxito
    const successElements = this.element.querySelectorAll('.text-green-700');
    successElements.forEach(element => element.remove());
  }
  
  /**
   * Limpia el campo RUT
   */
  clear() {
    if (this.hasInputTarget) {
      this.inputTarget.value = '';
      this.inputTarget.focus();
      
      // Ocultar el botón de limpiar
      if (this.hasClearButtonTarget) {
        this.clearButtonTarget.style.display = 'none';
      }
      
      // Limpiar mensajes y resultados
      this.clearError();
      
      if (this.hasResultsContainerTarget) {
        this.resultsContainerTarget.innerHTML = '';
        this.resultsContainerTarget.classList.add('hidden');
      }
    }
  }
}
