import { Controller } from "@hotwired/stimulus"

/**
 * Controlador Stimulus para manejar confirmaciones de eliminación
 * que requieren que el usuario escriba una palabra clave específica.
 *
 * Uso:
 * <form data-controller="delete-confirmation" 
 *       data-delete-confirmation-keyword-value="ELIMINAR"
 *       data-delete-confirmation-message-value="Escribe ELIMINAR para confirmar">
 *   <input type="text" data-delete-confirmation-target="input">
 *   <button type="submit" data-delete-confirmation-target="submit">Eliminar</button>
 *   <div data-delete-confirmation-target="error" class="hidden text-red-600"></div>
 * </form>
 */
export default class extends Controller {
  static targets = ["input", "submit", "error"]
  static values = {
    keyword: String,
    message: { type: String, default: "Por favor, escribe la palabra clave para confirmar" }
  }

  connect() {
    // Deshabilitar el botón de envío por defecto
    this.submitTarget.disabled = true
    
    // Agregar un campo oculto para la confirmación
    const hiddenInput = document.createElement("input")
    hiddenInput.type = "hidden"
    hiddenInput.name = "confirmation"
    hiddenInput.value = ""
    this.element.appendChild(hiddenInput)
    this.hiddenInput = hiddenInput
    
    // Mostrar mensaje de instrucción
    this.errorTarget.textContent = this.messageValue
    this.errorTarget.classList.remove("hidden")
  }

  // Verificar la entrada del usuario en cada cambio
  checkInput() {
    const inputValue = this.inputTarget.value
    this.hiddenInput.value = inputValue
    
    if (inputValue === this.keywordValue) {
      this.submitTarget.disabled = false
      this.errorTarget.classList.add("hidden")
    } else {
      this.submitTarget.disabled = true
      this.errorTarget.classList.remove("hidden")
      this.errorTarget.textContent = `Por favor, escribe "${this.keywordValue}" para confirmar`
    }
  }
}
