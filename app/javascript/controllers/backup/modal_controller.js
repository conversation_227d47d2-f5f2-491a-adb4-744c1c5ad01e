import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Modal is hidden by default
    this.element.setAttribute('aria-modal', 'true')
    this.element.classList.add('hidden')
    
    // Add fixed elements to prevent scrolling when modal is open
    document.body.classList.add('overflow-hidden')
    
    // Handle ESC key
    this.handleKeyup = this.handleKeyup.bind(this)
    document.addEventListener('keyup', this.handleKeyup)
  }

  disconnect() {
    document.removeEventListener('keyup', this.handleKeyup)
    document.body.classList.remove('overflow-hidden')
  }

  handleKeyup(event) {
    if (event.key === "Escape") {
      this.close()
    }
  }

  open(event) {
    if (event) {
      event.preventDefault()
    }
    
    // If there's a modal URL, fetch content
    const url = event?.currentTarget?.dataset?.modalUrl
    if (url) {
      this.fetchContent(url)
    }
    
    // Show modal
    this.element.classList.remove('hidden')
  }

  close() {
    // Hide modal
    this.element.classList.add('hidden')
    document.body.classList.remove('overflow-hidden')
  }

  async fetchContent(url) {
    try {
      const response = await fetch(url, {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const content = await response.text()
        const modalContentEl = this.element.querySelector('[data-modal-target="content"]')
        if (modalContentEl) {
          modalContentEl.innerHTML = content
        }
      } else {
        console.error('Failed to fetch modal content:', response.status)
      }
    } catch (error) {
      console.error('Error fetching modal content:', error)
    }
  }
}
