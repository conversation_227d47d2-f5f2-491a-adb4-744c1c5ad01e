import { Controller } from "@hotwired/stimulus"

// Controlador para el componente DataTable
// Maneja la búsqueda, filtrado, ordenación y paginación de tablas de datos
export default class extends Controller {
  static targets = [
    "table",        // La tabla completa
    "row",          // Filas de datos
    "searchInput",  // Campo de búsqueda (mantenido para compatibilidad)
    "filterSelect", // Selectores de filtro
    "pagination",   // Contenedor de paginación
    "pageButton",   // Botones de página
    "prevButton",   // Botón de página anterior
    "nextButton",   // Botón de página siguiente
    "noResults",    // Mensaje de no resultados
    "sortHeader",   // Encabezados de columna ordenables
    "checkAll",     // Checkbox para seleccionar todas las filas
    "checkRow"      // Checkbox para seleccionar una fila
  ]

  static values = {
    perPage: { type: Number, default: 10 },
    currentPage: { type: Number, default: 1 },
    sortColumn: { type: String, default: "" },
    sortDirection: { type: String, default: "asc" },
    tableId: { type: String, default: "" }
  }

  connect() {
    console.log("DataTable conectado")
    console.log("Targets disponibles:", {
      table: this.hasTableTarget,
      row: this.hasRowTarget,
      searchInput: this.hasSearchInputTarget,
      filterSelect: this.hasFilterSelectTarget,
      pagination: this.hasPaginationTarget,
      pageButton: this.hasPageButtonTarget,
      prevButton: this.hasPrevButtonTarget,
      nextButton: this.hasNextButtonTarget,
      noResults: this.hasNoResultsTarget,
      sortHeader: this.hasSortHeaderTarget,
      checkAll: this.hasCheckAllTarget,
      checkRow: this.hasCheckRowTarget
    })

    // Inicializar el DataTable
    this.loadPreferences()
    this.filterRows()
    this.updatePagination()
    this.updateSortIndicators()
  }

  // Cargar preferencias guardadas
  loadPreferences() {
    if (!this.tableIdValue) return

    try {
      const preferences = JSON.parse(localStorage.getItem(`datatable_${this.tableIdValue}`))
      if (preferences) {
        if (preferences.perPage) this.perPageValue = preferences.perPage
        if (preferences.sortColumn) this.sortColumnValue = preferences.sortColumn
        if (preferences.sortDirection) this.sortDirectionValue = preferences.sortDirection
      }
    } catch (e) {
      console.error("Error loading datatable preferences:", e)
    }
  }

  // Guardar preferencias actuales
  savePreferences() {
    if (!this.tableIdValue) return

    const preferences = {
      perPage: this.perPageValue,
      sortColumn: this.sortColumnValue,
      sortDirection: this.sortDirectionValue
    }

    localStorage.setItem(`datatable_${this.tableIdValue}`, JSON.stringify(preferences))
  }

  // Método para filtrar filas basado en búsqueda y filtros
  filterRows() {
    console.log("Filtrando filas...")

    // Obtener el término de búsqueda (normalizado)
    const searchTerm = this.hasSearchInputTarget ? this.normalizeText(this.searchInputTarget.value) : ""
    console.log("Término de búsqueda:", searchTerm)

    // Obtener filtros activos
    const activeFilters = this.getActiveFilters()
    console.log("Filtros activos:", activeFilters)

    let visibleCount = 0

    // Recorrer todas las filas y aplicar filtros
    this.rowTargets.forEach(row => {
      // Verificar si la fila coincide con el término de búsqueda
      const matchesSearch = searchTerm === "" || this.rowContainsText(row, searchTerm)

      // Verificar si la fila coincide con los filtros activos
      const matchesFilters = this.rowMatchesFilters(row, activeFilters)

      // Mostrar u ocultar la fila según los resultados
      if (matchesSearch && matchesFilters) {
        row.dataset.filtered = "false"
        visibleCount++
      } else {
        row.dataset.filtered = "true"
      }

      // Depuración
      if (searchTerm !== "" && !matchesSearch) {
        console.log("Fila no coincide con búsqueda:", row.dataset.searchableText)
      }

      if (Object.keys(activeFilters).length > 0 && !matchesFilters) {
        console.log("Fila no coincide con filtros:", row.dataset)
      }
    })

    console.log(`Filas visibles después de filtrar: ${visibleCount} de ${this.rowTargets.length}`)

    // Mostrar mensaje de no resultados si es necesario
    if (this.hasNoResultsTarget) {
      if (visibleCount === 0) {
        this.noResultsTarget.classList.remove("hidden")
      } else {
        this.noResultsTarget.classList.add("hidden")
      }
    }

    // Aplicar ordenación si hay una columna seleccionada
    if (this.sortColumnValue) {
      this.sortRows()
    }

    // Resetear a la primera página después de filtrar
    this.currentPageValue = 1
    this.applyPagination()

    // Actualizar contadores de resultados
    this.updateResultCounters(visibleCount)
  }

  // Ordenar las filas según la columna y dirección seleccionadas
  sortRows() {
    if (!this.sortColumnValue) return

    const filteredRows = Array.from(this.rowTargets).filter(row => row.dataset.filtered === "false")
    const tbody = this.tableTarget.querySelector('tbody')

    // Ordenar las filas
    filteredRows.sort((a, b) => {
      const aValue = a.dataset[`sort${this.sortColumnValue}`] || a.dataset[this.sortColumnValue] || ""
      const bValue = b.dataset[`sort${this.sortColumnValue}`] || b.dataset[this.sortColumnValue] || ""

      // Determinar si los valores son numéricos
      const aNum = !isNaN(parseFloat(aValue))
      const bNum = !isNaN(parseFloat(bValue))

      // Si ambos son números, comparar como números
      if (aNum && bNum) {
        return this.sortDirectionValue === "asc"
          ? parseFloat(aValue) - parseFloat(bValue)
          : parseFloat(bValue) - parseFloat(aValue)
      }

      // De lo contrario, comparar como strings
      return this.sortDirectionValue === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    })

    // Reordenar las filas en el DOM
    filteredRows.forEach(row => {
      tbody.appendChild(row)
    })
  }

  // Actualizar los indicadores de ordenación en los encabezados
  updateSortIndicators() {
    if (!this.hasSortHeaderTarget) return

    this.sortHeaderTargets.forEach(header => {
      // Limpiar clases e iconos anteriores
      header.classList.remove("text-indigo-600")

      // Obtener el ícono de ordenación
      const sortIcon = header.querySelector(".sort-icon")
      if (sortIcon) {
        sortIcon.innerHTML = `
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
          </svg>
        `
      }

      // Si esta es la columna ordenada actualmente
      if (header.dataset.sortColumn === this.sortColumnValue) {
        header.classList.add("text-indigo-600")

        if (sortIcon) {
          if (this.sortDirectionValue === "asc") {
            sortIcon.innerHTML = `
              <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
            `
          } else {
            sortIcon.innerHTML = `
              <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            `
          }
        }
      }
    })
  }

  // Actualizar los contadores de resultados mostrados
  updateResultCounters(totalFilteredRows) {
    const startItem = document.getElementById("start-item")
    const endItem = document.getElementById("end-item")
    const totalItems = document.getElementById("total-items")
    const currentPage = document.getElementById("current-page")
    const totalPages = document.getElementById("total-pages")

    if (startItem && endItem && totalItems) {
      if (totalFilteredRows === 0) {
        startItem.textContent = "0"
        endItem.textContent = "0"
      } else {
        const start = (this.currentPageValue - 1) * this.perPageValue + 1
        const end = Math.min(start + this.perPageValue - 1, totalFilteredRows)

        startItem.textContent = start
        endItem.textContent = end
      }

      totalItems.textContent = totalFilteredRows
    }

    // Actualizar información de página actual y total de páginas
    if (currentPage && totalPages) {
      const totalPagesValue = Math.ceil(totalFilteredRows / this.perPageValue)
      currentPage.textContent = this.currentPageValue
      totalPages.textContent = totalPagesValue > 0 ? totalPagesValue : 1
    }
  }

  // Verificar si una fila contiene el texto de búsqueda
  rowContainsText(row, searchTerm) {
    // Usar el atributo data-searchable-text si existe, de lo contrario usar el contenido de texto
    const searchableText = row.dataset.searchableText || row.textContent

    // Normalizar el texto (eliminar acentos, convertir a minúsculas)
    const normalizedText = this.normalizeText(searchableText)

    // Verificar si el texto normalizado contiene el término de búsqueda
    return normalizedText.includes(searchTerm)
  }

  // Normalizar texto para búsqueda (eliminar acentos, convertir a minúsculas)
  normalizeText(text) {
    return text.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Eliminar acentos
      .replace(/[^\w\s]/gi, '') // Eliminar caracteres especiales
  }

  // Verificar si una fila coincide con los filtros activos
  rowMatchesFilters(row, activeFilters) {
    // Si no hay filtros activos, todas las filas coinciden
    if (Object.keys(activeFilters).length === 0) return true

    // Verificar cada filtro activo
    for (const [filterName, filterValue] of Object.entries(activeFilters)) {
      if (filterValue === "") continue // Ignorar filtros vacíos

      const rowValue = row.dataset[filterName]
      if (!rowValue || rowValue !== filterValue) {
        return false
      }
    }

    return true
  }

  // Obtener los filtros activos de los selectores
  getActiveFilters() {
    const filters = {}

    if (this.hasFilterSelectTarget) {
      this.filterSelectTargets.forEach(select => {
        if (select.value) {
          filters[select.dataset.filterName] = select.value
        }
      })
    }

    return filters
  }

  // Aplicar paginación a las filas filtradas
  applyPagination() {
    if (!this.hasRowTarget) return

    console.log("Aplicando paginación...")

    // Primero, ocultar todas las filas filtradas
    this.rowTargets.forEach(row => {
      if (row.dataset.filtered === "true") {
        row.classList.add("hidden")
      }
    })

    // Luego, aplicar paginación a las filas no filtradas
    const filteredRows = this.rowTargets.filter(row => row.dataset.filtered === "false")
    const startIndex = (this.currentPageValue - 1) * this.perPageValue
    const endIndex = startIndex + this.perPageValue

    console.log(`Mostrando filas ${startIndex} a ${endIndex} de ${filteredRows.length} filas filtradas`)

    // Mostrar solo las filas de la página actual
    filteredRows.forEach((row, index) => {
      if (index >= startIndex && index < endIndex) {
        row.classList.remove("hidden")
      } else {
        row.classList.add("hidden")
      }
    })

    this.updatePagination(filteredRows.length)
  }

  // Actualizar los controles de paginación
  updatePagination(totalFilteredRows) {
    console.log("Método updatePagination llamado")
    console.log("¿Tiene target de paginación?", this.hasPaginationTarget)

    if (!this.hasPaginationTarget) {
      console.log("No se encontró el target de paginación, saliendo del método")
      return
    }

    console.log("Actualizando paginación...")
    console.log("Elemento de paginación:", this.paginationTarget)

    const totalRows = totalFilteredRows || this.rowTargets.filter(row => row.dataset.filtered === "false").length
    const totalPages = Math.ceil(totalRows / this.perPageValue)

    console.log(`Total de filas: ${totalRows}, Total de páginas: ${totalPages}, Página actual: ${this.currentPageValue}`)

    // Actualizar botones de paginación
    // Limpiar botones existentes
    const existingButtons = this.paginationTarget.querySelectorAll('[data-datatable-target="pageButton"]')
    existingButtons.forEach(button => button.remove())

    // Crear nuevos botones de página
    const maxVisiblePages = 5
    let startPage = Math.max(1, this.currentPageValue - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    // Botón de página anterior
    if (this.hasPrevButtonTarget) {
      if (this.currentPageValue > 1) {
        this.prevButtonTarget.classList.remove("opacity-50", "cursor-not-allowed")
        this.prevButtonTarget.disabled = false
      } else {
        this.prevButtonTarget.classList.add("opacity-50", "cursor-not-allowed")
        this.prevButtonTarget.disabled = true
      }
    }

    // Botones de página
    for (let i = startPage; i <= endPage; i++) {
      const pageButton = document.createElement("button")
      pageButton.type = "button"
      pageButton.dataset.datatableTarget = "pageButton"
      pageButton.dataset.action = "click->datatable#goToPage"
      pageButton.dataset.page = i
      pageButton.classList.add(
        "relative", "inline-flex", "items-center", "px-4", "py-2", "border", "border-gray-300",
        "text-sm", "font-medium", "text-gray-700", "focus:z-10", "focus:outline-none",
        "focus:ring-1", "focus:ring-indigo-500", "focus:border-indigo-500"
      )

      if (i === this.currentPageValue) {
        pageButton.classList.add("bg-indigo-50", "border-indigo-500", "text-indigo-600", "z-10")
      } else {
        pageButton.classList.add("bg-white", "hover:bg-gray-50")
      }

      pageButton.textContent = i

      // Insertar antes del botón siguiente
      if (this.hasNextButtonTarget) {
        this.paginationTarget.insertBefore(pageButton, this.nextButtonTarget)
      } else {
        this.paginationTarget.appendChild(pageButton)
      }
    }

    // Botón de página siguiente
    if (this.hasNextButtonTarget) {
      if (this.currentPageValue < totalPages) {
        this.nextButtonTarget.classList.remove("opacity-50", "cursor-not-allowed")
        this.nextButtonTarget.disabled = false
      } else {
        this.nextButtonTarget.classList.add("opacity-50", "cursor-not-allowed")
        this.nextButtonTarget.disabled = true
      }
    }

    // Siempre mostrar la información de paginación, pero ocultar los botones si solo hay una página
    if (totalPages <= 1) {
      // Ocultar solo los botones de navegación, no toda la paginación
      this.prevButtonTarget.classList.add("hidden")
      this.nextButtonTarget.classList.add("hidden")
    } else {
      this.prevButtonTarget.classList.remove("hidden")
      this.nextButtonTarget.classList.remove("hidden")
    }

    // Actualizar los contadores de resultados
    this.updateResultCounters(totalRows);
  }

  // Manejadores de eventos

  // Cuando se escribe en el campo de búsqueda (compatibilidad hacia atrás)
  search(event) {
    if (event) event.preventDefault();
    this.currentPageValue = 1;
    this.filterRows();
  }

  // Limpiar el campo de búsqueda (compatibilidad hacia atrás)
  clearSearch(event) {
    if (event) event.preventDefault();
    if (this.hasSearchInputTarget) {
      this.searchInputTarget.value = '';
      this.search();
    }
  }

  // Manejar búsqueda desde el controlador de búsqueda unificado
  handleSearch(event) {
    this.currentPageValue = 1;
    this.filterRows();
  }

  // Cuando se escribe en el campo de búsqueda
  handleSearchKeydown(event) {
    // Si se presiona Enter, realizar la búsqueda
    if (event.key === 'Enter') {
      event.preventDefault();
      // Cancelar cualquier búsqueda pendiente
      clearTimeout(this.searchTimeout);
      // Ejecutar la búsqueda inmediatamente
      this.filterRows();
    }
  }

  // Cuando se cambia un filtro
  filter() {
    this.filterRows()
  }

  // Ir a una página específica
  goToPage(event) {
    const page = parseInt(event.currentTarget.dataset.page)
    if (page !== this.currentPageValue) {
      this.currentPageValue = page
      this.applyPagination()
    }
  }

  // Ir a la página anterior
  prevPage() {
    if (this.currentPageValue > 1) {
      this.currentPageValue--
      this.applyPagination()
    }
  }

  // Ir a la página siguiente
  nextPage() {
    const totalRows = this.rowTargets.filter(row => row.dataset.filtered === "false").length
    const totalPages = Math.ceil(totalRows / this.perPageValue)

    if (this.currentPageValue < totalPages) {
      this.currentPageValue++
      this.applyPagination()
    }
  }

  // Cambiar el número de elementos por página
  changePerPage(event) {
    this.perPageValue = parseInt(event.target.value)
    this.currentPageValue = 1
    this.applyPagination()
  }

  // Métodos para selección múltiple

  // Seleccionar o deseleccionar todas las filas visibles
  toggleSelectAll() {
    if (!this.hasCheckAllTarget || !this.hasCheckRowTarget) return

    const isChecked = this.checkAllTarget.checked
    const visibleRows = this.rowTargets.filter(row =>
      row.dataset.filtered === "false" &&
      !row.classList.contains("hidden")
    )

    visibleRows.forEach(row => {
      const checkbox = row.querySelector('[data-datatable-target="checkRow"]')
      if (checkbox) {
        checkbox.checked = isChecked
      }
    })

    // Disparar evento personalizado
    this.dispatch("selectionChanged", {
      detail: {
        selectedRows: this.getSelectedRows()
      }
    })
  }

  // Actualizar el estado del checkbox "seleccionar todos"
  updateSelectAllState() {
    if (!this.hasCheckAllTarget || !this.hasCheckRowTarget) return

    const visibleRows = this.rowTargets.filter(row =>
      row.dataset.filtered === "false" &&
      !row.classList.contains("hidden")
    )

    const selectedRows = visibleRows.filter(row => {
      const checkbox = row.querySelector('[data-datatable-target="checkRow"]')
      return checkbox && checkbox.checked
    })

    // Actualizar el estado del checkbox "seleccionar todos"
    if (visibleRows.length === 0) {
      this.checkAllTarget.checked = false
      this.checkAllTarget.indeterminate = false
    } else if (selectedRows.length === 0) {
      this.checkAllTarget.checked = false
      this.checkAllTarget.indeterminate = false
    } else if (selectedRows.length === visibleRows.length) {
      this.checkAllTarget.checked = true
      this.checkAllTarget.indeterminate = false
    } else {
      this.checkAllTarget.checked = false
      this.checkAllTarget.indeterminate = true
    }

    // Disparar evento personalizado
    this.dispatch("selectionChanged", {
      detail: {
        selectedRows: this.getSelectedRows()
      }
    })
  }

  // Obtener las filas seleccionadas
  getSelectedRows() {
    if (!this.hasCheckRowTarget) return []

    return this.rowTargets.filter(row => {
      const checkbox = row.querySelector('[data-datatable-target="checkRow"]')
      return checkbox && checkbox.checked
    })
  }

  // Métodos para exportación de datos

  // Exportar datos a CSV
  exportCSV() {
    const headers = this.getColumnHeaders()
    const rows = this.getExportableRows()

    // Crear contenido CSV
    let csvContent = headers.join(",") + "\\n"

    rows.forEach(row => {
      const values = headers.map(header => {
        const cellValue = row[header] || ""
        // Escapar comillas y encerrar en comillas si contiene comas o saltos de línea
        if (cellValue.includes(",") || cellValue.includes("\\n") || cellValue.includes('"')) {
          return '"' + cellValue.replace(/"/g, '""') + '"'
        }
        return cellValue
      })
      csvContent += values.join(",") + "\\n"
    })

    // Descargar el archivo
    this.downloadFile(csvContent, "text/csv", "datatable_export.csv")
  }

  // Exportar datos a Excel (XLSX)
  exportExcel() {
    // Esta función requiere una biblioteca externa como SheetJS
    // Por simplicidad, usaremos CSV que Excel puede abrir
    this.exportCSV()
  }

  // Obtener los encabezados de las columnas
  getColumnHeaders() {
    const headers = []
    const headerRow = this.tableTarget.querySelector("thead tr")

    if (headerRow) {
      headerRow.querySelectorAll("th").forEach(th => {
        // Ignorar la columna de checkboxes y acciones
        if (!th.classList.contains("checkbox-column") && !th.classList.contains("actions-column")) {
          headers.push(th.textContent.trim())
        }
      })
    }

    return headers
  }

  // Obtener los datos de las filas para exportación
  getExportableRows() {
    const headers = this.getColumnHeaders()
    const rows = []

    // Determinar qué filas exportar (todas, filtradas o seleccionadas)
    const rowsToExport = this.getSelectedRows().length > 0
      ? this.getSelectedRows()
      : this.rowTargets.filter(row => row.dataset.filtered === "false")

    rowsToExport.forEach(row => {
      const rowData = {}

      // Obtener los datos de cada celda
      let cellIndex = 0
      row.querySelectorAll("td").forEach(td => {
        // Ignorar la columna de checkboxes y acciones
        if (!td.classList.contains("checkbox-cell") && !td.classList.contains("actions-cell")) {
          if (cellIndex < headers.length) {
            rowData[headers[cellIndex]] = td.textContent.trim()
            cellIndex++
          }
        }
      })

      rows.push(rowData)
    })

    return rows
  }

  // Descargar un archivo
  downloadFile(content, mimeType, filename) {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)

    const link = document.createElement("a")
    link.href = url
    link.download = filename
    link.style.display = "none"

    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }, 100)
  }

  // Manejadores de eventos adicionales

  // Ordenar por una columna
  sort(event) {
    const header = event.currentTarget
    const column = header.dataset.sortColumn

    if (this.sortColumnValue === column) {
      // Cambiar dirección si es la misma columna
      this.sortDirectionValue = this.sortDirectionValue === "asc" ? "desc" : "asc"
    } else {
      // Nueva columna, ordenar ascendente
      this.sortColumnValue = column
      this.sortDirectionValue = "asc"
    }

    this.updateSortIndicators()
    this.sortRows()
    this.applyPagination()
    this.savePreferences()
  }

  // Manejar cambio en checkbox de fila
  handleRowCheckboxChange() {
    this.updateSelectAllState()
  }
}
