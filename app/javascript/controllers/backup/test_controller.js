import { Controller } from "@hotwired/stimulus"

console.log('[TestController] Importando controlador de prueba...');

// Conecta con data-controller="test"
export default class extends Controller {
  static targets = ["message"]
  
  initialize() {
    console.log('[TestController] Inicializando controlador de prueba...');
  }
  
  connect() {
    console.log('[TestController] Conectando controlador de prueba...', this.element);
    
    if (this.hasMessageTarget) {
      console.log('[TestController] Target "message" encontrado');
      this.messageTarget.textContent = "¡Controlador de prueba funcionando!"
    } else {
      console.warn('[TestController] No se encontró el target "message"');
    }
    
    // Verificar que el botón está presente
    const button = this.element.querySelector('[data-action*="test#showAlert"]');
    if (button) {
      console.log('[TestController] Botón encontrado:', button);
    } else {
      console.warn('[TestController] No se encontró el botón con data-action');
    }
  }
  
  showAlert() {
    console.log('[TestController] Mostrando alerta...');
    alert("¡Botón de prueba funcionando!");
  }
}

console.log('[TestController] Controlador de prueba exportado');
