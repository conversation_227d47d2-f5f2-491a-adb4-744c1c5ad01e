import { Controller } from "@hotwired/stimulus"

/**
 * Controlador para manejar menús desplegables
 *
 * Este controlador se encarga de mostrar/ocultar un menú desplegable
 * y cerrar el menú cuando se hace clic fuera de él.
 */
export default class extends Controller {
  static targets = ["menu"]

  connect() {
    // Añadir un event listener para cerrar el menú cuando se hace clic fuera
    document.addEventListener("click", this.closeMenuOnClickOutside.bind(this))
  }

  disconnect() {
    // Eliminar el event listener cuando el controlador se desconecta
    document.removeEventListener("click", this.closeMenuOnClickOutside.bind(this))
  }

  /**
   * Alterna la visibilidad del menú desplegable
   */
  toggle(event) {
    event.stopPropagation()
    this.menuTarget.classList.toggle("hidden")
  }

  /**
   * Cierra el menú desplegable
   */
  close() {
    this.menuTarget.classList.add("hidden")
  }

  /**
   * Cierra el menú cuando se hace clic fuera de él
   */
  closeMenuOnClickOutside(event) {
    // Si el menú está visible y el clic fue fuera del controlador
    if (!this.menuTarget.classList.contains("hidden") && 
        !this.element.contains(event.target)) {
      this.close()
    }
  }
}
