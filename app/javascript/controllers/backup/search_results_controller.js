// app/javascript/controllers/search_results_controller.js
import { Controller } from "@hotwired/stimulus"

// Controlador para manejar los resultados de búsqueda con Turbo Frames
export default class extends Controller {
  static targets = ["results"]
  
  // Manejar la respuesta del servidor
  handleResponse(event) {
    const response = event.detail.fetchResponse
    
    // Si hay un error en la respuesta
    if (!response || response.status >= 400) {
      this.showError()
      event.preventDefault() // Evitar que Turbo maneje la respuesta
    }
    
    // Podemos agregar lógica adicional aquí si es necesario
  }
  
  // Mostrar estado de carga
  showLoading() {
    this.element.innerHTML = `
      <div class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-700">Buscando clientes...</span>
      </div>
    `
  }
  
  // Mostrar error
  showError() {
    const errorTemplate = document.getElementById('error-template')
    if (errorTemplate) {
      this.element.innerHTML = errorTemplate.innerHTML
    } else {
      this.element.innerHTML = `
        <div class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error en la búsqueda</h3>
              <div class="mt-2 text-sm text-red-700">
                <p>No se pudo completar la búsqueda. Por favor, intente nuevamente.</p>
              </div>
            </div>
          </div>
        </div>
      `
    }
  }
  
  // Mostrar mensaje de sin resultados
  showNoResults() {
    const noResultsTemplate = document.getElementById('no-results-template')
    if (noResultsTemplate) {
      this.element.innerHTML = noResultsTemplate.innerHTML
    } else {
      this.element.innerHTML = `
        <div class="text-center py-8">
          <p class="text-gray-500">No se encontraron resultados para la búsqueda.</p>
        </div>
      `
    }
  }
  
  // Actualizar los resultados con la respuesta del servidor
  updateResults(html) {
    this.element.innerHTML = html
  }
}
