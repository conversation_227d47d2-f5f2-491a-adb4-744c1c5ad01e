// app/javascript/controllers/client_controller.js
import { Controller } from "@hotwired/stimulus"

// Controlador para manejar las acciones de clientes
export default class extends Controller {
  static targets = ['button', 'icon']
  static values = {
    url: String,
    active: Boolean,
    loading: { type: Boolean, default: false }
  }

  connect() {
    this.updateButtonState()
  }

  // Alternar el estado activo del cliente
  toggleActive(event) {
    if (event) event.preventDefault()
    
    this.loading = true
    this.updateButtonState()
    
    fetch(this.urlValue, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      credentials: 'same-origin'
    })
    .then(this.handleResponse)
    .then(this.handleSuccess)
    .catch(this.handleError)
    .finally(this.handleFinally)
  }

  // Manejar la respuesta del servidor
  handleResponse = (response) => {
    if (!response.ok) {
      return response.json().then(data => {
        throw new Error(data.message || 'Error en la solicitud')
      })
    }
    return response.json()
  }

  // Manejar respuesta exitosa
  handleSuccess = (data) => {
    if (data.status === 'success') {
      this.activeValue = data.active
      this.showToast('success', data.message)
    } else {
      throw new Error(data.message || 'Error al actualizar el estado')
    }
  }

  // Manejar errores
  handleError = (error) => {
    console.error('Error:', error)
    this.showToast('error', error.message || 'Error al procesar la solicitud')
  }

  // Finalizar carga
  handleFinally = () => {
    this.loading = false
    this.updateButtonState()
  }

  // Actualizar el estado visual del botón
  updateButtonState() {
    if (this.loading) {
      this.iconTarget.innerHTML = '<i class="ri-loader-4-line animate-spin text-xl"></i>'
      this.buttonTarget.disabled = true
    } else if (this.activeValue) {
      this.iconTarget.innerHTML = '<i class="ri-star-fill text-yellow-500 text-xl"></i>'
      this.buttonTarget.title = 'Cliente activo - Click para desactivar'
      this.buttonTarget.classList.remove('text-gray-400', 'hover:text-yellow-500')
      this.buttonTarget.classList.add('text-yellow-500', 'hover:text-yellow-600')
      this.buttonTarget.disabled = false
    } else {
      this.iconTarget.innerHTML = '<i class="ri-star-line text-xl"></i>'
      this.buttonTarget.title = 'Marcar como cliente activo'
      this.buttonTarget.classList.remove('text-yellow-500', 'hover:text-yellow-600')
      this.buttonTarget.classList.add('text-gray-400', 'hover:text-yellow-500')
      this.buttonTarget.disabled = false
    }
  }

  // Mostrar notificación toast
  showToast(type, message) {
    const event = new CustomEvent('toast:show', {
      detail: { type, message },
      bubbles: true,
      cancelable: true
    })
    this.element.dispatchEvent(event)
  }

  // Getters y setters para las propiedades reactivas
  get loading() {
    return this._loading
  }

  set loading(value) {
    this._loading = value
    this.loadingValue = value
  }

  get active() {
    return this._active
  }

  set active(value) {
    this._active = value
    this.activeValue = value
  }
}
