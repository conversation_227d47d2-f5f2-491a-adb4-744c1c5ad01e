import { Controller } from "@hotwired/stimulus"

/**
 * Controlador de prueba para verificar la correcta configuración de Stimulus
 */
export default class extends Controller {
  static targets = ["output"]
  
  connect() {
    console.log("[Stimulus] Controlador Hello conectado");
    this.element.textContent = "¡Hola Mundo! - Controlador funcionando correctamente";
    this.element.classList.add("text-green-600", "font-bold");
    
    // Actualizar el elemento de salida si existe
    if (this.hasOutputTarget) {
      this.outputTarget.textContent = "✓ Controlador conectado y funcionando";
      this.outputTarget.classList.add("text-green-700");
    }
  }
  
  disconnect() {
    console.log("[Stimulus] Controlador Hello desconectado");
  }
}
