// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"

import Backup__ClientController from "./backup/client_controller"
application.register("backup--client", Backup__ClientController)

import Backup__Clients<PERSON>oaderController from "./backup/clients_loader_controller"
application.register("backup--clients-loader", Backup__ClientsLoaderController)

import Backup__DatatableClientsBridgeController from "./backup/datatable_clients_bridge_controller"
application.register("backup--datatable-clients-bridge", Backup__DatatableClientsBridgeController)

import Backup__DatatableController from "./backup/datatable_controller"
application.register("backup--datatable", Backup__DatatableController)

import Backup__DatatableGridController from "./backup/datatable_grid_controller"
application.register("backup--datatable-grid", Backup__DatatableGridController)

import Backup__DatatableRegistryController from "./backup/datatable_registry_controller"
application.register("backup--datatable-registry", Backup__DatatableRegistryController)

import Backup__DeleteConfirmationController from "./backup/delete_confirmation_controller"
application.register("backup--delete-confirmation", Backup__DeleteConfirmationController)

import Backup__DropdownController from "./backup/dropdown_controller"
application.register("backup--dropdown", Backup__DropdownController)

import Backup__FiscalDataController from "./backup/fiscal_data_controller"
application.register("backup--fiscal-data", Backup__FiscalDataController)

import Backup__FormController from "./backup/form_controller"
application.register("backup--form", Backup__FormController)

import Backup__HelloController from "./backup/hello_controller"
application.register("backup--hello", Backup__HelloController)

import Backup__ModalController from "./backup/modal_controller"
application.register("backup--modal", Backup__ModalController)

import Backup__PreventScrollController from "./backup/prevent_scroll_controller"
application.register("backup--prevent-scroll", Backup__PreventScrollController)

import Backup__RutController from "./backup/rut_controller"
application.register("backup--rut", Backup__RutController)

import Backup__RutValidationController from "./backup/rut_validation_controller"
application.register("backup--rut-validation", Backup__RutValidationController)

import Backup__SearchController from "./backup/search_controller"
application.register("backup--search", Backup__SearchController)

import Backup__SearchResultsController from "./backup/search_results_controller"
application.register("backup--search-results", Backup__SearchResultsController)

import Backup__SidebarController from "./backup/sidebar_controller"
application.register("backup--sidebar", Backup__SidebarController)

import Backup__SimplifiedRutController from "./backup/simplified_rut_controller"
application.register("backup--simplified-rut", Backup__SimplifiedRutController)

import Client__ClientController from "./client/client_controller"
application.register("client--client", Client__ClientController)

import Client__ClientsLoaderController from "./client/clients_loader_controller"
application.register("client--clients-loader", Client__ClientsLoaderController)

import Client__SearchController from "./client/search_controller"
application.register("client--search", Client__SearchController)

import Client__SearchResultsController from "./client/search_results_controller"
application.register("client--search-results", Client__SearchResultsController)

import Core__HelloController from "./core/hello_controller"
application.register("core--hello", Core__HelloController)

import Datatable__DatatableClientsBridgeController from "./datatable/datatable_clients_bridge_controller"
application.register("datatable--datatable-clients-bridge", Datatable__DatatableClientsBridgeController)

import Datatable__DatatableController from "./datatable/datatable_controller"
application.register("datatable--datatable", Datatable__DatatableController)

import Datatable__DatatableGridController from "./datatable/datatable_grid_controller"
application.register("datatable--datatable-grid", Datatable__DatatableGridController)

import Datatable__DatatableRegistryController from "./datatable/datatable_registry_controller"
application.register("datatable--datatable-registry", Datatable__DatatableRegistryController)

import Fiscal__FiscalDataController from "./fiscal/fiscal_data_controller"
application.register("fiscal--fiscal-data", Fiscal__FiscalDataController)

import Fiscal__RutController from "./fiscal/rut_controller"
application.register("fiscal--rut", Fiscal__RutController)

import Fiscal__RutValidationController from "./fiscal/rut_validation_controller"
application.register("fiscal--rut-validation", Fiscal__RutValidationController)

import Fiscal__SimplifiedRutController from "./fiscal/simplified_rut_controller"
application.register("fiscal--simplified-rut", Fiscal__SimplifiedRutController)

import HelloController from "./hello_controller"
application.register("hello", HelloController)

import RutController from "./rut_controller"
application.register("rut", RutController)

import TestController from "./test_controller"
application.register("test", TestController)

import Ui__DeleteConfirmationController from "./ui/delete_confirmation_controller"
application.register("ui--delete-confirmation", Ui__DeleteConfirmationController)

import Ui__DropdownController from "./ui/dropdown_controller"
application.register("ui--dropdown", Ui__DropdownController)

import Ui__FormController from "./ui/form_controller"
application.register("ui--form", Ui__FormController)

import Ui__ModalController from "./ui/modal_controller"
application.register("ui--modal", Ui__ModalController)

import Ui__PreventScrollController from "./ui/prevent_scroll_controller"
application.register("ui--prevent-scroll", Ui__PreventScrollController)

import Ui__SidebarController from "./ui/sidebar_controller"
application.register("ui--sidebar", Ui__SidebarController)
