import { Application } from "@hotwired/stimulus"

// Inicializar la aplicación de Stimulus siguiendo las mejores prácticas
console.log('[Stimulus] Inicializando aplicación Stimulus...');
const application = Application.start()

// Mejorar depuración configurando un prefijo para los logs
application.debug = true
application.handleError = (error, message, detail) => {
  // Conservar el comportamiento original de imprimir en consola
  console.error(`%c[Stimulus Error] ${message}`, "color: #e83e8c;", error, detail)
  
  // Opcionalmente, podríamos agregar reporte a Sentry/Rollbar aquí
  // if (window.Rollbar) window.Rollbar.error(error, { message, detail })
}

// Hacer que la aplicación esté disponible globalmente para depuración
// Esto es útil para depuración en consola y pruebas
window.Stimulus = application

// Verificar que todo se inicializó correctamente
console.log('[Stimulus] Aplicación inicializada correctamente:', {
  version: application.version || 'no disponible',
  debug: application.debug,
  global: window.Stimulus === application ? 'disponible' : 'no disponible'
});

export { application }
