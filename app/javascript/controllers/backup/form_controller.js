// Form controller for handling form submissions and validations
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["submitButton"]

  connect() {
    // Add loading state to submit button on form submission
    this.element.addEventListener('submit', this.handleSubmit.bind(this))
  }

  disconnect() {
    // Clean up event listeners
    this.element.removeEventListener('submit', this.handleSubmit)
  }

  // Handle form submission
  handleSubmit() {
    const submitButton = this.hasSubmitButtonTarget ? this.submitButtonTarget : null
    
    // Disable submit button and show loading state
    if (submitButton) {
      const originalText = submitButton.innerHTML
      submitButton.disabled = true
      submitButton.classList.add('opacity-75', 'cursor-not-allowed')
      
      // Store original text if not already stored
      if (!submitButton.dataset.originalText) {
        submitButton.dataset.originalText = originalText
      }
      
      // Show loading text if data-disable-with is present
      if (submitButton.dataset.disableWith) {
        submitButton.innerHTML = submitButton.dataset.disableWith
      }
    }
  }

  // Handle form submission end (after Turbo Streams)
  submitEnd(event) {
    // Only handle successful form submissions
    if (event.detail.success) {
      this.resetForm()
    } else {
      this.enableSubmitButton()
    }
  }

  // Reset form to initial state
  resetForm() {
    if (this.hasSubmitButtonTarget) {
      this.enableSubmitButton()
    }
    
    // Reset form if it's not a Turbo Frame
    if (!this.element.closest('turbo-frame')) {
      this.element.reset()
    }
  }

  // Enable submit button and restore original text
  enableSubmitButton() {
    if (!this.hasSubmitButtonTarget) return
    
    const button = this.submitButtonTarget
    button.disabled = false
    button.classList.remove('opacity-75', 'cursor-not-allowed')
    
    if (button.dataset.originalText) {
      button.innerHTML = button.dataset.originalText
    }
  }
}
