import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["sidebar"]

  connect() {
    console.log("=== Sidebar controller conectado ====")
    console.log("Elemento controller:", this.element)
    console.log("¿Tiene targets?", this.sidebarTarget ? "sí" : "no")
    console.log("¿Tiene sidebarTarget?", this.hasSidebarTarget)
    console.log("Targets disponibles:", this.sidebarTargets.length)
    
    // Buscar el elemento del sidebar directamente para diagnóstico
    const sidebarById = document.getElementById("sidebar")
    console.log("Sidebar encontrado por ID:", sidebarById)
    
    // Verificar si tenemos el sidebar como target
    if (this.hasSidebarTarget) {
      console.log("Sidebar target encontrado correctamente")
      this.sidebarTarget.setAttribute("aria-hidden", "true")
    } else {
      // Solución alternativa si no encuentra el target
      this.sidebarElement = sidebarById
      if (this.sidebarElement) {
        console.log("Usando sidebar encontrado por ID como alternativa")
      } else {
        console.error("ERROR CRÍTICO: No se encontró el sidebar de ninguna forma")
      }
    }
    
    // Escuchar la tecla Escape
    document.addEventListener("keydown", this.handleKeydown.bind(this))
  }
  
  disconnect() {
    // Limpiar event listeners al desconectar
    document.removeEventListener("keydown", this.handleKeydown.bind(this))
    this.removeOverlay()
  }

  toggle() {
    console.log("Método toggle llamado")
    console.log("¿Tiene sidebarTarget?", this.hasSidebarTarget)
    
    // Determinar qué elemento usar (target o referencia directa)
    const sidebarElement = this.hasSidebarTarget ? this.sidebarTarget : this.sidebarElement || document.getElementById("sidebar")
    
    if (sidebarElement) {
      console.log("Toggle en elemento sidebar:", sidebarElement)
      const isHidden = sidebarElement.classList.contains("-translate-x-full")
      
      if (isHidden) {
        // Mostrar sidebar
        console.log("Mostrando sidebar")
        sidebarElement.classList.remove("-translate-x-full")
        sidebarElement.setAttribute("aria-hidden", "false")
        this.addOverlay()
      } else {
        // Ocultar sidebar
        console.log("Ocultando sidebar")
        sidebarElement.classList.add("-translate-x-full")
        sidebarElement.setAttribute("aria-hidden", "true")
        this.removeOverlay()
      }
    } else {
      console.error("ERROR CRÍTICO: No se encontró ningún elemento sidebar para toggle")
      
      // Intentar una última solución
      const anyElement = document.querySelector("#sidebar") || document.querySelector("[data-sidebar-target]") || document.querySelector("aside")
      if (anyElement) {
        alert("Encontrado sidebar por selector alternativo")
        anyElement.classList.toggle("-translate-x-full")
      } else {
        alert("ERROR: No se pudo encontrar el sidebar de ninguna manera")
      }
    }
  }
  
  handleKeydown(event) {
    if (event.key === "Escape" && this.hasSidebarTarget && 
        !this.sidebarTarget.classList.contains("-translate-x-full")) {
      this.toggle()
    }
  }
  
  addOverlay() {
    if (document.getElementById("sidebar-overlay")) return
    
    const overlay = document.createElement("div")
    overlay.id = "sidebar-overlay"
    overlay.className = "fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
    overlay.setAttribute("aria-hidden", "true")
    
    // Agregar listener para cerrar al hacer click
    overlay.addEventListener("click", () => this.toggle())
    
    document.body.appendChild(overlay)
    document.body.classList.add("overflow-hidden")
  }
  
  removeOverlay() {
    const overlay = document.getElementById("sidebar-overlay")
    if (!overlay) return
    
    overlay.remove()
    document.body.classList.remove("overflow-hidden")
  }
}