import { Controller } from "@hotwired/stimulus"

// Controlador de búsqueda unificado para toda la aplicación
export default class extends Controller {
  static targets = ["input", "results", "item"]
  static values = {
    debounce: { type: Number, default: 300 },
    minLength: { type: Number, default: 2 },
    searchIn: { type: String, default: 'both' } // 'both', 'text', 'data'
  }

  connect() {
    this.timeout = null
    this.initializeEventListeners()
  }

  disconnect() {
    this.removeEventListeners()
  }

  initializeEventListeners() {
    this.inputTarget.addEventListener('input', this.handleInput.bind(this))
    this.inputTarget.addEventListener('keydown', this.handleKeyDown.bind(this))
    
    // Manejar clic en el botón de limpiar si existe
    const clearButton = this.element.querySelector('[data-action~="search#clear"]')
    if (clearButton) {
      clearButton.addEventListener('click', this.clear.bind(this))
    }
  }

  removeEventListeners() {
    this.inputTarget?.removeEventListener('input', this.handleInput)
    this.inputTarget?.removeEventListener('keydown', this.handleKeyDown)
  }

  handleInput(event) {
    clearTimeout(this.timeout)
    
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, this.debounceValue)
  }

  handleKeyDown(event) {
    // Permitir navegación con teclado en los resultados
    if (event.key === 'Escape') {
      this.clear()
    } else if (event.key === 'Enter') {
      event.preventDefault()
      this.performSearch({ immediate: true })
    }
  }

  performSearch(options = {}) {
    const searchTerm = this.inputTarget.value.trim().toLowerCase()
    
    // No buscar si el término es muy corto
    if (searchTerm.length > 0 && searchTerm.length < this.minLengthValue) {
      return
    }

    // Disparar evento personalizado antes de la búsqueda
    this.dispatch('beforeSearch', { detail: { searchTerm } })

    if (this.hasResultsTarget) {
      this.filterResults(searchTerm)
    } else if (this.hasItemTargets) {
      this.filterItems(searchTerm)
    }

    // Disparar evento personalizado después de la búsqueda
    this.dispatch('afterSearch', { 
      detail: { 
        searchTerm,
        results: this.getVisibleItems()
      } 
    })
  }

  filterResults(searchTerm) {
    const items = this.resultsTarget.querySelectorAll('[data-search-target="item"], [data-searchable]')
    this.filterItems(searchTerm, Array.from(items))
  }

  filterItems(searchTerm, items = this.itemTargets) {
    items.forEach(item => {
      let searchableText = ''
      
      // Buscar en texto o atributos de datos según la configuración
      if (this.searchInValue === 'both' || this.searchInValue === 'text') {
        searchableText += ' ' + item.textContent.toLowerCase()
      }
      
      if (this.searchInValue === 'both' || this.searchInValue === 'data') {
        const searchableData = item.getAttribute('data-search-text') || ''
        searchableText += ' ' + searchableData.toLowerCase()
      }

      // Mostrar/ocultar según coincidencia
      const isMatch = searchTerm === '' || searchableText.includes(searchTerm)
      item.classList.toggle('hidden', !isMatch)
    })
  }

  getVisibleItems() {
    if (this.hasResultsTarget) {
      return Array.from(this.resultsTarget.querySelectorAll(':not(.hidden)'))
    }
    return this.itemTargets.filter(item => !item.classList.contains('hidden'))
  }

  clear() {
    this.inputTarget.value = ''
    this.inputTarget.focus()
    this.performSearch()
    this.dispatch('cleared')
  }

  // Método para búsqueda programática
  search(term) {
    this.inputTarget.value = term
    this.performSearch({ immediate: true })
  }
}

// Inicializar búsqueda global con atajo de teclado
const initializeGlobalSearch = () => {
  document.addEventListener('keydown', (event) => {
    // Atajo: Ctrl+K o Cmd+K para enfocar la búsqueda
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      const searchInput = document.querySelector('[data-search-target="input"], [data-controller~="search"] input[type="search"]')
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
    }
  })
}

// Inicializar cuando el DOM esté listo
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeGlobalSearch)
} else {
  initializeGlobalSearch()
}
