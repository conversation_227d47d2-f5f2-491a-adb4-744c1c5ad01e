import { Controller } from "@hotwired/stimulus";
import UruguayanRutController from "./rut_validation_controller";

export default class extends Controller {
  static targets = [
    "input", 
    "form", 
    "turboFrame", 
    "resultsContainer", 
    "loadingIndicator",
    "clearButton", 
    "spinner", 
    "results", 
    "error", 
    "fiscalForm"
  ];
  
  static values = {
    formatRut: { type: Boolean, default: true },
    validateRut: { type: Boolean, default: true },
    searchUrl: { type: String, default: '/owner/search/clients' },
    fiscalDataUrl: { type: String, default: '/owner/search/fiscal_data' },
    debounceTimeout: { type: Number, default: 300 },
    minSearchLength: { type: Number, default: 3 },
    namespace: { type: String, default: 'owner' }
  };
  
  // Método para depurar los objetivos disponibles
  debug() {
    console.log('Depurando controlador RUT:');
    console.log('¿Tiene input target?', this.hasInputTarget);
    console.log('¿Tiene spinner target?', this.hasSpinnerTarget);
    console.log('¿Tiene fiscalForm target?', this.hasFiscalFormTarget);
    if (this.hasInputTarget) {
      console.log('Valor del input:', this.inputTarget.value);
    }
  }

  connect() {
    console.log("RUT Controller conectado");
    
    // Inicializar propiedades
    this.lastSearch = '';
    this.searchTimeout = null;
    this.isSearching = false;
    
    // Configurar el input para formateo automático
    if (this.hasInputTarget) {
      this.inputTarget.addEventListener('input', this.handleInput.bind(this));
      this.inputTarget.addEventListener('keydown', this.handleKeyDown.bind(this));
      
      // Manejar el evento de submit del formulario
      if (this.hasFormTarget) {
        this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
      }
    }
    
    // Configurar el autofocus
    this.initializeAutoFocus();
    
    // Cerrar resultados al hacer clic fuera
    document.addEventListener('click', this.handleClickOutside.bind(this));
  }
  
  disconnect() {
    if (this.hasInputTarget) {
      this.inputTarget.removeEventListener('input', this.handleInput);
      this.inputTarget.removeEventListener('keydown', this.handleKeyDown);
    }
    
    if (this.hasFormTarget) {
      this.formTarget.removeEventListener('submit', this.handleSubmit);
    }
    
    document.removeEventListener('click', this.handleClickOutside);
    clearTimeout(this.searchTimeout);
  }
  
  initializeAutoFocus() {
    if (this.hasInputTarget && !this.inputTarget.value) {
      this.inputTarget.focus();
    }
  }

  clear() {
    if (this.hasInputTarget) {
      this.inputTarget.value = '';
      this.inputTarget.focus();
      
      // Actualizar también el campo hidden
      const hiddenRutField = document.getElementById('organization_rut_hidden');
      if (hiddenRutField) {
        hiddenRutField.value = '';
      }
    }
    this.clearErrors();
  }

  clearErrors() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden');
      this.errorTarget.textContent = '';
    }
  }

  // Manejar entrada de texto con debounce
  handleInput(event) {
    // Limpiar timeout anterior
    clearTimeout(this.searchTimeout);
    
    // Formatear el RUT
    if (this.formatRutValue) {
      this.formatRut(event.target);
    }
    
    // Obtener el valor limpio
    const cleanValue = this.cleanRut(event.target.value);
    
    // Si el valor es muy corto, limpiar resultados
    if (cleanValue.length < this.minSearchLengthValue) {
      this.clearResults();
      return;
    }
    
    // Si el valor no ha cambiado, no hacer nada
    if (cleanValue === this.lastSearch) return;
    
    // Actualizar último término de búsqueda
    this.lastSearch = cleanValue;
    
    // Mostrar indicador de carga
    this.showLoading(true);
    
    // Configurar debounce para la búsqueda
    this.searchTimeout = setTimeout(() => {
      this.performSearch(cleanValue);
    }, this.debounceTimeoutValue);
  }
  
  // Manejar teclas especiales
  handleKeyDown(event) {
    if (event.key === 'Escape') {
      // ESC cierra los resultados
      this.clearResults();
      return;
    }
    
    if (!this.hasResultsContainerTarget || this.resultsContainerTarget.classList.contains('hidden')) {
      return;
    }
    
    if (event.key === 'ArrowDown') {
      // Flecha abajo para navegar resultados
      event.preventDefault();
      this.navigateResults('down');
    } else if (event.key === 'ArrowUp') {
      // Flecha arriba para navegar resultados
      event.preventDefault();
      this.navigateResults('up');
    } else if (event.key === 'Enter' && !event.ctrlKey && !event.metaKey) {
      // Enter para seleccionar resultado
      event.preventDefault();
      this.selectFocusedResult();
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      // Ctrl+Enter / Cmd+Enter para buscar datos fiscales
      event.preventDefault();
      this.fetchFiscalData(event);
    }
  }
  
  // Manejar clic fuera del contenedor
  handleClickOutside(event) {
    if (this.hasResultsContainerTarget && !this.resultsContainerTarget.contains(event.target)) {
      this.clearResults();
    }
  }
  
  // Realizar búsqueda en el servidor
  performSearch(term) {
    if (!term || term.length < this.minSearchLengthValue) {
      this.showLoading(false);
      this.clearResults();
      return;
    }
    
    const url = `${this.searchUrlValue}?term=${encodeURIComponent(term)}`;
    
    fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
      this.showLoading(false);
      this.displayResults(data);
    })
    .catch(error => {
      console.error('Error en la búsqueda:', error);
      this.showLoading(false);
    });
  }
  
  // Mostrar indicador de carga
  showLoading(show) {
    if (this.hasLoadingIndicatorTarget) {
      this.loadingIndicatorTarget.classList.toggle('hidden', !show);
    }
  }
  
  // Mostrar resultados de búsqueda
  displayResults(clients) {
    if (!this.hasResultsContainerTarget || !this.hasResultsTarget) {
      return;
    }
    
    this.clearResults();
    
    if (!clients || clients.length === 0) {
      return;
    }
    
    this.resultsContainerTarget.classList.remove('hidden');
    
    const resultsHtml = clients.map((client, index) => {
      const name = client.name || 'Cliente sin nombre';
      const rut = this.formatRutForDisplay(client.rut || '');
      const href = client.url || '#';
      
      return `
        <li class="result-item" data-index="${index}" data-rut="${client.rut || ''}" data-url="${href}">
          <a href="${href}" class="block px-4 py-2 hover:bg-gray-100">
            <div class="font-medium text-sm">${name}</div>
            <div class="text-xs text-gray-500">${rut}</div>
          </a>
        </li>
      `;
    }).join('');
    
    this.resultsTarget.innerHTML = resultsHtml;
    
    // Agregar event listeners a los resultados
    this.resultsTarget.querySelectorAll('.result-item').forEach(item => {
      item.addEventListener('click', this.selectResult.bind(this));
    });
  }
  
  // Limpiar resultados
  clearResults() {
    if (this.hasResultsContainerTarget) {
      this.resultsContainerTarget.classList.add('hidden');
    }
    
    if (this.hasResultsTarget) {
      this.resultsTarget.innerHTML = '';
    }
  }
  
  // Navegar por los resultados con el teclado
  navigateResults(direction) {
    // Implementación pendiente
  }
  
  // Seleccionar un resultado
  selectResult(event) {
    event.preventDefault();
    
    let target = event.target;
    while (target && !target.matches('.result-item')) {
      target = target.parentElement;
    }
    
    if (!target) return;
    
    const url = target.dataset.url;
    const rut = target.dataset.rut;
    
    if (url && url !== '#') {
      // Si hay una URL, navegar a ella
      if (this.hasTurboFrameTarget) {
        // Usar Turbo para la navegación
        this.turboFrameTarget.src = url;
      } else {
        // Navegación normal
        window.location.href = url;
      }
    } else if (rut) {
      // Si solo hay RUT, rellenar el input
      if (this.hasInputTarget) {
        this.inputTarget.value = rut;
      }
    }
    
    this.clearResults();
  }
  
  // Seleccionar el resultado enfocado
  selectFocusedResult() {
    // Implementación pendiente
  }
  
  // Formatear RUT para mostrar en los resultados
  formatRutForDisplay(rut) {
    if (!rut) return '';
    
    // Limpiar el RUT (quitar puntos y guiones)
    let clean = rut.replace(/[^\dK]/g, '');
    
    // Si es muy corto, devolver sin formatear
    if (clean.length <= 2) return clean;
    
    // Separar dígito verificador
    const dv = clean.charAt(clean.length - 1);
    const num = clean.slice(0, -1);
    
    // Formatear número con puntos
    let formatted = '';
    for (let i = num.length - 1, j = 0; i >= 0; i--, j++) {
      if (j % 3 === 0 && j > 0) formatted = '.' + formatted;
      formatted = num.charAt(i) + formatted;
    }
    
    // Devolver RUT formateado
    return `${formatted}-${dv}`;
  }
  
  // Formatear el RUT uruguayo mientras se escribe
  formatRut(event) {
    let input;
    
    if (typeof event === 'object' && event.tagName) {
      // Si se pasó el elemento directamente
      input = event;
    } else if (event && event.target) {
      // Si se pasó un evento
      input = event.target;
    } else if (this.hasInputTarget) {
      // Si no se pasó nada, usar el input target
      input = this.inputTarget;
    } else {
      return;
    }
    
    const value = input.value;
    const cleanValue = this.cleanRut(value);
    
    // Si está vacío o es muy corto, no formatear
    if (cleanValue.length <= 2) {
      return;
    }
    
    // Separar dígito verificador
    const dv = cleanValue.charAt(cleanValue.length - 1);
    const num = cleanValue.slice(0, -1);
    
    // Formatear con puntos
    let formatted = '';
    for (let i = 0; i < num.length; i++) {
      if (i > 0 && i % 3 === 0) formatted += '.';
      formatted += num.charAt(i);
    }
    
    // Actualizar el input con el RUT formateado
    input.value = `${formatted}-${dv}`;
  }

  // Validar formato de RUT uruguayo usando UruguayanRutController
  validarRut(rut) {
    if (!this.validateRutValue) {
      return true;
    }
    
    try {
      const rutController = new UruguayanRutController();
      return rutController.validateRut(rut);
    } catch (error) {
      console.error('Error al validar RUT:', error);
      return false;
    }
  }
  
  // Limpiar RUT usando el método estático de UruguayanRutController
  cleanRut(rut) {
    return UruguayanRutController.cleanRut(rut);
  }
  
  // Manejar el envío del formulario
  handleSubmit(event) {
    const rut = this.inputTarget.value.trim();
    
    // Modificación: Si el formulario no debe enviarse normalmente
    if (this.hasFormTarget && !this.formTarget.getAttribute('action')) {
      event.preventDefault();
      this.submitWithTurbo();
    }
    if (!rut) {
      event.preventDefault();
      return;
    }
    
    // Validar RUT antes de enviar
    if (!this.validarRut(rut)) {
      event.preventDefault();
      this.showError('El RUT ingresado no es válido');
      return;
    }
    
    // Disparar evento personalizado con el RUT limpio y formateado
    this.dispatch('search', { 
      detail: { 
        rut: rut,
        formattedRut: this.inputTarget.value
      },
      cancelable: true
    });
  }
  
  // Disparar evento personalizado
  dispatch(name, payload = {}) {
    const event = new CustomEvent(`rut:${name}`, {
      bubbles: true,
      cancelable: payload.cancelable || false,
      detail: payload.detail || {}
    });
    
    return this.element.dispatchEvent(event);
  }
  
  // Manejar tecla Enter para buscar datos fiscales
  handleKeypress(event) {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      this.fetchFiscalData(event);
    }
  }
  
  // Sincronizar el campo oculto con el campo visible
  syncHiddenField(event) {
    const hiddenRutField = document.getElementById('organization_rut_hidden');
    if (hiddenRutField && this.hasInputTarget) {
      hiddenRutField.value = this.inputTarget.value;
    }
  }
  
  // Consultar datos fiscales a partir del RUT
  fetchFiscalData(event) {
    console.log('Iniciando fetchFiscalData - Evento disparado correctamente');
    
    if (!this.hasInputTarget) {
      console.error('Error: No se encontró el input target - Compruebe data-rut-target="input"');
      alert('Error: No se encontró el campo RUT');
      return;
    }
    
    const rut = this.inputTarget.value;
    if (!rut) {
      this.showError('Debe ingresar un RUT para consultar');
      return;
    }
    
    // Actualizar también el campo hidden si existe
    const hiddenRutField = document.getElementById('organization_rut_hidden');
    if (hiddenRutField) {
      hiddenRutField.value = rut;
    }
    
    // Mostrar spinner de carga si existe
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.classList.remove('hidden');
    }
    
    // Limpiar mensajes de error previos
    this.clearErrors();

    // Obtener la URL de datos fiscales (configurable por namespace)
    const fiscalUrl = this.fiscalDataUrlValue || `/${this.namespaceValue}/search/fiscal_data`;
    console.log(`Utilizando URL para consulta fiscal: ${fiscalUrl}`);
    
    // Realizar la consulta al backend
    fetch(`${fiscalUrl}?rut=${encodeURIComponent(rut)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(result => {
      // Ocultar spinner
      if (this.hasSpinnerTarget) {
        this.spinnerTarget.classList.add('hidden');
      }
      
      console.log('Respuesta de la API fiscal:', result);
      
      if (result.success) {
        // Si la consulta fue exitosa, autocompletar el formulario
        this.showSuccess('Datos fiscales obtenidos correctamente');
        // Usar los datos directamente, no anidados en data
        this.autocompleteFiscalForm(result);
      } else {
        // Si hubo un error, mostrar mensaje
        this.showError(result.error || 'Error al consultar datos fiscales');
      }
    })
    .catch(error => {
      console.error('Error al consultar datos fiscales:', error);
      
      // Ocultar spinner
      if (this.hasSpinnerTarget) {
        this.spinnerTarget.classList.add('hidden');
      }
      
      // Mostrar error
      this.showError(`Error: ${error.message}`);
    });
  }
  
  // Autocompletar formulario de datos fiscales y guardarlo automáticamente
  autocompleteFiscalForm(fiscalData) {
    console.log('Autocompletando formulario con datos fiscales:', fiscalData);
    
    // Encontrar el formulario fiscal
    const form = this.hasFiscalFormTarget ? this.fiscalFormTarget : document.querySelector('form');
    if (!form) {
      console.error('No se encontró el formulario fiscal');
      this.showError('No se pudo encontrar el formulario para autocompletar');
      return false;
    }
    
    try {
      // Mapeo de campos del formulario
      // Los nombres de los campos deben coincidir exactamente con los del formulario HTML
      const fieldMapping = {
        'organization[nombre_legal]': fiscalData.nombre_legal || '',
        'organization[nombre_fantasia]': fiscalData.nombre_fantasia || '',
        'organization[tipo_entidad]': fiscalData.tipo_entidad || '',
        'organization[direccion]': fiscalData.direccion || fiscalData.address || '',
        'organization[telefono]': fiscalData.telefono || fiscalData.phone || '',
        'organization[email]': fiscalData.email || '',
        'organization[email_facturacion]': fiscalData.email || ''
      };
      
      console.log('Mapeo de campos:', fieldMapping);
      
      // Actualizar cada campo del formulario
      Object.entries(fieldMapping).forEach(([fieldName, value]) => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
          field.value = value || '';
          console.log(`Campo ${fieldName} actualizado con:`, value);
          
          // Disparar eventos para notificar a otros componentes
          ['input', 'change'].forEach(eventType => {
            field.dispatchEvent(new Event(eventType, { bubbles: true }));
          });
        } else {
          console.warn(`No se encontró el campo: ${fieldName}`);
        }
      });
      
      // Manejar campo de actividades (si existe)
      const actividadesField = form.querySelector('[name="organization[actividades]"]');
      if (actividadesField) {
        let actividadesText = '';
        
        if (Array.isArray(fiscalData.actividades) && fiscalData.actividades.length > 0) {
          actividadesText = fiscalData.actividades
            .map(act => {
              if (typeof act === 'object' && act.descripcion) {
                return `${act.descripcion} (${act.codigo || ''})`;
              }
              return act;
            })
            .filter(Boolean)
            .join('\n');
        } else if (typeof fiscalData.actividades === 'string') {
          actividadesText = fiscalData.actividades;
        }
        
        actividadesField.value = actividadesText;
        console.log('Actividades actualizadas:', actividadesText);
        actividadesField.dispatchEvent(new Event('change', { bubbles: true }));
        actividadesField.dispatchEvent(new Event('input', { bubbles: true }));
      }
      
      // Mostrar mensaje de éxito
      this.showSuccess('¡Datos fiscales cargados correctamente!');
      
      // Guardar automáticamente el formulario después de un breve retraso
      setTimeout(() => {
        console.log('Guardando formulario automáticamente...');
        
        // Buscar el botón de submit
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
          // Si hay un botón de submit, simulamos su clic
          submitButton.click();
          console.log('Formulario enviado vía botón submit');
        } else {
          // Si no hay botón de submit, enviamos el formulario directamente
          form.submit();
          console.log('Formulario enviado vía form.submit()');
        }
      }, 1000); // Esperar 1 segundo antes de enviar
      
      return true;
      
    } catch (error) {
      console.error('Error al autocompletar el formulario:', error);
      this.showError('Ocurrió un error al cargar los datos fiscales');
      return false;
    }
  }
  
  // Eliminar mensaje de error
  removeError() {
    const errorDivs = this.element.querySelectorAll('.text-red-600');
    errorDivs.forEach(div => div.remove());
  }

  // Mostrar mensaje de error
  showError(message) {
    this.removeError();

    const errorDiv = document.createElement('div');
    errorDiv.className = 'mt-1 text-sm text-red-600';
    errorDiv.textContent = message;

    // Insertar después del input
    if (this.hasInputTarget) {
      this.inputTarget.parentNode.insertBefore(errorDiv, this.inputTarget.nextSibling);

      // Enfocar el input
      this.inputTarget.focus();

      // Auto-ocultar después de 5 segundos
      setTimeout(() => {
        errorDiv.remove();
      }, 5000);
    } else {
      this.element.appendChild(errorDiv);
    }

    return errorDiv;
  }
  
  // Mostrar mensaje de éxito
  showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'mt-1 text-sm text-green-600';
    successDiv.textContent = message;
    
    if (this.hasInputTarget) {
      this.inputTarget.parentNode.insertBefore(successDiv, this.inputTarget.nextSibling);
    } else {
      this.element.appendChild(successDiv);
    }
    
    // Auto-ocultar después de 5 segundos
    setTimeout(() => {
      successDiv.remove();
    }, 5000);
    
    return successDiv;
  }
}
