import { Application } from "@hotwired/stimulus"

console.log('[Stimulus] 🎯 Inicializando aplicación Stimulus...');

const application = Application.start()

// Configurar Stimulus para desarrollo
application.debug = true
application.handleError = (error, message, detail) => {
  console.error(`[Stimulus Error] ${message}`, error, detail)
}

console.log('[Stimulus] ✅ Aplicación Stimulus creada:', application);

// Hacer que la aplicación esté disponible globalmente para depuración
window.Stimulus = application
console.log('[Stimulus] ✅ window.Stimulus configurado:', window.Stimulus);

console.log('[Stimulus] ✅ Aplicación inicializada correctamente');

export { application }
