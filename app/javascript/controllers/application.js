console.log('[Stimulus] 🎯 Intentando cargar Stimulus...');

// Función asíncrona para cargar Stimulus
async function loadStimulus() {
  let Application;

  try {
    // Método 1: Import estándar
    console.log('[Stimulus] Método 1: Import estándar...');
    const stimulusModule = await import("@hotwired/stimulus");
    Application = stimulusModule.Application;
    console.log('[Stimulus] ✅ Método 1 exitoso:', Application);
  } catch (error) {
    console.warn('[Stimulus] ⚠️ Método 1 falló:', error);

    try {
      // Método 2: Import directo del archivo local
      console.log('[Stimulus] Método 2: Import directo del archivo local...');
      const stimulusModule = await import("/assets/@hotwired--stimulus.js");
      Application = stimulusModule.Application;
      console.log('[Stimulus] ✅ Método 2 exitoso:', Application);
    } catch (error2) {
      console.warn('[Stimulus] ⚠️ Método 2 falló:', error2);

      // Método 3: Fallback a CDN
      console.log('[Stimulus] Método 3: Fallback a CDN...');
      try {
        const stimulusModule = await import("https://unpkg.com/@hotwired/stimulus@3.2.2/dist/stimulus.js");
        Application = stimulusModule.Application;
        console.log('[Stimulus] ✅ Método 3 (CDN) exitoso:', Application);
      } catch (error3) {
        console.error('[Stimulus] ❌ Todos los métodos fallaron:', error3);
        throw new Error('No se pudo cargar Stimulus');
      }
    }
  }

  if (Application) {
    console.log('[Stimulus] 🎯 Inicializando aplicación Stimulus...');

    const application = Application.start()

    // Configurar Stimulus para desarrollo
    application.debug = true
    application.handleError = (error, message, detail) => {
      console.error(`[Stimulus Error] ${message}`, error, detail)
    }

    console.log('[Stimulus] ✅ Aplicación Stimulus creada:', application);

    // Hacer que la aplicación esté disponible globalmente para depuración
    window.Stimulus = application
    console.log('[Stimulus] ✅ window.Stimulus configurado:', window.Stimulus);

    console.log('[Stimulus] ✅ Aplicación inicializada correctamente');

    // Exportar para uso en otros módulos
    window.StimulusApplication = application;

    return application;
  } else {
    console.error('[Stimulus] ❌ No se pudo inicializar Stimulus');
    return null;
  }
}

// Iniciar la carga de Stimulus
loadStimulus().catch(error => {
  console.error('[Stimulus] ❌ Error fatal al cargar Stimulus:', error);
});

// También exportar la función para uso externo
window.loadStimulus = loadStimulus;
