import { Controller } from "@hotwired/stimulus";
import { validateRut } from "../utils/rut_utils";
import FiscalApiService from "../services/fiscal_api_service";

/**
 * Controlador legacy para validación y formato de RUT
 * 
 * NOTA: Este controlador se mantiene solo para vistas legacy en:
 * - owner/clients/edit.html.erb
 * - collaborator/clients/edit.html.erb
 * 
 * Para nuevas implementaciones, usar simplified_rut_controller.js
 */
export default class extends Controller {
  static targets = [
    "input", "searchButton", "clearButton", "searchContainer", "searchResults", 
    "rutError", "fiscalMessage", "fiscalForm", "spinner"
  ];

  static values = {
    searchUrl: String,
    validateRut: { type: Boolean, default: true },
    namespace: { type: String, default: 'owner' }
  };

  connect() {
    console.log("Legacy RUT Controller connected");
    
    if (this.hasInputTarget && this.inputTarget.value.trim()) {
      this.format();
    }

    // Crear instancia del servicio fiscal para consulta DGI
    this.fiscalApiService = new FiscalApiService();
  }

  /**
   * Formatea un RUT con puntos y guion
   */
  format() {
    if (!this.hasInputTarget) return;

    let value = this.inputTarget.value.trim();
    if (!value) return;

    // Eliminar todos los caracteres no numéricos y 'k'
    value = value.replace(/[^0-9kK]/g, "");

    // Formatear con puntos y guion
    if (value.length > 1) {
      const body = value.slice(0, -1);
      const dv = value.slice(-1).toLowerCase();
      
      // Agregar puntos para los miles
      let formatted = "";
      for (let i = body.length - 1, j = 0; i >= 0; i--, j++) {
        if (j % 3 === 0 && j > 0) formatted = "." + formatted;
        formatted = body[i] + formatted;
      }
      
      // Agregar guion y dígito verificador
      formatted = formatted + "-" + dv;
      
      this.inputTarget.value = formatted;
    }

    // Validar si es necesario
    if (this.validateRutValue) {
      this.validate();
    }
  }

  /**
   * Valida un RUT
   * @returns {boolean} Indica si el RUT es válido
   */
  validate() {
    if (!this.hasInputTarget) return false;
    
    const rut = this.inputTarget.value.trim();
    if (!rut) return false;

    // Limpiar RUT para validación
    const cleanRut = rut.replace(/\.|-/g, "");
    
    const result = validateRut(cleanRut);
    
    // Mostrar error si es inválido
    if (this.hasRutErrorTarget) {
      if (!result) {
        this.rutErrorTarget.textContent = "RUT inválido";
        this.rutErrorTarget.classList.remove("hidden");
      } else {
        this.rutErrorTarget.classList.add("hidden");
      }
    }
    
    return result;
  }

  /**
   * Limpia el campo de búsqueda y los resultados
   */
  clear() {
    if (this.hasInputTarget) {
      this.inputTarget.value = "";
      this.inputTarget.focus();
    }
    
    if (this.hasSearchResultsTarget) {
      this.searchResultsTarget.innerHTML = "";
      this.searchResultsTarget.classList.add("hidden");
    }
  }

  /**
   * Búsqueda simple de RUT
   */
  search() {
    if (!this.hasInputTarget) return;

    const query = this.inputTarget.value.trim();
    if (!query) return;

    // Validar formato de RUT
    if (this.validateRutValue && !this.validate()) {
      return;
    }

    // Redirigir a página de búsqueda con RUT
    window.location.href = `/${this.namespaceValue}/search?q=${encodeURIComponent(query)}`;
  }

  /**
   * Consultar datos fiscales para el RUT actual
   */
  async fetchFiscalData() {
    if (!this.hasInputTarget) {
      console.error("No se encontró el input target de RUT");
      return;
    }

    const rut = this.inputTarget.value.trim();
    if (!rut) {
      this.showFiscalMessage("Por favor ingrese un RUT para consultar", "warning");
      return;
    }

    // Validar formato de RUT si es necesario
    if (this.validateRutValue && !this.validate()) {
      this.showFiscalMessage("RUT inválido, por favor verifique el formato", "error");
      return;
    }

    // Mostrar spinner de carga
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.classList.remove("hidden");
    }

    try {
      // Obtener el ID del cliente de la URL
      const pathParts = window.location.pathname.split('/');
      const clientId = pathParts[pathParts.length - 1];

      // Usar el servicio fiscal
      const result = await this.fiscalApiService.fetchFiscalData(rut, clientId);

      if (result && result.success) {
        this._populateFiscalForm(result);
        this.showFiscalMessage("Datos fiscales cargados correctamente", "success");
      } else {
        this.showFiscalMessage(result?.message || "No se pudieron obtener datos fiscales", "error");
      }
    } catch (error) {
      console.error("Error al consultar datos fiscales:", error);
      this.showFiscalMessage(error.message || "Error al consultar datos fiscales", "error");
    } finally {
      // Ocultar spinner de carga
      if (this.hasSpinnerTarget) {
        this.spinnerTarget.classList.add("hidden");
      }
    }
  }

  /**
   * Completa el formulario fiscal con los datos de la API
   * @param {Object} data - Los datos fiscales de la API
   * @private
   */
  _populateFiscalForm(data) {
    if (!this.hasFiscalFormTarget || !data.result) return;

    const fiscalData = data.result;
    const form = this.fiscalFormTarget;

    // Mapear campos de la API a campos del formulario
    const fieldMapping = {
      nombre_legal: "organization[nombre_legal]",
      nombre_fantasia: "organization[nombre_fantasia]",
      giro: "organization[giro]",
      actividades: "organization[actividades]",
      direccion: "organization[direccion]",
      codigo_postal: "organization[codigo_postal]",
      ciudad: "organization[ciudad]",
      region: "organization[region]",
      comuna: "organization[comuna]",
      email: "organization[email]",
      telefono: "organization[telefono]"
    };

    // Rellenar los campos del formulario
    Object.entries(fieldMapping).forEach(([apiField, formField]) => {
      const input = form.querySelector(`[name="${formField}"]`);
      if (input && fiscalData[apiField] !== undefined) {
        input.value = fiscalData[apiField] || "";
      }
    });

    // Disparar eventos de cambio para selects
    form.querySelectorAll('select').forEach(select => {
      select.dispatchEvent(new Event('change', { bubbles: true }));
    });
  }

  /**
   * Muestra un mensaje en el formulario fiscal
   * @param {string} message - Mensaje a mostrar
   * @param {string} type - Tipo de mensaje (success, error, warning, info)
   */
  showFiscalMessage(message, type = "info") {
    if (!this.hasFiscalMessageTarget) return;

    // Limpiar clases de mensajes anteriores
    this.fiscalMessageTarget.classList.remove(
      "bg-green-50", "border-green-400", "text-green-700", 
      "bg-red-50", "border-red-400", "text-red-700", 
      "bg-yellow-50", "border-yellow-400", "text-yellow-700",
      "bg-blue-50", "border-blue-400", "text-blue-700"
    );

    // Añadir nuevas clases según el tipo
    let bgColor, borderColor, textColor, icon;
    switch (type) {
      case "success":
        bgColor = "bg-green-50";
        borderColor = "border-green-400";
        textColor = "text-green-700";
        icon = "ri-checkbox-circle-line";
        break;
      case "error":
        bgColor = "bg-red-50";
        borderColor = "border-red-400";
        textColor = "text-red-700";
        icon = "ri-error-warning-line";
        break;
      case "warning":
        bgColor = "bg-yellow-50";
        borderColor = "border-yellow-400";
        textColor = "text-yellow-700";
        icon = "ri-alert-line";
        break;
      default: // info
        bgColor = "bg-blue-50";
        borderColor = "border-blue-400";
        textColor = "text-blue-700";
        icon = "ri-information-line";
    }

    this.fiscalMessageTarget.classList.add(
      bgColor, borderColor, textColor, "border-l-4", "p-4", "my-4", "rounded"
    );

    // Establecer contenido del mensaje
    this.fiscalMessageTarget.innerHTML = `
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="${icon} text-xl"></i>
        </div>
        <div class="ml-3">
          <p>${message}</p>
        </div>
      </div>
    `;

    // Mostrar mensaje
    this.fiscalMessageTarget.classList.remove("hidden");

    // Ocultar después de 5 segundos para mensajes de éxito
    if (type === "success") {
      setTimeout(() => {
        this.fiscalMessageTarget.classList.add("hidden");
      }, 5000);
    }
  }
}
