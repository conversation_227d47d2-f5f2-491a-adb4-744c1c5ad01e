import { Controller } from "@hotwired/stimulus"

/**
 * Controlador simplificado para manejar RUT y datos fiscales
 * 
 * Este controlador maneja:
 * 1. Validación y formateo de RUT
 * 2. Consulta de datos fiscales
 * 3. Autocompletado del formulario
 * 4. Guardado de datos
 */
export default class extends Controller {
  static targets = [
    "input",           // Input del RUT
    "output",          // Área de salida de resultados
    "error",           // Mensaje de error
    "errorContainer",  // Contenedor de error
    "spinner",         // Indicador de carga
    "fetchButton",     // Botón de consulta
    "clearButton",     // Botón para limpiar
    "resultsContainer",// Contenedor de resultados
    "fiscalForm",      // Formulario fiscal
    "businessName",    // Campo razón social
    "tradingName"      // Campo nombre de fantasía
  ]
  
  static values = {
    formatRut: { type: Boolean, default: true },
    validateRut: { type: Boolean, default: true },
    namespace: { type: String, default: 'owner' }
  }
  
  connect() {
    console.log("[Stimulus] Controlador SimplifiedRut conectado");
    
    // Inicializar estado
    this.isLoading = false;
    this.lastFetchedRut = null;
    
    // Configurar eventos
    this.setupEventListeners();
  }
  
  disconnect() {
    // Limpiar event listeners si es necesario
    this.inputTarget?.removeEventListener('input', this.formatRut);
    this.inputTarget?.removeEventListener('keypress', this.handleKeypress);
  }
  
  // Configura los event listeners
  setupEventListeners() {
    if (this.hasInputTarget) {
      this.inputTarget.addEventListener('input', this.formatRut.bind(this));
      this.inputTarget.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.fetchFiscalData(e);
        }
      });
    }
    
    // Configurar botón de limpieza
    if (this.hasClearButtonTarget) {
      this.clearButtonTarget.addEventListener('click', this.clearInput.bind(this));
    }
  }
  
  // Formatea el RUT mientras se escribe
  formatRut(event) {
    if (!this.formatRutValue) return;
    
    let rut = this.inputTarget.value.replace(/[^\dkK]/g, '').toUpperCase();
    
    if (rut.length > 1) {
      rut = rut.slice(0, -1).replace(/[^\d]/g, '') + '-' + rut.slice(-1);
      
      if (rut.length > 4) {
        rut = rut.slice(0, -5).replace(/\B(?=(\d{3})+(?!\d))/g, '.') + rut.slice(-5);
      }
    }
    
    this.inputTarget.value = rut;
  }
  
  // Limpia el campo de entrada
  clearInput(event) {
    if (event) event.preventDefault();
    if (this.hasInputTarget) {
      this.inputTarget.value = '';
      this.inputTarget.focus();
    }
    this.hideError();
    this.hideResults();
  }
  
  // Limpia los resultados
  clearResults(event) {
    if (event) event.preventDefault();
    this.hideResults();
  }
  
  // Limpia todo
  clearAll(event) {
    if (event) event.preventDefault();
    this.clearInput();
    this.hideFiscalForm();
  }
  
  // Muestra el spinner de carga
  showLoading() {
    this.isLoading = true;
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.classList.remove('hidden');
    }
    if (this.hasFetchButtonTarget) {
      this.fetchButtonTarget.disabled = true;
    }
  }
  
  // Oculta el spinner de carga
  hideLoading() {
    this.isLoading = false;
    if (this.hasSpinnerTarget) {
      this.spinnerTarget.classList.add('hidden');
    }
    if (this.hasFetchButtonTarget) {
      this.fetchButtonTarget.disabled = false;
    }
  }
  
  // Muestra un mensaje de error
  showError(message) {
    if (this.hasErrorTarget && this.hasErrorContainerTarget) {
      this.errorTarget.textContent = message;
      this.errorContainerTarget.classList.remove('hidden');
    }
  }
  
  // Oculta el mensaje de error
  hideError() {
    if (this.hasErrorContainerTarget) {
      this.errorContainerTarget.classList.add('hidden');
    }
  }
  
  // Muestra los resultados
  showResults() {
    if (this.hasResultsContainerTarget) {
      this.resultsContainerTarget.classList.remove('hidden');
    }
  }
  
  // Oculta los resultados
  hideResults() {
    if (this.hasResultsContainerTarget) {
      this.resultsContainerTarget.classList.add('hidden');
    }
  }
  
  // Muestra el formulario fiscal
  showFiscalForm() {
    if (this.hasFiscalFormTarget) {
      this.fiscalFormTarget.classList.remove('hidden');
    }
  }
  
  // Oculta el formulario fiscal
  hideFiscalForm() {
    if (this.hasFiscalFormTarget) {
      this.fiscalFormTarget.classList.add('hidden');
    }
  }
  
  // Consulta los datos fiscales
  async fetchFiscalData(event) {
    if (event) event.preventDefault();
    
    if (!this.hasInputTarget || !this.inputTarget.value.trim()) {
      this.showError('Por favor ingrese un RUT válido');
      return;
    }
    
    const rut = this.inputTarget.value.trim();
    this.lastFetchedRut = rut;
    
    try {
      this.showLoading();
      this.hideError();
      
      // Simular una llamada a la API (reemplazar con la implementación real)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Datos de ejemplo (simulando respuesta de la API)
      const mockData = {
        rut: rut,
        razon_social: `Empresa de Prueba ${Math.floor(Math.random() * 1000)}`,
        nombre_fantasia: `Fantasia ${Math.floor(Math.random() * 1000)}`,
        actividad_economica: 'Actividad de prueba',
        estado: 'ACTIVO',
        fecha_inscripcion: '2020-01-01'
      };
      
      // Mostrar resultados
      if (this.hasOutputTarget) {
        this.outputTarget.innerHTML = `
          <div class="space-y-2">
            <p><strong>RUT:</strong> ${mockData.rut}</p>
            <p><strong>Razón Social:</strong> ${mockData.razon_social}</p>
            <p><strong>Nombre Fantasía:</strong> ${mockData.nombre_fantasia}</p>
            <p><strong>Actividad:</strong> ${mockData.actividad_economica}</p>
            <p><strong>Estado:</strong> <span class="${mockData.estado === 'ACTIVO' ? 'text-green-600' : 'text-red-600'}">${mockData.estado}</span></p>
            <p><strong>Inscripción:</strong> ${mockData.fecha_inscripcion}</p>
          </div>
        `;
      }
      
      // Rellenar formulario fiscal
      if (this.hasBusinessNameTarget) {
        this.businessNameTarget.value = mockData.razon_social;
      }
      
      if (this.hasTradingNameTarget) {
        this.tradingNameTarget.value = mockData.nombre_fantasia;
      }
      
      // Mostrar resultados y formulario
      this.showResults();
      this.showFiscalForm();
      
    } catch (error) {
      console.error("Error al consultar datos fiscales:", error);
      this.showError('Error al consultar los datos fiscales. Por favor, intente nuevamente.');
    } finally {
      this.hideLoading();
    }
  }
  
  // Guarda los datos fiscales
  async saveFiscalData(event) {
    if (event) event.preventDefault();
    
    try {
      this.showLoading();
      
      // Simular guardado (reemplazar con implementación real)
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mostrar mensaje de éxito
      if (this.hasOutputTarget) {
        this.outputTarget.innerHTML = `
          <div class="p-3 bg-green-50 text-green-700 rounded border border-green-200">
            <p class="font-medium">¡Datos guardados correctamente!</p>
            <p class="text-sm">Los datos fiscales han sido actualizados.</p>
          </div>
        `;
      }
      
      // Ocultar formulario después de guardar
      setTimeout(() => {
        this.hideFiscalForm();
      }, 1500);
      
    } catch (error) {
      console.error("Error al guardar datos fiscales:", error);
      this.showError('Error al guardar los datos. Por favor, intente nuevamente.');
    } finally {
      this.hideLoading();
    }
  }
}
