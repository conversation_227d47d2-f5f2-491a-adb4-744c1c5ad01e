/**
 * Servicio para manejar operaciones relacionadas con datos fiscales
 */
export default class FiscalService {
  /**
   * Consulta datos fiscales a la API
   * @param {string} rut - RUT a consultar
   * @param {string} clientId - ID del cliente
   * @param {string} [namespace='owner'] - Namespace del controlador
   * @return {Promise<Object>} Datos fiscales
   */
  static async fetchFiscalData(rut, clientId, namespace = 'owner') {
    console.log('FiscalService: Consultando datos fiscales para RUT:', rut);
    
    try {
      // Validar RUT antes de consultar
      if (!rut || rut.trim() === '') {
        return {
          success: false,
          message: 'El RUT no puede estar vacío'
        };
      }
      
      // Asegurarse de tener un clientId válido
      if (!clientId) {
        clientId = window.location.pathname.split('/').filter(Boolean).pop();
        console.log('ClientId obtenido de URL:', clientId);
      }
      
      // Construir URL con el RUT codificado para evitar problemas con caracteres especiales
      const encodedRut = encodeURIComponent(rut.trim());
      const url = `/${namespace}/clients/${clientId}/fiscal_data.json?rut=${encodedRut}`;
      console.log('URL para consulta fiscal:', url);
      
      // Obtener token CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
      if (!csrfToken) {
        console.warn('No se encontró token CSRF para la consulta fiscal');
      }
      
      // Consultar a la API
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': csrfToken || ''
        },
        credentials: 'same-origin'
      });
      
      console.log('FiscalService: Respuesta recibida con status:', response.status);
      
      // Si hay un error HTTP, manejarlo adecuadamente
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error HTTP:', response.status, errorText);
        return {
          success: false,
          status: response.status,
          message: `Error en la consulta: ${response.status} - ${response.statusText}`
        };
      }
      
      // Verificar el tipo de contenido
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        // Parsear respuesta JSON
        const data = await response.json();
        console.log('FiscalService: Datos fiscales recibidos:', data);
        
        // Si hay datos y son válidos, agregarles la bandera de éxito
        if (data && (data.nombre_legal || data.razon_social)) {
          return {
            ...data,
            success: true
          };
        } else if (data && data.error) {
          // Si hay un error específico en el JSON
          return {
            success: false,
            message: data.error || 'No se encontraron datos para el RUT consultado'
          };
        } else {
          // Si no hay datos suficientes
          return {
            success: false,
            message: 'No se encontraron datos fiscales para el RUT proporcionado'
          };
        }
      } else {
        // Si la respuesta no es JSON
        const text = await response.text();
        console.error('Respuesta no es JSON:', text.substring(0, 200));
        return {
          success: false,
          message: 'Formato de respuesta inesperado'
        };
      }
    } catch (error) {
      console.error('FiscalService: Error al consultar datos fiscales:', error);
      return {
        success: false,
        message: error.message || 'Error desconocido al consultar datos fiscales'
      };
    }
  }
  
  /**
   * Guarda los datos fiscales en el servidor
   * @param {HTMLFormElement} form - Formulario con los datos fiscales
   * @param {Object} [options] - Opciones para el guardado
   * @param {boolean} [options.showLogs=true] - Si se deben mostrar logs
   * @param {string} [options.namespace='owner'] - Namespace del controlador
   * @return {Promise<Object>} - Resultado de la operación
   */
  static async saveFiscalData(form, options = {}) {
    const { showLogs = true, namespace = 'owner' } = options;
    const log = showLogs ? console.log : () => {};
    const logError = showLogs ? console.error : () => {};
    
    log('=== INICIO FiscalService.saveFiscalData ===');
    log('Iniciando guardado de datos fiscales...');
    
    try {
      // Verificar que el formulario sea válido
      if (!form || !(form instanceof HTMLFormElement)) {
        throw new Error('Se requiere un formulario HTML válido');
      }
      
      // Obtener el ID del cliente de la URL
      const clientId = window.location.pathname.split('/').filter(Boolean).pop();
      
      // Crear un objeto FormData con los datos del formulario
      const formData = new FormData(form);
      
      // Asegurarse de que los campos necesarios estén presentes
      formData.set('tab', 'fiscal');
      formData.set('_method', 'PATCH');
      
      // Asegurar que el campo de actividades se envíe correctamente como array vacío si está vacío
      const actividadesField = form.querySelector('[name="organization[actividades]"]') || 
                             form.querySelector('[name="organization[actividades][]"]');
      
      if (actividadesField) {
        if (!actividadesField.value) {
          // Si el campo existe pero está vacío, eliminar cualquier valor y agregar array vacío
          formData.delete('organization[actividades]');
          formData.append('organization[actividades][]', '');
          log('Campo actividades vacío, configurado como array vacío');
        }
      }
      
      // Obtener el token CSRF
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
      if (!csrfToken) {
        throw new Error('No se encontró el token CSRF');
      }
      
      // Construir la URL específica para datos fiscales
      const url = `/${namespace}/clients/${clientId}/update_organization_fiscal_data`;
      log(`URL para guardado fiscal: ${url}`);
      
      // Enviar la solicitud
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        credentials: 'same-origin'
      });
      
      log(`Respuesta recibida con status: ${response.status}`);
      
      // Procesar la respuesta según su tipo
      const contentType = response.headers.get('content-type');
      let data;
      let responseText;
      
      try {
        responseText = await response.text();
        if (contentType && contentType.includes('application/json')) {
          try {
            data = JSON.parse(responseText);
            log('Datos JSON recibidos:', data);
          } catch (e) {
            logError('Error al parsear JSON:', e);
            throw new Error('Error al procesar la respuesta JSON del servidor');
          }
        } else if (response.ok) {
          // Si la respuesta es exitosa pero no es JSON, crear un objeto de éxito
          data = { success: true };
          log('Respuesta exitosa (no-JSON)');
        } else {
          // Si hay error y no es JSON, extraer mensaje de error si existe
          logError('Error no-JSON:', responseText.substring(0, 200));
          throw new Error('Error en el servidor');
        }
      } catch (e) {
        logError('Error al leer respuesta:', e);
        throw new Error('Error al procesar la respuesta del servidor');
      }
      
      // Verificar si hay errores en la respuesta
      if (!response.ok || (data && data.success === false)) {
        const errorMessage = (data && data.message) || 
                           (data && data.errors ? Object.values(data.errors).flat().join(', ') : 
                           'Error en la respuesta del servidor');
        logError('Error en respuesta:', errorMessage);
        
        return {
          success: false,
          message: errorMessage
        };
      }
      
      log('Datos fiscales guardados exitosamente');
      log('=== FIN FiscalService.saveFiscalData (éxito) ===');
      
      return {
        success: true, 
        data
      };
    } catch (error) {
      logError('Error en FiscalService.saveFiscalData:', error);
      logError('Stack:', error.stack);
      log('=== FIN FiscalService.saveFiscalData (error) ===');
      
      return {
        success: false,
        message: error.message || 'Error al guardar datos fiscales'
      };
    }
  }
  
  /**
   * Prepara un objeto de datos para activar el autocompletado fiscal
   * @param {Object} fiscalData - Datos fiscales recibidos de la API
   * @returns {Object} - Datos normalizados para autocompletado
   */
  static prepareAutocompletionData(fiscalData) {
    if (!fiscalData) return {};
    
    const normalizedData = {};
    
    // Mapeo de campos comunes
    const fieldMappings = {
      nombre_legal: ['nombre_legal', 'razon_social', 'nombre'],
      nombre_fantasia: ['nombre_fantasia', 'nombre_comercial', 'nombre_de_fantasia'],
      tipo_entidad: ['tipo_entidad', 'tipo_empresa', 'tipo_contribuyente'],
      direccion: ['direccion', 'domicilio_fiscal'],
      ciudad: ['ciudad', 'localidad'],
      departamento: ['departamento', 'provincia'],
      telefono: ['telefono', 'telefono_contacto'],
      email: ['email', 'correo', 'email_contacto'],
      actividades: ['actividades', 'giros', 'rubros'],
      estado: ['estado', 'situacion_fiscal']
    };
    
    // Normalizar datos usando los mapeos
    Object.entries(fieldMappings).forEach(([normalKey, possibleKeys]) => {
      // Buscar el primer valor no vacío entre todas las claves posibles
      for (const key of possibleKeys) {
        if (fiscalData[key] !== undefined && fiscalData[key] !== null) {
          normalizedData[normalKey] = fiscalData[key];
          break;
        }
      }
    });
    
    // Asegurarse de que actividades siempre sea un array
    if (normalizedData.actividades && !Array.isArray(normalizedData.actividades)) {
      normalizedData.actividades = [normalizedData.actividades];
    } else if (!normalizedData.actividades) {
      normalizedData.actividades = [];
    }
    
    return normalizedData;
  }
}
