/**
 * Servicio JavaScript vanilla para manejo de consultas DGI
 * 
 * Este script proporciona una solución temporal para consultar datos fiscales
 * sin depender de Stimulus mientras se resuelven los problemas de configuración.
 */

document.addEventListener('DOMContentLoaded', function() {
  // Debug info
  console.log('[Debug] Servicio vanilla DGI cargado');
  
  // Capturar elementos DOM
  const fetchButton = document.getElementById('dgi-fetch-button');
  const rutInput = document.getElementById('fiscal_rut_input');
  const spinner = document.getElementById('dgi-fetch-spinner');
  
  if (fetchButton && rutInput) {
    console.log('[Debug] Botón DGI y campo RUT encontrados');
    
    // Función para mostrar/ocultar spinner
    function toggleSpinner(show) {
      if (spinner) {
        spinner.classList.toggle('hidden', !show);
      }
      if (fetchButton) {
        fetchButton.disabled = show;
      }
    }
    
    // Función para limpiar un RUT (quitar caracteres especiales)
    function cleanRut(rut) {
      return rut.replace(/[^0-9]/g, '');
    }
    
    // Función para mostrar mensaje de error
    function showError(message) {
      const errorContainer = document.createElement('div');
      errorContainer.className = 'mt-3 p-2 text-sm font-medium text-red-700 bg-red-100 rounded';
      errorContainer.textContent = message;
      
      // Agregar al DOM cerca del botón
      fetchButton.parentNode.appendChild(errorContainer);
      
      // Auto-eliminar después de 5 segundos
      setTimeout(() => errorContainer.remove(), 5000);
    }
    
    // Función para mostrar mensaje de éxito
    function showSuccess(message) {
      const successContainer = document.createElement('div');
      successContainer.className = 'mt-3 p-2 text-sm font-medium text-green-700 bg-green-100 rounded';
      successContainer.textContent = message;
      
      // Agregar al DOM cerca del botón
      fetchButton.parentNode.appendChild(successContainer);
      
      // Auto-eliminar después de 5 segundos
      setTimeout(() => successContainer.remove(), 5000);
    }
    
    // Función para auto-completar el formulario con los datos fiscales
    function autocompleteFiscalForm(data) {
      console.log('[Debug] Auto-completando formulario con datos:', data);
      
      // Mapeo de campos
      const fieldMapping = {
        nombre_legal: 'organization_nombre_legal',
        nombre_fantasia: 'organization_nombre_fantasia',
        tipo_entidad: 'organization_tipo_entidad',
        actividades: 'organization_actividades',
        direccion: 'organization_direccion',
        ciudad: 'organization_ciudad',
        departamento: 'organization_departamento',
        email: 'organization_email',
        telefono: 'organization_telefono'
      };
      
      // Completar campos
      Object.keys(fieldMapping).forEach(key => {
        const input = document.getElementById(fieldMapping[key]);
        if (input && data[key]) {
          input.value = data[key];
        }
      });
      
      showSuccess('Datos fiscales cargados correctamente');
    }
    
    // Función principal para consultar datos fiscales
    async function fetchFiscalData() {
      const rut = rutInput.value;
      const cleanedRut = cleanRut(rut);
      
      if (!cleanedRut || cleanedRut.length < 11) {
        showError('RUT inválido o incompleto');
        return;
      }
      
      console.log(`[Debug] Consultando datos fiscales para RUT: ${cleanedRut}`);
      toggleSpinner(true);
      
      try {
        // URL construida con el mismo formato que el endpoint del backend
        const url = `/owner/search/fiscal_data?rut=${encodeURIComponent(cleanedRut)}`;
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          }
        });
        
        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('[Debug] Datos fiscales recibidos:', data);
        
        if (data.error) {
          throw new Error(data.error);
        }
        
        // Auto-completar formulario
        autocompleteFiscalForm(data);
        
      } catch (error) {
        console.error('[Error] Error al consultar datos fiscales:', error);
        showError(error.message || 'Error al consultar datos fiscales');
      } finally {
        toggleSpinner(false);
      }
    }
    
    // Agregar evento al botón
    fetchButton.addEventListener('click', fetchFiscalData);
  } else {
    console.warn('[Debug] No se encontró el botón DGI o el campo RUT');
  }
});
