/**
 * Servicio para consultar datos fiscales a través de la API
 * Responsabilidad única: obtener los datos fiscales por RUT
 */
class FiscalApiService {
  constructor(options = {}) {
    this.namespace = options.namespace || 'owner';
    this.baseUrl = options.baseUrl || `/${this.namespace}/search/fiscal_data`;
  }

  /**
   * Consulta los datos fiscales de un RUT en la API
   * @param {string} rut - RUT a consultar (con o sin formato)
   * @param {string} clientId - ID del cliente para guardado automático (opcional)
   * @returns {Promise} - Promesa con los datos fiscales o error
   */
  async fetchFiscalData(rut, clientId = null) {
    if (!rut) {
      return Promise.reject(new Error('Debe proporcionar un RUT para consultar'));
    }

    // Limpiar el RUT (remover puntos y guiones)
    const cleanRut = this.cleanRut(rut);
    console.log(`Consultando datos fiscales para RUT: ${cleanRut}`);

    try {
      // Construir la URL con los parámetros necesarios
      const url = new URL(this.baseUrl, window.location.origin);
      url.searchParams.append('rut', cleanRut);
      
      // Si se proporcionó un clientId, agregarlo a la consulta
      if (clientId) {
        url.searchParams.append('client_id', clientId);
        console.log(`Incluyendo client_id en la consulta: ${clientId}`);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content || ''
        },
        credentials: 'same-origin'
      });

      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status} - ${response.statusText}`);
      }

      const result = await response.json();
      
      // Validar resultado
      if (!result.success && result.error) {
        throw new Error(result.error);
      }

      console.log('Datos fiscales obtenidos correctamente:', result);
      return result;

    } catch (error) {
      console.error('Error al consultar datos fiscales:', error);
      throw error;
    }
  }

  /**
   * Limpia un RUT para consulta (elimina puntos y guiones)
   * @param {string} rut - RUT a limpiar
   * @returns {string} - RUT limpio
   */
  cleanRut(rut) {
    return rut.replace(/[^\dK]/g, '');
  }
}

export default FiscalApiService;
