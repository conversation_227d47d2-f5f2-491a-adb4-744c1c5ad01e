/* Importar estilos de Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * ARCHIVO DE ESTILOS CENTRALIZADO
 * 
 * Este archivo contiene todos los estilos de la aplicación.
 * Para mantener consistencia y evitar conflictos, todos los estilos
 * se definen aquí y no en archivos separados.
 */

/* Definir la paleta de colores */
:root {
  /* Colores primarios */
  --color-primary: #5A67D8; /* Violeta */
  --color-primary-light: #939DE9; /* Violeta claro */
  --color-primary-dark: #2E368E; /* Violeta oscuro */

  /* Colores secundarios */
  --color-secondary: #388e3c; /* Verde */
  --color-secondary-light: #81c784; /* Verde claro */
  --color-secondary-dark: #1b5e20; /* Verde oscuro */

  /* Colores de fondo */
  --color-background: #f5f5f5; /* Gris claro */
  --color-surface: #ffffff; /* Blanco */
  --color-navbar: #FFFFFF; /* Blanco puro */
  --color-navbar-text: #4A3B76; /* Texto púrpura para contraste */
  --color-navbar-border: #E6E6E6; /* Borde gris muy claro */
  --color-navbar-accent: #5D4B9C; /* Acento púrpura */

  /* Otros colores */
  --color-error: #f44336; /* Rojo */
  --color-warning: #ff9800; /* Amarillo */
  --color-success: #4caf50; /* Verde */
  --color-info: #2196f3; /* Azul */

  /* Color del borde al hacer hover */
  --color-border-hover: #5A67D8; /* Violeta */

  /* Variables para espaciado */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Componentes con utilidades de Tailwind */
@layer components {  
  /* === CONTENEDORES DE CONTENIDO === */
  /* Estilos que anteriormente estaban en components/content_container.css */
  .container-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0 1rem;
  }
  
  .content-container {
    width: 100%;
    max-width: 1280px; /* 80rem - Equivalente a max-w-7xl en Tailwind */
    padding: 1.5rem 0;
  }
  
  .content-inner {
    width: 100%;
    max-width: 800px; /* 50rem - Ancho máximo para mejorar la legibilidad */
    margin: 0 auto;
  }
  
  @media (max-width: 640px) {
    .container-wrapper {
      padding: 0 0.75rem;
    }
  
    .content-container {
      padding: 1rem 0;
    }
  
    .content-inner {
      padding: 0 0.5rem;
    }
  }
  
  @media (min-width: 1536px) {
    .content-container {
      max-width: 1536px; /* 96rem - Para pantallas muy grandes */
    }
  }
  
  /* === CONTENEDORES DE FORMULARIO === */
  /* Estilos que anteriormente estaban en components/form_container.css */
  .form-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb; /* Borde sutil para mejor definición */
  }
  
  @media (max-width: 640px) {
    .form-container {
      padding: 1rem;
      overflow: visible;
    }
  
    /* Asegurarse de que los botones sean visibles */
    .form-container button,
    .form-container a {
      margin-bottom: 0.75rem;
      display: block;
      width: 100%;
    }
  }
  
  /* === CAMPOS DE FORMULARIO === */
  /* Estilos que anteriormente estaban en components/form_fields.css */
  
  /* Base input styles */
  .form-field-input {
    @apply w-full rounded-lg border-2 border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200;
    @apply focus:border-primary-500 focus:ring-2 focus:ring-primary-200 focus:ring-opacity-50;
    @apply hover:border-gray-400;
    @apply disabled:border-gray-200 disabled:bg-gray-100 disabled:text-gray-600 disabled:cursor-not-allowed;
    @apply read-only:border-gray-200 read-only:bg-gray-50 read-only:text-gray-600 read-only:cursor-default;
    min-height: 44px;
  }
  
  /* Textarea specific styles */
  .form-field-input.textarea,
  textarea.form-field-input {
    @apply min-h-[120px] resize-y;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    line-height: 1.5;
  }
  
  /* Select specific styles */
  .form-field-select {
    @apply form-field-input pr-10 bg-white bg-no-repeat bg-right-2 bg-[length:1.5em_1.5em];
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  /* Checkbox and radio button styles */
  .form-field-checkbox,
  .form-field-radio {
    @apply h-5 w-5 rounded border-gray-300 text-primary-600 focus:ring-primary-500;
  }
  
  /* Error states */
  .form-field-input-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-200;
  }
  
  /* Label styles */
  .form-field-label {
    @apply mb-2 block text-sm font-medium text-gray-700;
  }
  
  .form-field-required:after {
    @apply ml-1 text-red-500;
    content: '*';
  }
  
  /* === ESTILOS DE BOTONES === */
  /* Estos estilos deben ser utilizados consistentemente en toda la aplicación */
  
  /* Botón base unificado */
  .btn, button, a.btn, button.btn, a.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    transition: all 0.2s ease-in-out;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    cursor: pointer;
  }
  
  /* Tamaños de botones */
  .btn-sm, .btn.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    line-height: 1rem;
    border-radius: 0.25rem;
  }

  .btn-lg, .btn.btn-lg {
    padding: 0.625rem 1.25rem;
    font-size: 1rem;
    line-height: 1.5rem;
    border-radius: 0.5rem;
  }
  
  /* Estados */
  .btn:disabled, .btn.btn-disabled, button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  .btn:focus, button:focus, a.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
  }
  
  /* Variantes de botones */
  .btn-primary, .btn.btn-primary {
    color: #ffffff;
    background-color: #4f46e5;
    border: 1px solid #4f46e5;
  }
  
  .btn-primary:hover {
    background-color: #4338ca;
    border-color: #3730a3;
  }
  
  .btn-secondary, .btn.btn-secondary {
    color: #374151;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
  }
  
  .btn-secondary:hover {
    background-color: #f9fafb;
  }
  
  .btn-success, .btn.btn-success {
    color: #ffffff;
    background-color: #10b981;
    border: 1px solid #10b981;
  }
  
  .btn-success:hover {
    background-color: #0d9f6e;
    border-color: #0d9f6e;
  }
  
  .btn-danger, .btn.btn-danger {
    color: #ffffff;
    background-color: #ef4444;
    border: 1px solid #ef4444;
  }
  
  .btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
  }
  
  .btn-warning, .btn.btn-warning {
    color: #92400e;
    background-color: #fef3c7;
    border: 1px solid #fde68a;
  }
  
  .btn-warning:hover {
    background-color: #fde68a;
    border-color: #fcd34d;
  }
  
  .btn-info, .btn.btn-info {
    color: #1e40af;
    background-color: #dbeafe;
    border: 1px solid #bfdbfe;
  }
  
  .btn-info:hover {
    background-color: #bfdbfe;
    border-color: #93c5fd;
  }
  /* Estilos para columnas fijas en tablas */
  .table-sticky-container {
    overflow-x: auto;
    position: relative;
    width: 100%;
  }
  
  /* Estructura básica de la tabla con columna fija */
  .table-with-sticky-column {
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
    border-spacing: 0;
  }
  
  /* Estilos para el encabezado de columna fija */
  .sticky-column-header {
    position: -webkit-sticky; /* Safari */
    position: sticky !important;
    right: 0 !important;
    z-index: 2 !important;
    background-color: #F9FAFB !important;
    box-shadow: -5px 0 8px -5px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Estilos para las celdas de columna fija */
  .sticky-column-cell {
    position: -webkit-sticky; /* Safari */
    position: sticky !important;
    right: 0 !important;
    z-index: 1 !important;
    background-color: #FFFFFF !important;
    box-shadow: -5px 0 8px -5px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Logo */
  .logo {
    width: 55px;
    height: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-weight: normal;
    letter-spacing: 0.1em;
    font-size: 12px;
    font-family: 'Roboto', sans-serif;
    border-radius: 50%;
    border: 2px solid;
    background-color: transparent;
    margin: 0 auto;
  }

  /* Estilos del Navbar */
  .custom-nav {
    background: var(--color-navbar);
    color: var(--color-navbar-text);
    padding: 0.75rem 1rem;
    font-family: 'Roboto', sans-serif;
    border-bottom: 1px solid var(--color-navbar-border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /* Media queries para dispositivos móviles */
  @media (max-width: 640px) {
    .custom-nav {
      padding: 0.5rem 0.75rem;
    }
  }

  .custom-nav::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--color-navbar-accent), transparent);
    opacity: 0.3;
  }

  /* Estilos del formulario de añadir producto y de inicio de sesión */
  .form-container, .login-form {
    max-width: 30rem;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--color-surface);
    border-radius: 0.5rem;
    box-shadow: 0 0 0.5rem rgba(0, 0, 0, 0.1);
  }

  .form-container h1, .login-form h1 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  /* Nuevas clases estandarizadas */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700;
    @apply focus:outline-none focus:ring-2 focus:ring-indigo-500;
    @apply transition-colors duration-200 ease-in-out;
  }

  .form-button {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent;
    @apply text-sm font-medium rounded-md text-white bg-indigo-600;
    @apply hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
    @apply transition-colors duration-200 ease-in-out;
    height: 45px;
  }

  .card-container {
    @apply bg-white p-6 rounded-lg border border-gray-200 shadow-sm;
    @apply hover:shadow-md transition-shadow duration-200 ease-in-out;
  }

  .section-title {
    @apply text-xl font-bold text-gray-800 mb-4;
  }

  /* Se elimina la definición duplicada de .custom-btn porque ahora usamos .btn */

  /* Se elimina la definición duplicada de .primary-btn porque ahora usamos .btn-primary */

  /* Estilos de botones simplificados y sin efectos de brillo */
  /* Se han eliminado los estilos duplicados y los efectos de gradiente */

  /* Botón tipo enlace */
  .link-btn, .btn.btn-link, a.btn-link, button.btn-link {
    background-color: transparent !important;
    color: #4f46e5 !important; /* indigo-600 */
    text-decoration: underline !important;
    padding: 0 !important;
    font-weight: normal !important;
    cursor: pointer !important;
    box-shadow: none !important;
  }
  
  .link-btn:hover, .btn.btn-link:hover, a.btn-link:hover, button.btn-link:hover {
    color: #4338ca !important; /* indigo-700 */
  }

  /* Se elimina la definición duplicada de tamaños porque ahora están integrados en .btn */

  /* Botones con ancho fijo */
  .btn-fixed-sm {
    width: 100px;
    box-sizing: border-box;
  }

  .btn-fixed-md {
    width: 120px;
    box-sizing: border-box;
  }

  .btn-fixed-lg {
    width: 140px;
    box-sizing: border-box;
  }

  /* Botones de ancho completo */
  .full-width-btn {
    width: 100%;
  }

  /* Botones con iconos */
  .btn i[class^="ri-"], 
  .btn i[class*=" ri-"],
  .btn-icon svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.375rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Eliminar efectos de brillo en botones */
  .btn, .btn * {
    text-shadow: none !important;
    box-shadow: none !important;
    background-image: none !important;
  }
  
  .btn:hover, .btn:focus, .btn:active {
    transform: none !important;
    filter: none !important;
  }

  /* Botones de estado (activo/inactivo) */
  .btn-state-active {
    @apply inline-flex items-center px-4 py-2 border border-green-500 text-sm font-medium rounded-md text-green-700 bg-green-50;
  }

  .btn-state-inactive {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50;
  }

  /* Tarjeta de organización activa */
  .org-card-active {
    @apply relative bg-green-50 p-6 rounded border-2 border-green-300 shadow-md;
  }

  .org-card-inactive {
    @apply bg-gray-50 p-6 rounded border border-gray-200;
  }

  .org-active-badge {
    @apply absolute -top-3 -right-3 bg-green-500 text-white text-xs px-2 py-1 rounded-full;
  }

  /* Estilos para el spinner de carga */
  .spinner-container {
    @apply flex items-center justify-center;
    @apply fixed top-0 left-0 right-0 bottom-0;
    @apply bg-black bg-opacity-50 z-50;
  }

  .spinner {
    @apply w-12 h-12 rounded-full;
    @apply border-4 border-indigo-200;
    @apply border-t-4 border-t-indigo-600;
    animation: spin 1s linear infinite;
  }

  .spinner-overlay {
    @apply absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50;
    @apply rounded-md;
  }

  .spinner-sm {
    width: 30px;
    height: 30px;
    border: 4px solid #e2e8f0; /* Color gris claro para el borde */
    border-top: 4px solid #6366F1; /* Color indigo para el borde superior */
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
  }

  .spinner-text {
    @apply ml-3 text-sm font-medium text-indigo-700;
    font-weight: bold;
  }

  /* Spinner para botones */
  .btn-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: -0.125em;
    border: 0.15em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spin 0.75s linear infinite;
    margin-right: 0.5rem;
  }

  /* Ocultar el spinner cuando no está cargando */
  .btn-spinner.hidden {
    display: none;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Estilos para el sidebar */
.sidebar {
  @apply fixed top-0 left-0 h-screen w-64 shadow-lg z-50 transform transition-transform duration-300 ease-in-out;
  /* Posición inicial: oculto */
  transform: translateX(-100%);
}

/* Estilos para el overlay del sidebar */
#sidebar-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300 opacity-0 md:hidden;
}

/* Clase para cuando el sidebar está abierto */
.sidebar-open .sidebar {
  @apply translate-x-0;
  transform: translateX(0) !important;
}

/* Clase para cuando el sidebar está cerrado */
.sidebar-closed .sidebar {
  @apply -translate-x-full;
  transform: translateX(-100%) !important;
}

/* Estilos para los enlaces del sidebar */
.sidebar-link {
  @apply flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 transition-colors duration-200;
}

.sidebar-link.active {
  @apply bg-indigo-50 text-indigo-600 border-r-4 border-indigo-600;
}

/* Efectos al pasar el ratón ahora están definidos para cada variante */

/* Media queries para diseño responsivo */
@media (max-width: 640px) {
  .form-container, .login-form {
    max-width: 100%;
    padding: 1rem;
  }

  /* Los tamaños de fuente ahora se manejan con las clases .btn .btn-sm|btn-lg */
}

@media (max-width: 768px) {
  .btn-fixed-lg, .btn-fixed-md {
    width: 100%;
  }
}

/* Media query para dispositivos muy pequeños */
@media (max-width: 360px) {
  .btn-sm, .btn-md, .btn-lg {
    min-width: 0;
    width: 100%;
  }
}

/* Los estilos del botón de logout se han eliminado ya que ahora está integrado en el sidebar */

/* === Form Field Styles === */
@layer components {
  input.form-field-input,
  textarea.form-field-input,
  select.form-field-input {
    @apply w-full px-3 py-2 border-2 border-gray-400 rounded-md bg-white text-gray-900 placeholder-gray-500 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 !important;
    border: 2px solid #9CA3AF !important;
    background-color: #fff !important;
  }
  .form-field-input-error {
    @apply border-red-500 bg-red-50 focus:border-red-500 focus:ring-red-200 !important;
    border-color: #ef4444 !important;
  }
  .form-field-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  /* Input de búsqueda estilo TailAdmin */
  .search-input {
    display: block;
    width: 100%;
    max-width: 430px;
    height: 2.75rem; /* h-11 */
    padding: 0.625rem 1rem 0.625rem 2.5rem; /* Ajustado para el ícono del HTML */
    font-size: 0.875rem; /* text-sm */
    line-height: 1.25rem;
    color: #1f2937; /* text-gray-800 */
    background-color: transparent;
    border: 1px solid #e5e7eb; /* border-gray-200 */
    border-radius: 0.5rem; /* rounded-lg */
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-theme-xs */
    transition: all 0.2s ease-in-out;
  }
  
  .search-input:focus {
    border-color: #93c5fd; /* focus:border-brand-300 */
    outline: none;
    box-shadow: 0 0 0 3px rgba(191, 219, 254, 0.5); /* focus:ring-brand-500/10 */
    --tw-ring-color: rgba(99, 102, 241, 0.1); /* focus:ring-3 */
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }
  
  .search-input::placeholder {
    color: #9ca3af; /* placeholder-gray-400 */
    opacity: 1;
  }
  
  .search-input:hover {
    border-color: #d1d5db; /* hover:border-gray-300 */
  }
  
  /* Estilos para modo oscuro */
  .dark .search-input {
    background-color: rgba(255, 255, 255, 0.03); /* dark:bg-white/[0.03] */
    border-color: #374151; /* dark:border-gray-800 */
    color: #f3f4f6; /* dark:text-white/90 */
  }
  
  .dark .search-input::placeholder {
    color: #9ca3af; /* dark:placeholder-white/30 */
    opacity: 0.7;
  }
  
  /* Eliminadas las variantes de tamaño para simplificar */
}

