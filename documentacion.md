
# Documentación Unificada del Sistema de Gestión para Contadores y Clientes

## 1. Arquitectura General

### Visión
Sistema modular para gestionar contadores, colaboradores, organizaciones y clientes, orientado a roles con namespaces separados, interfaz moderna y principios de desarrollo sostenibles.

### Stack Tecnológico
- **Backend**: Ruby on Rails (v8.0.2)
- **Frontend**: TailwindCSS, StimulusJS
- **Base de Datos**: SQLite (dev), PostgreSQL (prod)
- **Autenticación**: Personalizada (sin Devise)
- **Autorización**: Pundit
- **Testing**: RSpec, Capybara

### Principios
- Simplicidad primero (YAGNI)
- DRY: no repetir código
- SRP: una responsabilidad por clase
- Convenciones > Configuración

### Estructura
```
app/
├── controllers/
│   ├── main/            # Público y redirección
│   ├── superadmin/      # Gestión global
│   ├── owner/           # Gestión de organizaciones
│   ├── collaborator/    # Gestión operativa
│   ├── client/          # Vista cliente
│   └── concerns/
├── models/
├── views/
│   ├── shared/          # Partials reutilizables
├── services/            # Lógica compleja
├── policies/            # Pundit
├── javascript/controllers/  # StimulusJS
```

### Roles y Namespaces
- **Main**: landing, redirección por rol
- **Superadmin**: acceso total, configuración global
- **Owner**: admins de organización
- **Collaborator**: operadores
- **Client**: usuarios finales
- **Support/Admin**: impersonación, soporte

### Controladores: Convenciones
- `before_action :authenticate_user!`
- `authorize` + `policy_scope`
- Controladores delgados pero legibles

### Servicios
- Usar solo si: lógica compleja, múltiples modelos o integración externa

## 2. Guía de Estilo y Diseño UI

### Principios de Diseño
- Funcional > Estético
- Layout responsive y limpio
- Formularios de ancho completo
- Navegación clara y jerárquica

### Componentes UI
- **Botones**: primario, secundario, enlace
- **Formularios**: `form_with`, validaciones en línea, errores claros
- **Notificaciones**: `success`, `error`, `info`, `warning`
- **Pestañas**: navegación con íconos, centradas
- **Tarjetas**: contenedores con sombra y bordes redondeados

### Tipografía y Colores
- Fuente: **Inter**
- Usar variables CSS: `--color-primary`, `--color-error`, etc.
- Clases Tailwind para layout y estados (`hover:`, `focus:`, `space-x-4`, etc.)

### Espaciado
- Márgenes/padding en múltiplos de 4px
- Utilizar `space-y-6`, `gap-4`, `px-6`, `py-6`

### Responsive
- Mobile-first
- Breakpoints: `sm`, `md`, `lg`, `xl`, `2xl`
- Utilizar utilidades `hidden`, `block`, `flex`, `grid-cols`

## 3. Funcionalidades Clave del Sistema

### Impersonación
- Permite a superadmins/admins ver la app como otro usuario
- Barra amarilla indica estado
- Botón para salir del modo impersonado

### Gestión Completa desde Panel Superadmin
- Configuración del sistema
- Activación de features
- Documentación editable desde UI

### Sistema de Invitaciones
- `invitation_token`, `invitation_sent_at`, etc.
- Seeds adaptados a este flujo

### Terminología
- Uso actualizado de "colaboradores" (antes "miembros")

### Eliminación de Usuarios
- Verificación de dependencias (clientes/organización)
- Fallback automático al superadmin
- Registro de auditoría

## 4. Buenas Prácticas

### ✅ Buenas Prácticas Generales

#### 1. Código
- Nombrado consistente: `snake_case` para Ruby, `camelCase` en JS, `kebab-case` para clases CSS personalizadas.
- Un archivo, una responsabilidad.
- Usá scopes en modelos para consultas comunes.
- Validaciones y callbacks: solo los necesarios y bien comentados.

#### 2. Controladores
- Evitá lógica compleja (→ servicios o modelos).
- Siempre autorizá con Pundit (`authorize`, `policy_scope`).
- Prefiere redirecciones claras (`redirect_to` con `notice`/`alert`).

#### 3. Vistas y UI
- Siempre usá clases de Tailwind predefinidas.
- Agrupá campos de formularios lógicamente.
- No dupliques estructuras visuales, usá partials (`render 'shared/...'`).
- Formularios accesibles: etiquetas claras, validaciones visibles.

#### 4. JavaScript (Stimulus)
- Un controller Stimulus = una función clara.
- Usá `data-*` y `targets` en lugar de `id`s o `classes` para lógica.

#### 5. Testing
- Cada modelo debe tener sus validaciones y relaciones cubiertas.
- Los features deben simular el comportamiento real del usuario.
- Tests deben ser claros, rápidos y predecibles.

#### 6. Documentación
- Cada nueva funcionalidad debe estar documentada.
- Si modifica comportamiento visual, actualizar también `UI_DESIGN_GUIDELINES.md`.

#### 7. Git
- Commits chicos, uno por cambio o fix.
- Usá `fix:`, `feat:`, `refactor:` como prefijo si seguís convención semántica.

## 5. Componentes Reutilizables

### Partials Compartidos para Clientes
Todos los partials se encuentran en `app/views/shared/clients/` y están diseñados para ser utilizados tanto en el namespace `owner` como `collaborator`.

#### `_client_tabs.html.erb`
Muestra las pestañas de navegación para la vista detallada de un cliente.

**Parámetros:**
- `client`: El objeto cliente (User) a mostrar
- `namespace`: El namespace actual ('owner' o 'collaborator') 
- `current_tab`: La pestaña actualmente seleccionada ('info', 'fiscal', 'documents', 'activity')

```erb
<%= render 'shared/clients/client_tabs', 
           client: @client, 
           namespace: 'owner', 
           current_tab: @tab || 'info' %>
```

#### `_card.html.erb`
Contenedor para secciones de información con título y acciones opcionales.

**Parámetros:**
- `title`: El título de la tarjeta (requerido)
- `subtitle`: Subtítulo opcional para la tarjeta
- `card_id`: ID opcional para la tarjeta (usado para referencias de JS)
- `card_class`: Clases CSS adicionales para la tarjeta
- `header_class`: Clases CSS adicionales para el encabezado
- `body_class`: Clases CSS adicionales para el cuerpo
- `header_actions`: Contenido HTML para botones o acciones en el encabezado
- El contenido principal se pasa como bloque

```erb
<!-- Uso básico -->
<%= render 'shared/clients/card', title: 'Información General', card_id: 'info-card' do %>
  <!-- Contenido de la tarjeta -->
<% end %>

<!-- Con acciones en el encabezado -->
<%= render 'shared/clients/card', 
           title: 'Información General', 
           card_id: 'info-card',
           header_actions: link_to("Editar", edit_path, class: "text-sm text-indigo-600") do %>
  <!-- Contenido de la tarjeta -->
<% end %>
```

#### `_access_status.html.erb`
Muestra el estado de acceso del cliente a la plataforma, con botones para habilitar/deshabilitar y enviar invitaciones.

**Parámetros:**
- `client`: El objeto cliente (User)
- `organization`: La organización actual
- `namespace`: El namespace actual ('owner' o 'collaborator')

```erb
<%= render 'shared/clients/access_status', 
           client: @client, 
           namespace: 'owner', 
           organization: current_organization %>
```

#### `_section_header.html.erb`
Muestra un encabezado de sección estándar con título, subtítulo opcional y acciones.

**Parámetros:**
- `title`: Título de la sección (requerido)
- `subtitle`: Texto para mostrar como subtítulo (opcional)
- `actions`: Contenido HTML para los botones de acción (opcional)

```erb
<!-- Uso básico -->
<%= render 'shared/clients/section_header', title: 'Gestionar Clientes' %>

<!-- Con subtítulo y acciones -->
<%= render 'shared/clients/section_header', 
           title: 'Gestionar Clientes',
           subtitle: 'Administra los clientes de tu organización',
           actions: link_to("Nuevo Cliente", new_owner_client_path, class: "btn btn-primary") %>
```

#### `_organization_info.html.erb`
Muestra y permite editar la información de la organización asociada al cliente.

**Parámetros:**
- `client_organization`: El objeto organización del cliente
- `show_fiscal_link`: Booleano para mostrar/ocultar enlaces a datos fiscales
- `namespace`: El namespace actual ('owner' o 'collaborator')
- `editable`: Booleano para determinar si el nombre de la organización es editable (opcional, por defecto: false)

#### `_fiscal_data.html.erb`
Formulario para mostrar y editar los datos fiscales del cliente.

**Parámetros:**
- `client_organization`: El objeto organización del cliente
- `namespace`: El namespace actual ('owner' o 'collaborator')
- `can_edit`: Booleano para determinar si el usuario puede editar los datos

### Controlador Stimulus para RUT
El controlador Stimulus `rut_controller.js` se utiliza para manejar la validación y formateo de RUTs en tiempo real.

```erb
<%= text_field_tag "organization[rut]", value, 
                data: { 
                  controller: "rut", 
                  action: "change->rut#format" 
                } %>
```

### Convenciones para Partials Compartidos
1. Siempre pasar `namespace` como 'owner' o 'collaborator' para URLs correctas
2. Implementar helper `can_edit_client?(client)` para determinar permisos
3. Utilizar `RutValidatorService.format` para formateo consistente de RUTs
4. Usar el set de iconos Remix (ri-*) para mantener consistencia visual

## 6. Desarrollo y Mantenimiento

### Git Workflow
- `main`: producción
- `feature/*`: desarrollo

### Commits
- Descriptivos y atómicos

### Archivos .env
```bash
SECRET_KEY_BASE=...
MAIL_HOST=...
MAIL_USERNAME=...
MAIL_PASSWORD=...
```

### Producción
- SSL
- Cache `:memory_store`
- Mailer configurado vía SMTP

### Tests
- `spec/models`, `spec/controllers`, `spec/features`, `spec/services`
- Factories con `FactoryBot`

### Reglas de Oro
- No usar servicios si un modelo puede manejarlo
- No agregar complejidad sin dolor real
- Usar partials, evitar duplicación
- Mantener lógica fuera de vistas cuando sea posible

### Archivos .env
```bash
SECRET_KEY_BASE=...
MAIL_HOST=...
MAIL_USERNAME=...
MAIL_PASSWORD=...
```

### Producción
- SSL
- Cache `:memory_store`
- Mailer configurado vía SMTP

### Tests
- `spec/models`, `spec/controllers`, `spec/features`, `spec/services`
- Factories con `FactoryBot`

### Reglas de Oro
- No usar servicios si un modelo puede manejarlo
- No agregar complejidad sin dolor real
- Usar partials, evitar duplicación
- Mantener lógica fuera de vistas cuando sea posible

---

**Última actualización:** Unificada toda la documentación del sistema en un solo archivo, incluyendo la documentación de los partials compartidos para clientes.
